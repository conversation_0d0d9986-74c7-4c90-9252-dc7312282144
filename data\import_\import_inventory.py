import json
import ast
import uuid
from data.constants.constant import CURRENCY_CODES, INVENTORY_STATUS_CHOICES
from datetime import datetime
import traceback
from django.utils import timezone

from data.models import ShopTurboInventory, ShopTurboInventoryValueCustomField, ShopTurboInventoryNameCustomField, ShopTurboItems, InventoryWarehouse, TransferHistory, InventoryTransaction
from utils.date import parse_date
from utils.inventory import update_inventory_stock_price, create_inventory_transaction_helper, re_calculate_inventory_stock
from utils.utility import is_valid_uuid, is_valid_int


def _validate_inventory_data(data_dictionary, user, workspace):
    """
    Validate inventory data before processing.

    Args:
        data_dictionary: Dictionary containing inventory data to validate
        user: User object for language preference

    Returns:
        tuple: (is_valid, errors) where is_valid is a boolean and errors is a list of error messages
    """
    errors = []

    # Validate required fields
    if not data_dictionary.get('how_to_import'):
        if user.verification.language == 'ja':
            errors.append('インポートモードが指定されていません。')
        else:
            errors.append('Import mode is not specified.')
        return errors

    # Validate import mode
    valid_import_modes = ['create', 'update', 'create_and_update']
    if data_dictionary['how_to_import'] not in valid_import_modes:
        if user.verification.language == 'ja':
            errors.append('無効なインポートモード。有効なモード: ' +
                          ', '.join(valid_import_modes))
        else:
            errors.append('Invalid import mode. Valid modes: ' +
                          ', '.join(valid_import_modes))
        return errors

    # Validate status if provided
    if 'status' in data_dictionary.get('default_property', {}):
        valid_statuses = ['draft', 'active', 'paused', 'canceled', 'archived']
        status = data_dictionary['default_property']['status']
        if status not in valid_statuses:
            if user.verification.language == 'ja':
                errors.append(
                    f'無効なステータス: {status}。有効なステータス: ' + ', '.join(valid_statuses))
            else:
                errors.append(
                    f'Invalid status: {status}. Valid statuses: ' + ', '.join(valid_statuses))
            return errors

    # Validate key_property for update and create_and_update modes
    if data_dictionary['how_to_import'] in ['update', 'create_and_update']:
        if not data_dictionary.get('key_property'):
            if user.verification.language == 'ja':
                errors.append('更新モードには key_property が必要です。')
            else:
                errors.append('key_property is required for update mode.')
            return errors

        # Validate item_id format in key_property for update mode
        if data_dictionary['how_to_import'] in ['update', 'create_and_update'] and 'item_id' in data_dictionary['key_property']:
            item_id = data_dictionary['key_property']['item_id']
            if not item_id:
                if user.verification.language == 'ja':
                    errors.append('商品IDが指定されていません。')
                else:
                    errors.append('Item ID is not specified.')
                return errors

            # Validate that item_id is a valid integer before querying
            if not is_valid_int(item_id):
                if user.verification.language == 'ja':
                    errors.append(f'商品ID {item_id} は数値である必要があります。')
                else:
                    errors.append(
                        f'Item ID {item_id} must be a valid integer.')
                return errors

            try:
                ShopTurboItems.objects.get(item_id=item_id)
            except ShopTurboItems.DoesNotExist:
                if user.verification.language == 'ja':
                    errors.append(f'商品ID {item_id} は存在しません。')
                else:
                    errors.append(f'Item ID {item_id} does not exist.')
                return errors

    # Validate item_id in default_property if it exists
    if 'default_property' in data_dictionary and 'item_id' in data_dictionary['default_property']:
        item_id = data_dictionary['default_property']['item_id']

        if (data_dictionary.get('property_object_relation_key_field') and
                data_dictionary['property_object_relation_key_field'].get('item_id')):
            custom_field_id = data_dictionary['property_object_relation_key_field']['item_id']
            if is_valid_uuid(custom_field_id):
                # This is a custom field relation
                if item_id and not ShopTurboItems.objects.filter(
                        status='active',
                        shopturbo_item_custom_field_relations__field_name__id=data_dictionary[
                            'property_object_relation_key_field']['item_id'],
                        shopturbo_item_custom_field_relations__value__iexact=str(item_id).lower().replace(' ', '')).first():
                    if user.verification.language == 'ja':
                        errors.append(f'ID {item_id} の商品が見つかりません。')
                    else:
                        errors.append(f'Item with ID {item_id} not found.')
                    return errors
            else:
                # This should be a direct item_id lookup
                if item_id:
                    # Validate that item_id is a valid integer before querying
                    if not is_valid_int(item_id):
                        if user.verification.language == 'ja':
                            errors.append(f'商品ID {item_id} は数値である必要があります。')
                        else:
                            errors.append(
                                f'Item ID {item_id} must be a valid integer.')
                        return errors

                    if not ShopTurboItems.objects.filter(
                        workspace=workspace,
                        item_id=item_id,
                        status='active'
                    ).exists():
                        if user.verification.language == 'ja':
                            errors.append(f'ID {item_id} の商品が見つかりません。')
                        else:
                            errors.append(f'Item with ID {item_id} not found.')
                        return errors
        else:
            # Direct item_id lookup
            if item_id:
                # Validate that item_id is a valid integer before querying
                if not is_valid_int(item_id):
                    if user.verification.language == 'ja':
                        errors.append(f'商品ID {item_id} は数値である必要があります。')
                    else:
                        errors.append(
                            f'Item ID {item_id} must be a valid integer.')
                    return errors

                if not ShopTurboItems.objects.filter(
                    workspace=workspace,
                    item_id=item_id,
                    status='active'
                ).exists():
                    if user.verification.language == 'ja':
                        errors.append(f'ID {item_id} の商品が見つかりません。')
                    else:
                        errors.append(f'Item with ID {item_id} not found.')
                    return errors

    if 'default_property' in data_dictionary and 'initial_value' in data_dictionary['default_property']:
        try:
            int(data_dictionary['default_property']['initial_value'])
        except ValueError:
            if user.verification.language == 'ja':
                errors.append('  initial_value  ')
            else:
                errors.append('initial_value should be an integer.')
            return errors

    if 'default_property' in data_dictionary and 'id_iw' in data_dictionary['default_property']:
        location_id = data_dictionary['default_property']['id_iw']

        if location_id:  # Only validate if id_iw is not empty
            try:
                location_id = int(location_id)
                # Verify the location exists in the workspace
                if not InventoryWarehouse.objects.filter(
                    id_iw=location_id,
                    workspace=workspace
                ).exists():
                    if user.verification.language == 'ja':
                        errors.append(f'ロケーションID {location_id} は存在しません。')
                    else:
                        errors.append(
                            f'Location with ID {location_id} does not exist.')
                    return errors
            except (ValueError, TypeError):
                if user.verification.language == 'ja':
                    errors.append('ロケーションIDは数値である必要があります。')
                else:
                    errors.append('Location ID must be a number.')
                return errors

    # Validate custom field if present
    for k, v in data_dictionary.get('key_property', {}).items():
        # Validate custom field value is provided
        if not v:
            if user.verification.language == 'ja':
                errors.append(
                    f'キー プロパティの値が指定されていません。')
            else:
                errors.append(
                    f'Value for Key property is not specified.')
            return errors
        if is_valid_uuid(k):
            # Validate custom field exists
            custom_field = ShopTurboInventoryNameCustomField.objects.filter(
                id=k, workspace=workspace
            ).first()

            if not custom_field:
                if user.verification.language == 'ja':
                    errors.append(f'カスタムフィールド {k} は存在しません。')
                else:
                    errors.append(f'Custom field {k} does not exist.')
                return errors

            if custom_field.type not in ['text', 'text-area', 'number']:
                if user.verification.language == 'ja':
                    errors.append(
                        f'カスタム プロパティ {custom_property} は、テキスト、テキスト エリア、数値型である必要があります。')
                else:
                    errors.append(
                        f'Custom property {custom_property} should be text, text-area, number type.')
                return errors

    # check currency if it's in the CURRENCY_CODES
    if 'default_property' in data_dictionary and 'currency' in data_dictionary['default_property']:
        currency = data_dictionary['default_property']['currency']
        if currency not in CURRENCY_CODES:
            if user.verification.language == 'ja':
                errors.append(f'通貨 {currency} は存在しません。')
            else:
                errors.append(f'Currency {currency} does not exist.')
            return errors

    # check inventory status if it's in the INVENTORY_STATUS_CHOICES
    if 'default_property' in data_dictionary and 'inventory_status' in data_dictionary['default_property']:
        inventory_status = data_dictionary['default_property']['inventory_status']
        if inventory_status.lower() not in [val[0].lower() for val in INVENTORY_STATUS_CHOICES]:
            if user.verification.language == 'ja':
                errors.append(f'在庫状態 {inventory_status} は存在しません。')
            else:
                errors.append(
                    f'Inventory status {inventory_status} does not exist.')
            return errors

    # check unit price if it's a number
    if 'default_property' in data_dictionary and 'unit_price' in data_dictionary['default_property']:
        unit_price = data_dictionary['default_property']['unit_price']
        if isinstance(unit_price, str):
            try:
                unit_price = float(unit_price.replace(',', ''))
            except ValueError:
                if user.verification.language == 'ja':
                    errors.append(f'単価 {unit_price} は数値である必要があります。')
                else:
                    errors.append(f'Unit price {unit_price} must be a number.')
                return errors
        if not isinstance(unit_price, (int, float)):
            if user.verification.language == 'ja':
                errors.append(f'単価 {unit_price} は数値である必要があります。')
            else:
                errors.append(f'Unit price {unit_price} must be a number.')
            return errors

    # check also date
    if 'default_property' in data_dictionary and 'date' in data_dictionary['default_property']:
        date = data_dictionary['default_property']['date']
        if not isinstance(date, (int, float)):
            if user.verification.language == 'ja':
                errors.append(f'日付 {date} は数値である必要があります。')
            else:
                errors.append(f'Date {date} must be a number.')
            return errors

        try:
            # The date string format should be in one of these formats:
            # YYYY-MM-DD or MM/DD/YYYY or YYYY年MM月DD日
            datetime.strptime(str(date), '%Y-%m-%d')
        except:
            try:
                datetime.strptime(str(date), '%m/%d/%Y')
            except:
                try:
                    datetime.strptime(str(date), '%Y年%m月%d日')
                except:
                    if user.verification.language == 'ja':
                        errors.append(
                            f'日付 {date} はYYYY-MM-DD形式またはMM/DD/YYYY形式またはYYYY年MM月DD日形式である必要があります。')
                    else:
                        errors.append(
                            f'Date {date} must be in YYYY-MM-DD or MM/DD/YYYY or YYYY年MM月DD日 format.')
                    return errors

    custom_property = data_dictionary.get('custom_property', {})
    for key, value in custom_property.items():
        if not is_valid_uuid(key):
            if user.verification.language == 'ja':
                errors.append(f'カスタムフィールド {key} の値 {value} はUUID形式である必要があります。')
            else:
                errors.append(
                    f'Custom field {key} value {value} must be a uuid string.')
            return errors

        inventory_property = ShopTurboInventoryNameCustomField.objects.filter(
            id=key, workspace=workspace).first()
        if not inventory_property:
            if user.verification.language == 'ja':
                errors.append(f'カスタムフィールド {key} は存在しません。')
            else:
                errors.append(f'Custom field {key} does not exist.')
            return errors

        if value is None or value == '':
            if inventory_property.required_field:
                if user.verification.language == 'ja':
                    errors.append(
                        f'必須のカスタムフィールド {inventory_property.name} の値が指定されていません。')
                else:
                    errors.append(
                        f'Required custom field {inventory_property.name} value is not specified.')
                return errors
            else:
                continue

        # Validate value based on field type
        field_type = inventory_property.type
        try:
            if field_type in ['number']:
                # Check if value is a valid number
                if isinstance(value, str):
                    value = value.replace(',', '')
                float(value)
            elif field_type in ['date', 'date_time']:
                try:
                    parsed_time = parse_date(
                        value, workspace.timezone)
                    if not isinstance(parsed_time, datetime):
                        raise ValueError()
                except Exception as e:
                    print(e)
                    if user.verification.language == 'ja':
                        errors.append(
                            f'日付 {value} は有効な日付ではありません。')
                    else:
                        errors.append(
                            f'Date {value} is not in valid format.')
                    return errors
            elif field_type == 'boolean':
                # Check if value is a valid boolean (true/false, 1/0)
                if str(value).lower() not in ['true', 'false', '1', '0']:
                    if user.verification.language == 'ja':
                        errors.append(
                            f'値 {value} はtrue/false、1/0である必要があります。')
                    else:
                        errors.append(
                            f'Value {value} must be true/false, 1/0.')
                    return errors
            # elif field_type in ['contact', 'company']:
            #     # For contact/company fields, value should be a valid ID
            #     if not is_valid_uuid(value):
            #         raise ValueError()
            elif 'choice' in field_type:
                choice_value = ast.literal_eval(
                    inventory_property.choice_value)
                choice_options = [choice['value'] for choice in choice_value]

                if value not in choice_options:
                    if user.verification.language == 'ja':
                        errors.append(
                            f'値 {value} は{inventory_property.name}の選択肢ではありません。')
                    else:
                        errors.append(
                            f'Value {value} is not a valid option for {inventory_property.name}.')
                    return errors
        except ValueError:
            if user.verification.language == 'ja':
                errors.append(
                    f'カスタムフィールド「{inventory_property.name}」の値「{value}」は{field_type}型として無効です。')
            else:
                errors.append(
                    f'Invalid value "{value}" for field "{inventory_property.name}" of type {field_type}.')
            return errors

    return []


def write_inventory(workspace, user, data_dictionary={}, transfer_history_id=None):
    inventory = None
    print('data_dictionary', data_dictionary)

    # Validate input data
    errors = _validate_inventory_data(
        data_dictionary, user, workspace)
    if errors:
        return None, errors

    try:
        transfer_history = None
        if transfer_history_id:
            try:
                transfer_history = TransferHistory.objects.get(
                    id=transfer_history_id)
            except:
                pass

        inventory = None
        # print("================================================================")
        # print(data_dictionary)
        item_id = None
        item = None
        if 'item_id' in data_dictionary['default_property']:
            item_id = data_dictionary['default_property'].pop('item_id')
            if data_dictionary['property_object_relation_key_field']:
                if is_valid_uuid(data_dictionary['property_object_relation_key_field']['item_id']):
                    item = ShopTurboItems.objects.filter(
                        status='active',
                        shopturbo_item_custom_field_relations__field_name__id=data_dictionary[
                            'property_object_relation_key_field']['item_id'],
                        shopturbo_item_custom_field_relations__value__iexact=str(item_id).lower().replace(' ', '')).first()
                else:
                    item = ShopTurboItems.objects.filter(
                        workspace=workspace, item_id=item_id, status='active').first()
            else:
                item = ShopTurboItems.objects.filter(
                    workspace=workspace, item_id=item_id, status='active').first()

        location_id = None
        is_created = False
        if 'id_iw' in data_dictionary['default_property']:
            location_id = data_dictionary['default_property'].pop('id_iw')
            if location_id:
                location_id = int(location_id)

        if data_dictionary['how_to_import'] == 'create':
            if 'inventory_id' in data_dictionary['default_property']:
                data_dictionary['default_property'].pop('inventory_id')

            # Prepare creation parameters
            create_params = data_dictionary['default_property'].copy()
            create_params['workspace'] = workspace
            if 'status' not in create_params:
                create_params['status'] = 'active'
            if 'date' not in create_params:
                create_params['date'] = timezone.now()

            if transfer_history and transfer_history.checkpoint_details.get('object_id'):
                # This process is already processed before.
                try:
                    inventory = ShopTurboInventory.objects.get(
                        id=transfer_history.checkpoint_details['object_id'], workspace=workspace)
                except ShopTurboInventory.DoesNotExist:
                    inventory = ShopTurboInventory.objects.create(
                        **create_params)
            else:
                inventory = ShopTurboInventory.objects.create(**create_params)

            if item:
                inventory.item.add(item)
                inventory.currency = item.currency

            is_created = True
            inventory.save(
                log_data={'user': user, 'status': 'create', 'workspace': workspace})

        elif data_dictionary['how_to_import'] == 'update':
            # Key property validation is already done in _validate_inventory_data
            if 'item_id' in data_dictionary['key_property']:
                data_dictionary['key_property']['item__item_id'] = data_dictionary['key_property'].pop(
                    'item_id')
            for k, v in data_dictionary['key_property'].items():
                if is_valid_uuid(k):
                    inventory = None
                    custom_prop_val = ShopTurboInventoryValueCustomField.objects.filter(
                        field_name__id=k, value__iexact=v, field_name__workspace=workspace).first()
                    if custom_prop_val:
                        inventory = custom_prop_val.inventory
                else:
                    # Handle inventory_id lookup with proper type conversion
                    key_property = data_dictionary['key_property'].copy()
                    if 'inventory_id' in key_property:
                        inventory_id_value = key_property['inventory_id']

                        # Try both string and integer formats for inventory_id
                        # First try with status filter
                        inventory = ShopTurboInventory.objects.filter(
                            inventory_id=inventory_id_value, workspace=workspace, status='active').first()

                        if not inventory:
                            # Try without status filter (for update mode, we should find any status)
                            inventory = ShopTurboInventory.objects.filter(
                                inventory_id=inventory_id_value, workspace=workspace).first()

                            if not inventory:
                                # Try converting to int if it's a string, or to string if it's an int
                                try:
                                    if isinstance(inventory_id_value, str):
                                        int_value = int(inventory_id_value)
                                        inventory = ShopTurboInventory.objects.filter(
                                            inventory_id=int_value, workspace=workspace).first()
                                    elif isinstance(inventory_id_value, int):
                                        # Try formatted string version
                                        str_value = f"{inventory_id_value:03d}"
                                        inventory = ShopTurboInventory.objects.filter(
                                            inventory_id=str_value, workspace=workspace).first()
                                except (ValueError, TypeError):
                                    print(
                                        '[Import inventory] [ERROR] Invalid inventory_id value: ' + str(inventory_id_value))
                    else:
                        inventory = ShopTurboInventory.objects.filter(
                            **key_property, workspace=workspace, status='active').first()
                break

            # Check if inventory was found in update mode
            if not inventory:
                if user.verification.language == 'ja':
                    errors.append('更新対象の在庫が見つかりません。')
                else:
                    errors.append('Inventory to update not found.')
                return None, errors

            if inventory and item:
                inventory.item.clear()
                inventory.item.add(item)

        elif data_dictionary['how_to_import'] == 'create_and_update':
            try:
                if 'item_id' in data_dictionary['key_property']:
                    data_dictionary['key_property']['item__item_id'] = data_dictionary['key_property'].pop(
                        'item_id')
                for k, v in data_dictionary['key_property'].items():
                    if is_valid_uuid(k):
                        inventory = None
                        custom_prop_val = ShopTurboInventoryValueCustomField.objects.filter(
                            field_name__id=k, value__iexact=v, field_name__workspace=workspace).first()
                        created = False
                        if custom_prop_val:
                            inventory = custom_prop_val.inventory
                        elif item:  # no existing custom prop but importing items data
                            inventory = ShopTurboInventory.objects.create(
                                workspace=workspace, status='active', date=timezone.now())
                            created = True
                            prop = ShopTurboInventoryNameCustomField.objects.get(
                                id=k, workspace=workspace)
                            if prop.type == 'number':
                                if isinstance(v, str):
                                    v = v.replace(',', '')
                                v = float(v)
                            ShopTurboInventoryValueCustomField.objects.create(
                                field_name=prop, value=v, inventory=inventory)
                    else:
                        try:
                            # Handle inventory_id lookup with proper type conversion
                            key_property = data_dictionary['key_property'].copy(
                            )
                            if 'inventory_id' in key_property:
                                inventory_id_value = key_property['inventory_id']
                                # Try both string and integer formats for inventory_id
                                inventory = ShopTurboInventory.objects.filter(
                                    inventory_id=inventory_id_value, workspace=workspace).first()
                                if not inventory:
                                    # Try converting to int if it's a string
                                    try:
                                        if isinstance(inventory_id_value, str):
                                            int_value = int(inventory_id_value)
                                            inventory = ShopTurboInventory.objects.filter(
                                                inventory_id=int_value, workspace=workspace).first()
                                        elif isinstance(inventory_id_value, int):
                                            # Try formatted string version
                                            str_value = f"{inventory_id_value:03d}"
                                            inventory = ShopTurboInventory.objects.filter(
                                                inventory_id=str_value, workspace=workspace).first()
                                    except (ValueError, TypeError):
                                        print(
                                            '[Import inventory] [ERROR] Invalid inventory_id value: ' + str(inventory_id_value))
                                if not inventory:
                                    if user.verification.language == 'ja':
                                        errors.append(
                                            f'キープロパティ inventory_id {inventory_id_value} で在庫が見つかりません')
                                    else:
                                        errors.append(
                                            f'Inventory not found by key_property inventory_id {inventory_id_value}')
                            else:
                                inventory = ShopTurboInventory.objects.get(
                                    **key_property, workspace=workspace)
                        except ShopTurboInventory.DoesNotExist:
                            if item:
                                # For create_and_update, create with default properties, not key properties
                                create_params = data_dictionary['default_property'].copy(
                                )
                                create_params['workspace'] = workspace
                                if 'status' not in create_params:
                                    create_params['status'] = 'active'
                                if 'date' not in create_params:
                                    create_params['date'] = timezone.now()
                                inventory = ShopTurboInventory.objects.create(
                                    **create_params)
                                created = True
                            else:
                                if user.verification.language == 'ja':
                                    errors.append('アイテムを指定して在庫を作成する必要があります。')
                                else:
                                    errors.append(
                                        'Item must be specified to create inventory.')
                        inventory.status = 'active'
                        inventory.date = timezone.now()
                        inventory.save()
                    break

                if created:
                    is_created = created
                    inventory.item.add(item)
                    inventory.currency = item.currency
                    inventory.save(
                        log_data={'user': user, 'status': 'create', 'workspace': workspace})
            except ShopTurboInventory.MultipleObjectsReturned:
                inventory = ShopTurboInventory.objects.filter(
                    **data_dictionary['key_property'], workspace=workspace).first()
            except:
                if 'inventory_id' in data_dictionary['default_property']:
                    data_dictionary['default_property'].pop('inventory_id')
                if item:
                    inventory = ShopTurboInventory.objects.create(
                        **data_dictionary['default_property'], workspace=workspace)
                    inventory.item.add(item)
                    inventory.save(
                        log_data={'user': user, 'status': 'create', 'workspace': workspace})
                else:
                    if user.verification.language == 'ja':
                        errors.append('アイテムを指定して在庫を作成する必要があります。')
                    else:
                        errors.append(
                            'Item must be specified to create inventory.')

        # Final check - this should not happen with proper validation above
        if not inventory:
            if user.verification.language == 'ja':
                # if method is update
                if data_dictionary['how_to_import'] == 'update':
                    errors.append('在庫の更新に失敗しました。')
                elif data_dictionary['how_to_import'] == 'create':
                    errors.append('在庫の作成に失敗しました。')
                else:
                    errors.append('在庫の作成または更新に失敗しました。')
            else:
                if data_dictionary['how_to_import'] == 'update':
                    errors.append('Failed to update inventory.')
                elif data_dictionary['how_to_import'] == 'create':
                    errors.append('Failed to create inventory.')
                else:
                    errors.append('Failed to create or update inventory.')
            return None, errors

        if transfer_history:
            imported_ids = transfer_history.checkpoint_details.get(
                'imported', [])
            imported_ids.append(inventory.inventory_id)
            transfer_history.checkpoint_details['imported'] = imported_ids
            transfer_history.checkpoint_details['object_id'] = str(
                inventory.id)
            transfer_history.save()

        inventory_id = inventory.id
        # Process Adding base Value
        for default_property_key in data_dictionary['default_property']:
            setattr(inventory, default_property_key,
                    data_dictionary['default_property'][default_property_key])

        initial_value = None
        if 'initial_value' in data_dictionary['default_property']:
            initial_value = data_dictionary['default_property']["initial_value"]

        # Save the changes before processing initial_value
        inventory.save()

        # Reload inventory to get fresh state for initial_value processing
        inventory = ShopTurboInventory.objects.get(id=inventory_id)
        re_calculate_stock = False
        if initial_value is not None:
            inventory.initial_value = initial_value
            initial_transaction = InventoryTransaction.objects.filter(
                inventory=inventory).order_by('transaction_date', 'created_at')
            if initial_transaction:
                initial_transaction = initial_transaction[0]
                if initial_transaction.amount != initial_value:
                    re_calculate_stock = True
                    initial_transaction.amount = initial_value
                    initial_transaction.transaction_amount = initial_value
                    initial_transaction.save()

        if location_id:
            location = InventoryWarehouse.objects.filter(
                id_iw=location_id, workspace=workspace).first()
            if location:
                inventory.warehouse = location
        inventory.save()

        if re_calculate_stock and not is_created:
            update_inventory_stock_price(inventory)
            inventory.refresh_from_db()
            re_calculate_inventory_stock(inventory)

        inventory.refresh_from_db()
        # Process Custom Property
        for custom_property in data_dictionary['custom_property']:
            inventory_property = ShopTurboInventoryNameCustomField.objects.get(
                id=custom_property, workspace=workspace)
            custom_field_value, _ = ShopTurboInventoryValueCustomField.objects.get_or_create(
                field_name=inventory_property, inventory=inventory)
            if inventory_property.type == 'date' or inventory_property.type == 'date_time':
                parsed_time = parse_date(
                    data_dictionary['custom_property'][custom_property], workspace.timezone)
                if isinstance(parsed_time, datetime):
                    custom_field_value.value_time = parsed_time
                    custom_field_value.value = str(parsed_time)
            elif inventory_property.type == 'tag':
                # Format tag values as a JSON array of objects with 'value' property
                tag_values = [v.strip() for v in str(
                    data_dictionary['custom_property'][custom_property]).split(',')]
                custom_field_value.value = json.dumps(
                    [{'value': v} for v in tag_values])
            else:
                custom_field_value.value = str(
                    data_dictionary['custom_property'][custom_property])
            custom_field_value.save()

        if is_created:
            create_inventory_transaction_helper(inventory, user, None)

    except Exception as e:
        traceback.print_exc()
        if data_dictionary['how_to_import'] == 'create':
            if inventory:
                if transfer_history:
                    imported_ids = transfer_history.checkpoint_details.get(
                        'imported', [])
                    if inventory.inventory_id in imported_ids:
                        imported_ids.remove(inventory.inventory_id)
                        transfer_history.checkpoint_details['imported'] = imported_ids
                        transfer_history.save()
                inventory.delete()

        # Return proper error message with language support
        if user.verification.language == 'ja':
            errors.append(f'在庫のインポート中にエラーが発生しました: {str(e)}')
        else:
            errors.append(f'Error occurred during inventory import: {str(e)}')
        return None, errors

    return inventory, errors
