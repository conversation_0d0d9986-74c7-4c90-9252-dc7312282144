{% load i18n %}
{% load hosts %}
{% load custom_tags %}
{% get_current_language as LANGUAGE_CODE %}



{% if "order" == import_as %}

    <div>
        <select name="sanka-properties" class="bg-white form-select form-select-solid border h-40px select2-this-order-{{header}}" 
            {% if LANGUAGE_CODE == 'ja'%}
            data-placeholder="注文ヘッダーの選択"
            {% else %}
            data-placeholder="Select Order Header"
            {% endif %}

            {% comment %} Checking Object Relation {% endcomment %}
            hx-target="#choose-object-relation-field-order"
            hx-get="{% host_url 'csv_upload' host 'app' %}"
            hx-trigger="htmx-change delay:0.5s,load delay:0.5s"
            hx-vals='{"section": "sanka-property-object-relation", "import_data_type":"commerce_orders","mapping_storage_id":"{{mapping_storage_id}}"}'
            hx-swap='beforeend'
            onchange="customSubmit(),htmx.trigger(this, 'htmx-change'),validateMappingProperties()"
            
            >  
            {% with header_index=forloop.counter0 %}
                {% for column in columns %}
                <option value='{{column}}' {% if mapping_sanka_property_storage ==  column %}selected{% endif %}>
                    {% with args=column|add:'|'|add:'commerce_orders' %} 
                        {% with column_display=args|get_column_display:request %}
                            {{column_display.name}}
                        {% endwith %}
                    {% endwith %}
                </option>   
                {% endfor %} 
            {% endwith %}
        </select>
    </div>

    <script>
        $(document).ready(function() {
            $('.select2-this-order-{{header}}').each(function() {
                let selectElement = $(this).closest('select').get(0);
                if (selectElement) {
                    if (selectElement.value == 'item_id' || selectElement.value == 'contact_id' || selectElement.value == 'company_id'){
                        selectElement && selectElement.dispatchEvent(new Event('htmx-change'));
                    }
                }
            });
            
            checkDuplicateRelations();
            checking_object_relation();
            
        });
    </script>
    
    

{% elif "item" == import_as %}
    <div>
        <select name="sanka-properties" class="bg-white form-select form-select-solid border h-40px select2-this-item-{{header}}" 
            {% if LANGUAGE_CODE == 'ja'%}
            data-placeholder="商品ヘッダーを選択"
            {% else %}
            data-placeholder="Select Item Header"
            {% endif %}


            onchange="customSubmit()"
            >  
            {% with header_index=forloop.counter0 %}
                {% for column in columns %}
                <option value='{{column}}' {% if mapping_sanka_property_storage ==  column %}selected{% endif %}>
                    {% if '|amount' in column %}
                        {% with args=column|cut:'|amount'|add:'|commerce_items'|add:'|amount' %} 
                            {% with column_display=args|get_column_display:request %}
                                {{column_display.name}}
                            {% endwith %}
                        {% endwith %}
                    {% else %}
                        {% with args=column|add:'|commerce_items' %} 
                            {% with column_display=args|get_column_display:request %}
                                {{column_display.name}}
                            {% endwith %}
                        {% endwith %}
                    {% endif %}
                </option>   
                {% endfor %} 
            {% endwith %}
        </select>
    </div>
    
{% elif "contact" == import_as %}
    <div>
        <select name="sanka-properties" class="bg-white form-select form-select-solid border h-40px select2-this-contact-{{header}}" 
            {% if LANGUAGE_CODE == 'ja'%}
            data-placeholder="連絡先ヘッダーの選択"
            {% else %}
            data-placeholder="Select Contact Header"
            {% endif %}
            onchange="customSubmit()"
            >  
            {% with header_index=forloop.counter0 %}
                {% for contacts_column in columns %}
                    <option value='{{contacts_column}}' {% if mapping_sanka_property_storage ==  contacts_column %}selected{% endif %}>
                        {% if not contacts_column|is_uuid %}
                            {{contacts_column|display_column_contacts:request}}
                        {% else %}
                            {% with custom_column=contacts_column|search_custom_field_object_contacts:request %}
                                {{custom_column.name}}
                            {% endwith %} 
                        {% endif %}
                    </option>  
                {% endfor %} 
            {% endwith %}
        </select>
    </div>
    
{% elif constant.TYPE_OBJECT_COMPANY == import_as %}
    <div>
        <select name="sanka-properties" class="bg-white form-select form-select-solid border h-40px select2-this-company-{{header}}" 
            {% if LANGUAGE_CODE == 'ja'%}
            data-placeholder="企業ヘッダーの選択"
            {% else %}
            data-placeholder="Select Company Header"
            {% endif %}
            onchange="customSubmit()"
            >  
            {% with header_index=forloop.counter0 %}
                {% for company_column in columns %}
                <option value='{{company_column}}' {% if mapping_sanka_property_storage ==  company_column %}selected{% endif %}>
                    {% if 'hierarchy' in company_column %}
                        {{company_column|get_hierarchy_display:request}}
                    {% elif not company_column|is_uuid %}
                       {{company_column|display_column_company:request}}
                    {% else %}
                        {% with custom_column=company_column|search_custom_field_object_company:request %}
                            {{custom_column.name}}
                        {% endwith %} 
                    {% endif %}
                </option>   
                {% endfor %} 
            {% endwith %}
        </select>
    </div>
    

{% elif constant.TYPE_OBJECT_INVENTORY == import_as %}

    <div>
        <select name="sanka-properties" class="bg-white form-select form-select-solid border h-40px select2-this-{{constant.TYPE_OBJECT_INVENTORY}}-{{header}}" 
            {% if LANGUAGE_CODE == 'ja'%}
            data-placeholder="在庫ヘッダーを選択"
            {% else %}
            data-placeholder="Select Inventory Header"
            {% endif %}

            hx-target="#choose-object-relation-field-{{constant.TYPE_OBJECT_INVENTORY}}"
            hx-get="{% host_url 'csv_upload' host 'app' %}"
            hx-trigger="htmx-change delay:0.5s,load delay:0.5s"
            hx-vals='{"section": "sanka-property-object-relation", "import_data_type":"{{constant.TYPE_OBJECT_INVENTORY}}","mapping_storage_id":"{{mapping_storage_id}}"}'
            hx-swap='beforeend'
            onchange="customSubmit(),htmx.trigger(this, 'htmx-change'),document.dispatchEvent(new Event('validateMappingProperties'))"
            >  
            {% with header_index=forloop.counter0 %}
                {% for column in columns %}
                <option value='{{column}}' {% if mapping_sanka_property_storage ==  column %}selected{% endif %}>
                    {% if not column|is_uuid %}
                        {% if 'item_id' in column %}
                            {% if LANGUAGE_CODE == 'ja'%}商品{% else %}Item{% endif %}
                        {% else %}
                            {{column|display_column_inventory:request}}
                        {% endif %}
                    {% else %}
                        {% with custom_column=column|search_custom_field_object_inventory:request %}
                            {{custom_column.name}} 
                        {% endwith %} 
                    {% endif %}
                </option>   
                {% endfor %} 
            {% endwith %}
        </select>
    </div>

    <script>
        $(document).ready(function() {
            $('.select2-this-{{constant.TYPE_OBJECT_INVENTORY}}-{{header}}').each(function() {
                let selectElement = $(this).closest('select').get(0);
                if (selectElement) {
                    if (selectElement.value == 'associate#{{constant.TYPE_OBJECT_ITEM}}'){
                        selectElement && selectElement.dispatchEvent(new Event('htmx-change'));
                    }
                }
            });

            checkDuplicateRelations();
            checking_object_relation();
            {% comment %} document.dispatchEvent(new Event('validateMappingProperties')) {% endcomment %}
        });
    </script>
    
    
{% elif "billing" == import_as %}
    <div>
        <select name="sanka-properties" class="bg-white form-select form-select-solid border h-40px select2-this-billing-{{header}}" 
            {% if LANGUAGE_CODE == 'ja'%}
            data-placeholder="請求ヘッダーを選択"
            {% else %}
            data-placeholder="Select Billing Header"
            {% endif %}

            hx-target="#choose-object-relation-field-billing"
            hx-get="{% host_url 'csv_upload' host 'app' %}"
            hx-trigger="htmx-change delay:0.5s,load delay:0.5s"
            hx-vals='{"section": "sanka-property-object-relation", "import_data_type":"bill","mapping_storage_id":"{{mapping_storage_id}}"}'
            hx-swap='beforeend'
            onchange="customSubmit(),htmx.trigger(this, 'htmx-change')"
            >  
            {% with header_index=forloop.counter0 %}
                {% for column in columns %}
                <option value='{{column}}' {% if mapping_sanka_property_storage ==  column %}selected{% endif %}>
                    {% if not column|is_uuid %}
                        {{column|display_column_billing:request}}
                    {% else %}
                        {% with custom_column=column|search_custom_field_object_bills:request %}
                            {{custom_column.name}}
                        {% endwith %} 
                    {% endif %}
                </option>   
                {% endfor %} 
            {% endwith %}
        </select>
    </div>

    <script>
        $(document).ready(function() {
            $('.select2-this-billing-{{header}}').each(function() {
                let selectElement = $(this).closest('select').get(0);
                if (selectElement) {
                    if (selectElement.value == 'contact_id' || selectElement.value == 'company_id'){
                        selectElement && selectElement.dispatchEvent(new Event('htmx-change'));
                    }
                }
            });
            
            checkDuplicateRelations();
            checking_object_relation();
            
        });
    </script>

{% elif constant.TYPE_OBJECT_CASE == import_as %}
    <div>
        <select name="sanka-properties" class="bg-white form-select form-select-solid border h-40px select2-this-customer_case-{{header}}" 
            {% if LANGUAGE_CODE == 'ja'%}
            data-placeholder="案件ヘッダーを選択"
            {% else %}
            data-placeholder="Select Case Header"
            {% endif %}

            hx-target="#choose-object-relation-field-customer_case"
            hx-get="{% host_url 'csv_upload' host 'app' %}"
            hx-trigger="htmx-change delay:0.5s,load delay:0.5s"
            hx-vals='{"section": "sanka-property-object-relation", "import_data_type":"customer_case","mapping_storage_id":"{{mapping_storage_id}}"}'
            hx-swap='beforeend'
            onchange="customSubmit(),htmx.trigger(this, 'htmx-change')"
            >  
            {% with header_index=forloop.counter0 %}
                {% for column in columns %}
                <option value='{{column}}' {% if mapping_sanka_property_storage ==  column %}selected{% endif %}>
                    {% if not column|is_uuid %}
                        {{column|display_column_deals:request}}
                    {% else %}
                        {% with custom_column=column|search_custom_field_object_deals:request %}
                            {{custom_column.name}}
                        {% endwith %} 
                    {% endif %}
                </option>   
                {% endfor %} 
            {% endwith %}
        </select>
    </div>

    <script>
        $(document).ready(function() {
            $('.select2-this-customer_case-{{header}}').each(function() {
                let selectElement = $(this).closest('select').get(0);
                if (selectElement) {
                    if (selectElement.value == 'item_id' || selectElement.value == 'contact_id' || selectElement.value == 'company_id'){
                        selectElement && selectElement.dispatchEvent(new Event('htmx-change'));
                    }
                }
            });
            
            checkDuplicateRelations();
            checking_object_relation();
            
        });
    </script>

{% elif constant.TYPE_OBJECT_INVENTORY_TRANSACTION == import_as %}
    <div>
        <select name="sanka-properties" class="bg-white form-select form-select-solid border h-40px select2-this-commerce_inventory_transaction-{{header}}" 
            {% if LANGUAGE_CODE == 'ja'%}
            data-placeholder="入出庫ヘッダーを選択"
            {% else %}
            data-placeholder="Select Inventory Transaction Header"
            {% endif %}
            onchange="customSubmit()"
            >  
            {% with header_index=forloop.counter0 %}
                {% for column in columns %}
                <option value='{{column}}' {% if mapping_sanka_property_storage ==  column %}selected{% endif %}>
                    {% if not column|is_uuid %}
                        {{column|display_column_inventory_transaction:request}}
                    {% else %}
                        {% with custom_column=column|search_custom_field_object_inventory_transaction:request %}
                            {{custom_column.name}}
                        {% endwith %} 
                    {% endif %}
                </option>   
                {% endfor %} 
            {% endwith %}
        </select>
    </div>


{% elif constant.TYPE_OBJECT_INVENTORY_WAREHOUSE == import_as %}
    <div>
        <select name="sanka-properties" class="bg-white form-select form-select-solid border h-40px select2-this-commerce_inventory_warehouse-{{header}}" 
            {% if LANGUAGE_CODE == 'ja'%}
            data-placeholder="場所ヘッダーを選択"
            {% else %}
            data-placeholder="Select Location Header"
            {% endif %}
            onchange="customSubmit()"
            >  
            {% with header_index=forloop.counter0 %}
                {% for column in columns %}
                <option value='{{column}}' {% if mapping_sanka_property_storage ==  column %}selected{% endif %}>
                    {% if not column|is_uuid %}
                        {{column|display_column_inventory_warehouse:request}}
                    {% else %}
                        {% with custom_column=column|search_custom_field_object_inventory_warehouse:request %}
                            {{custom_column.name}}
                        {% endwith %} 
                    {% endif %}
                </option>   
                {% endfor %} 
            {% endwith %}
        </select>  
    </div>
{% elif constant.TYPE_OBJECT_ESTIMATE == import_as %}
    <div>
        <select name="sanka-properties" class="bg-white form-select form-select-solid border h-40px select2-this-estimates-{{header}}" 
            {% if LANGUAGE_CODE == 'ja'%}
            data-placeholder="見積書ヘッダーを選択"
            {% else %}
            data-placeholder="Select Estimate Header"
            {% endif %}
            onchange="customSubmit()"
            >  
            {% with header_index=forloop.counter0 %}
                {% for column in columns %}
                <option value='{{column}}' {% if mapping_sanka_property_storage ==  column %}selected{% endif %}>
                    {% if not column|is_uuid %}
                        {{column|display_column_estimate:request}}
                    {% else %}
                        {% with custom_column=column|search_custom_field_object_estimate:request %}
                            {{custom_column.name}}
                        {% endwith %} 
                    {% endif %}
                </option>   
                {% endfor %} 
            {% endwith %}
        </select>
    </div>

{% elif constant.TYPE_OBJECT_INVOICE == import_as %}
    <div>
        <select name="sanka-properties" class="bg-white form-select form-select-solid border h-40px select2-this-invoices-{{header}}" 
            {% if LANGUAGE_CODE == 'ja'%}
            data-placeholder="請求書ヘッダーを選択"
            {% else %}
            data-placeholder="Select Invoice Header"
            {% endif %}
            onchange="customSubmit()"
            >  
            {% with header_index=forloop.counter0 %}
                {% for column in columns %}
                <option value='{{column}}' {% if mapping_sanka_property_storage ==  column %}selected{% endif %}>
                    {% if not column|is_uuid %}
                        {{column|display_column_invoice:request}}
                    {% else %}
                        {% with custom_column=column|search_custom_field_object_invoice:request %}
                            {{custom_column.name}}
                        {% endwith %} 
                    {% endif %}
                </option>   
                {% endfor %} 
            {% endwith %}
        </select>
    </div>
{% elif constant.TYPE_OBJECT_DELIVERY_NOTE == import_as %}
    <div>
        <select name="sanka-properties" class="bg-white form-select form-select-solid border h-40px select2-this-delivery_slips-{{header}}" 
            {% if LANGUAGE_CODE == 'ja'%}
            data-placeholder="配送伝票ヘッダーを選択"
            {% else %}
            data-placeholder="Select Delivery Note Header"
            {% endif %}
            onchange="customSubmit()"
            >  
            {% with header_index=forloop.counter0 %}
                {% for column in columns %}
                <option value='{{column}}' {% if mapping_sanka_property_storage ==  column %}selected{% endif %}>
                    {% if not column|is_uuid %}
                        {{column|display_column_delivery_slip:request}}
                    {% else %}
                        {% with custom_column=column|search_custom_field_object_delivery_slip:request %}
                            {{custom_column.name}}
                        {% endwith %} 
                    {% endif %}
                </option>   
                {% endfor %} 
            {% endwith %}
        </select>
    </div>
{% elif constant.TYPE_OBJECT_RECEIPT == import_as %}
    <div>
        <select name="sanka-properties" class="bg-white form-select form-select-solid border h-40px select2-this-receipts-{{header}}" 
            {% if LANGUAGE_CODE == 'ja'%}
            data-placeholder="入金レコードのヘッダーを選択します"
            {% else %}
            data-placeholder="Select Payment Record Header"
            {% endif %}
            onchange="customSubmit()"
            >  
            {% with header_index=forloop.counter0 %}
                {% for column in columns %}
                <option value='{{column}}' {% if mapping_sanka_property_storage ==  column %}selected{% endif %}>
                    {% if not column|is_uuid %}
                        {{column|display_column_receipt:request}}
                    {% else %}
                        {% with custom_column=column|search_custom_field_object_receipt:request %}
                            {{custom_column.name}}
                        {% endwith %} 
                    {% endif %}
                </option>   
                {% endfor %} 
            {% endwith %}
        </select>
    </div>
{% elif constant.TYPE_OBJECT_SLIP == import_as %}
    <div>
        <select name="sanka-properties" class="bg-white form-select form-select-solid border h-40px select2-this-slips-{{header}}" 
            {% if LANGUAGE_CODE == 'ja'%}
            data-placeholder="スリップヘッダーを選択"
            {% else %}
            data-placeholder="Select Slip Header"
            {% endif %}
            onchange="customSubmit()"
            >  
            {% with header_index=forloop.counter0 %}
                {% for column in columns %}
                <option value='{{column}}' {% if mapping_sanka_property_storage ==  column %}selected{% endif %}>
                    {% if not column|is_uuid %}
                        {{column|display_column_slip:request}}
                    {% else %}
                        {% with custom_column=column|search_custom_field_object_slip:request %}
                            {{custom_column.name}}
                        {% endwith %} 
                    {% endif %}
                </option>   
                {% endfor %} 
            {% endwith %}
        </select>
    </div>

{% elif constant.TYPE_OBJECT_SUBSCRIPTION == import_as %}
    <div>
        <select name="sanka-properties" class="bg-white form-select form-select-solid border h-40px select2-this-commerce_subscription-{{header}}" 
            {% if LANGUAGE_CODE == 'ja'%}
            data-placeholder="サブスクリプションヘッダーを選択"
            {% else %}
            data-placeholder="Select Subscriptions Header"
            {% endif %}

            hx-target="#choose-object-relation-field-commerce_subscription"
            hx-get="{% host_url 'csv_upload' host 'app' %}"
            hx-trigger="htmx-change delay:0.5s,load delay:0.5s"
            hx-vals='{"section": "sanka-property-object-relation", "import_data_type":"commerce_subscription","mapping_storage_id":"{{mapping_storage_id}}"}'
            hx-swap='beforeend'
            onchange="customSubmit(),htmx.trigger(this, 'htmx-change')"

            >  
            {% with header_index=forloop.counter0 %}
                {% for column in columns %}
                <option value='{{column}}' {% if mapping_sanka_property_storage ==  column %}selected{% endif %}>
                    {% if not column|is_uuid %}
                        {{column|display_column_subscriptions:request}}
                    {% else %}
                        {% with custom_column=column|search_custom_field_object_subscriptions:request %}
                            {{custom_column.name}}
                        {% endwith %} 
                    {% endif %}
                </option>   
                {% endfor %} 
            {% endwith %}
        </select>
    </div>

    <script>
        $(document).ready(function() {
            $('.select2-this-commerce_subscription-{{header}}').each(function() {
                let selectElement = $(this).closest('select').get(0);
                if (selectElement) {
                    if (selectElement.value == 'item_id' || selectElement.value == 'contact_id' || selectElement.value == 'company_id'){
                        selectElement && selectElement.dispatchEvent(new Event('htmx-change'));
                    }
                }
            });
            
            checkDuplicateRelations();
            checking_object_relation();
            
        });
    </script>

{% elif "purchaseorder" == import_as %}

    <div>
        <select name="sanka-properties" class="bg-white form-select form-select-solid border h-40px select2-this-purchaseorder-{{header}}" 
            {% if LANGUAGE_CODE == 'ja'%}
            data-placeholder="サブスクリプションヘッダーを選択"
            {% else %}
            data-placeholder="Select Subscriptions Header"
            {% endif %}

            hx-target="#choose-object-relation-field-purchaseorder"
            hx-get="{% host_url 'csv_upload' host 'app' %}"
            hx-trigger="htmx-change delay:0.5s,load delay:0.5s"
            hx-vals='{"section": "sanka-property-object-relation", "import_data_type":"purchaseorder","mapping_storage_id":"{{mapping_storage_id}}"}'
            hx-swap='beforeend'
            onchange="customSubmit(),htmx.trigger(this, 'htmx-change')"

            >  
            {% with header_index=forloop.counter0 %}
                {% for column in columns %}
                <option value='{{column}}' {% if mapping_sanka_property_storage ==  column %}selected{% endif %}>
                    {% if column|startswith:'source_item__' %}
                        {% with custom_column=column|cut:'source_item__'|search_custom_field_object_items:request item_col_display=column|cut:'source_item__'|display_column_items:request %}
                            {% if custom_column %}
                                {{'source_item'|display_column_orders:request}} - {{custom_column.name}}
                            {% else %}
                                {{'source_item'|display_column_orders:request}} - {{item_col_display}}
                            {% endif %}
                        {% endwith %}
                    {% else %}
                        {% with args=column|add:'|'|add:import_as %} 
                            {% with column_display=args|get_column_display:request %}
                                {{column_display.name}}
                            {% endwith %}
                        {% endwith %}
                    {% endif %}
                </option>   
                {% endfor %} 
            {% endwith %}
        </select>
    </div>

    <script>
        $(document).ready(function() {
            $('.select2-this-purchaseorder-{{header}}').each(function() {
                let selectElement = $(this).closest('select').get(0);
                if (selectElement) {
                    if (selectElement.value == 'item_id' || selectElement.value == 'contact_id' || selectElement.value == 'company_id'){
                        selectElement && selectElement.dispatchEvent(new Event('htmx-change'));
                    }
                }
            });
            
            checkDuplicateRelations();
            checking_object_relation();
            
        });
    </script>

{% elif constant.TYPE_OBJECT_EXPENSE == import_as %}

    <div>
        <select name="sanka-properties" class="bg-white form-select form-select-solid border h-40px select2-this-expense-{{header}}" 
            {% if LANGUAGE_CODE == 'ja'%}
            data-placeholder="経費ヘッダーを選択"
            {% else %}
            data-placeholder="Select Expenses Header"
            {% endif %}

            hx-target="#choose-object-relation-field-expense"
            hx-get="{% host_url 'csv_upload' host 'app' %}"
            hx-trigger="htmx-change delay:0.5s,load delay:0.5s"
            hx-vals='{"section": "sanka-property-object-relation", "import_data_type":"expense","mapping_storage_id":"{{mapping_storage_id}}"}'
            hx-swap='beforeend'
            onchange="customSubmit(),htmx.trigger(this, 'htmx-change')"

            >  
            {% with header_index=forloop.counter0 %}
                {% for column in columns %}
                <option value='{{column}}' {% if mapping_sanka_property_storage ==  column %}selected{% endif %}>
                    {% with args=column|add:'|expense' %} 
                        {% with column_display=args|get_column_display:request %}
                            {{column_display.name}}
                        {% endwith %}
                    {% endwith %}
                </option>   
                {% endfor %} 
            {% endwith %}
        </select>
    </div>

    <script>
        $(document).ready(function() {
            $('.select2-this-expense-{{header}}').each(function() {
                let selectElement = $(this).closest('select').get(0);
                if (selectElement) {
                    if (selectElement.value == 'item_id' || selectElement.value == 'contact_id' || selectElement.value == 'company_id'){
                        selectElement && selectElement.dispatchEvent(new Event('htmx-change'));
                    }
                }
            });
            
            checkDuplicateRelations();
            checking_object_relation();
            
        });
    </script>

{% elif constant.TYPE_OBJECT_JOURNAL == import_as %}

<div>
    <select name="sanka-properties" class="bg-white form-select form-select-solid border h-40px select2-this-journal-{{header}}" 
        {% if LANGUAGE_CODE == 'ja'%}
        data-placeholder="ジャーナルヘッダーを選択"
        {% else %}
        data-placeholder="Select Journal Header"
        {% endif %}

        hx-target="#choose-object-relation-field-journal"
        hx-get="{% host_url 'csv_upload' host 'app' %}"
        hx-trigger="htmx-change delay:0.5s,load delay:0.5s"
        hx-vals='{"section": "sanka-property-object-relation", "import_data_type":"journal","mapping_storage_id":"{{mapping_storage_id}}"}'
        hx-swap='beforeend'
        onchange="customSubmit(),htmx.trigger(this, 'htmx-change')"

        >  
        {% with header_index=forloop.counter0 %}
            {% for column in columns %}
            <option value='{{column}}' {% if mapping_sanka_property_storage ==  column %}selected{% endif %}>
                
                {% with args=column|add:'|'|add:import_as %} 
                    {% with column_display=args|get_column_display:request %}
                        {{column_display.name}}
                    {% endwith %}
                {% endwith %}
                
            </option>   
            {% endfor %} 
        {% endwith %}
    </select>
</div>

<script>
    $(document).ready(function() {
        $('.select2-this-journal-{{header}}').each(function() {
            let selectElement = $(this).closest('select').get(0);
            if (selectElement) {
                if (selectElement.value == 'item_id' || selectElement.value == 'contact_id' || selectElement.value == 'company_id'){
                    selectElement && selectElement.dispatchEvent(new Event('htmx-change'));
                }
            }
        });
        
        checkDuplicateRelations();
        checking_object_relation();
        
    });
</script>

{% elif custom_object %}
    <div>
        <select name="sanka-properties" class="bg-white form-select form-select-solid border h-40px select2-this-{{custom_object.slug}}-{{header}}" 
            {% if LANGUAGE_CODE == 'ja'%}
            data-placeholder="{{custom_object.name}}ヘッダーを選択"
            {% else %}
            data-placeholder="Select {{custom_object.name}} Header"
            {% endif %}
            onchange="customSubmit()"
            >  
            {% with header_index=forloop.counter0 %}
                {% for column in columns %}
                <option value='{{column}}' {% if mapping_sanka_property_storage ==  column %}selected{% endif %}>
                    {% search_custom_field_object_custom_object custom_object.id column request as custom_column %}
                    {% if custom_column.name %}
                        {% with custom_column=custom_column %}
                            {{custom_column.name}}
                        {% endwith %}
                    {% else %}
                        {% with default_column=column|display_column_custom_objects:request %}
                            {{default_column}}
                        {% endwith %}
                    {% endif %} 
                </option>   
                {% endfor %} 
            {% endwith %}
        </select>
    </div>

{% endif %}

<script>
    $(document).ready(function() {

        {% if import_as == 'item' and component_props %}
            function checkToggleComponentKey(){
                const componentProps = ['{{ component_props|join:"', '" }}']
                const sankaProps = $('select[name="sanka-properties"]').map(function(){
                    return $(this).val();
                }).get();
                if (sankaProps.some(prop => componentProps.includes(prop))) {
                    document.getElementById('component-key').classList.remove('d-none')
                    document.querySelector('select[name="component-key"]').disabled = false
                } else {
                    document.getElementById('component-key').classList.add('d-none')
                    document.querySelector('select[name="component-key"]').disabled = true
                }
            }
        {% endif %}

        var selectImportAS = ['order','item','contact','company','billing','inventory','production','customer_case','{{constant.TYPE_OBJECT_INVENTORY}}','commerce_inventory_transaction','commerce_inventory_warehouse','estimates','invoices','delivery_slips','receipts','slips','commerce_subscription','purchaseorder','expense','journal','{{custom_object.slug}}']; 
        selectImportAS.forEach(function(import_as) {
            //console.log("import_as: ", import_as)
            var string=""
            if (import_as == 'item'){
                string = `{% if LANGUAGE_CODE == 'ja'%}商品プロパティの作成{% else %}Create Item Property{% endif %}`
            }
            else if (import_as == 'order'){
                string = `{% if LANGUAGE_CODE == 'ja'%}受注プロパティの作成{% else %}Create Order Property{% endif %}`
            }
            else if (import_as == 'contact'){
                string = `{% if LANGUAGE_CODE == 'ja'%}連絡先プロパティの作成{% else %}Create Contact Property{% endif %}`
            }
            else if (import_as == 'company'){
                string = `{% if LANGUAGE_CODE == 'ja'%}企業プロパティの作成{% else %}Create Company Property{% endif %}`
            }
            else if (import_as == 'billing'){
                string = `{% if LANGUAGE_CODE == 'ja'%}支払請求プロパティの作成{% else %}Create Bill Property{% endif %}`
            }
            else if (import_as == "{{constant.TYPE_OBJECT_INVENTORY}}"){
                string = `{% if LANGUAGE_CODE == 'ja'%}在庫プロパティの作成{% else %}Create Inventory Property{% endif %}`
            }
            else if (import_as == 'customer_case'){
                string = `{% if LANGUAGE_CODE == 'ja'%}案件プロパティの作成{% else %}Create Case Property{% endif %}`
            }
            else if (import_as == 'commerce_inventory_transaction'){
                string = `{% if LANGUAGE_CODE == 'ja'%}入出庫プロパティの作成{% else %}Create Inventory Transaction Property{% endif %}`
            }
            else if (import_as == 'commerce_inventory_warehouse'){
                string = `{% if LANGUAGE_CODE == 'ja'%}ロケーションプロパティの作成{% else %}Create Location Property{% endif %}`
            }
            else if (import_as == 'estimates'){
                string = `{% if LANGUAGE_CODE == 'ja'%}見積書プロパティの作成{% else %}Create Estimate Property{% endif %}`
            }
            else if (import_as == 'invoices'){
                string = `{% if LANGUAGE_CODE == 'ja'%}請求書プロパティの作成{% else %}Create Invoice Property{% endif %}`
            }
            else if (import_as == 'delivery_slips'){
                string = `{% if LANGUAGE_CODE == 'ja'%}納品書プロパティの作成{% else %}Create Delivery Note Property{% endif %}`
            }
            else if (import_as == 'receipts'){
                string = `{% if LANGUAGE_CODE == 'ja'%}入金プロパティを作成{% else %}Create Payment Property{% endif %}`
            }
            else if (import_as == 'slips'){
                string = `{% if LANGUAGE_CODE == 'ja'%}スリッププロパティの作成{% else %}Create Slip Property{% endif %}`
            }
            else if (import_as == 'commerce_subscription'){
                string = `{% if LANGUAGE_CODE == 'ja'%}サブスクリプションプロパティの作成{% else %}Create Subscriptions Property{% endif %}`
            }
            else if (import_as == 'purchaseorder'){
                string = `{% if LANGUAGE_CODE == 'ja'%}発注プロパティの作成{% else %}Create Purchase Order Property{% endif %}`
            }
            else if (import_as == 'expense'){
                string = `{% if LANGUAGE_CODE == 'ja'%}経費プロパティの作成{% else %}Create Expense Property{% endif %}`
            }
            else if (import_as == 'journal'){
                string = `{% if LANGUAGE_CODE == 'ja'%}ジャーナルエントリプロパティの作成{% else %}Create Journal Entry Property{% endif %}`
            }
            else if (import_as == '{{custom_object.slug}}'){
                string = `{% if LANGUAGE_CODE == 'ja'%}{{import_as|title}}プロパティの作成{% else %}Create {{import_as|title}} Property{% endif %}`
            }


            var previousValues = {};
            $(`.select2-this-${import_as}-{{header}}`).select2().on('select2:open', function(e) {
                // Add footer after dropdown opens
                if (!$(`.select2-footer`).length) {
                    
                    $(`.select2-dropdown`).append(`
                        <div class="select2-footer">
                            <div class="p-2 fw-bolder fs-5 border-t text-sm text-center">
                                <!-- Your footer content here -->
                                <a type='button' class="cursor-pointer fs-8 create-property-in-dropdown">
                                    ${string}
                                </a>
                            </div>
                        </div>
                    `);

                    // Add click handler
                    $(`.select2-footer .create-property-in-dropdown`).on('click', function() {
                        $(`.select2-this-${import_as}-{{header}}`).select2('close');
                        
                        var elements = document.getElementsByClassName(`create-property-elm-${import_as}`);
                        if (elements.length > 0) {
                            elements[0].click();
                        }


                    });

                }
                
            }).on('select2:opening', function(e) {
                // Store the current value before change
                var selectElement = $(this).closest('select').get(0);
                if (selectElement) {
                    previousValues[selectElement.id] = selectElement.value;
                }
            }).on('select2:select', function (e) {
                var selectElement = $(this).closest('select').get(0);
                let oldValue = previousValues[selectElement.id] || '';
                let currentValue = selectElement.value;

                if (['order', 'inventory','commerce_inventory_transaction','purchaseorder','expense','journal'].includes(import_as)) {
                    if (selectElement) {
                        // Trigger if changing TO item_id OR FROM item_id
                        if ((currentValue === 'item_id') || (oldValue === 'item_id')) {
                            selectElement.dispatchEvent(new Event('htmx-change'));
                        }
                    }
                } else {
                    // Keep your existing else condition for other cases
                    selectElement && selectElement.dispatchEvent(new Event('htmx-change'));
                }

                // Update the previous value after change
                previousValues[selectElement.id] = currentValue;

                {% if import_as == 'item' and component_props %}
                checkToggleComponentKey()
                {% endif %}
            });
        });
        {% if import_as == 'item' and component_props %}
        checkToggleComponentKey()
        {% endif %}
    });
</script>
