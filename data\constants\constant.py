from .properties_constant import *

timzone = ['Africa/Abidjan',
           'Africa/Accra',
           'Africa/Addis_Ababa',
           'Africa/Algiers',
           'Africa/Asmara',
           'Africa/Asmera',
           'Africa/Bamako',
           'Africa/Bangui',
           'Africa/Banjul',
           'Africa/Bissau',
           'Africa/Blantyre',
           'Africa/Brazzaville',
           'Africa/Bujumbura',
           'Africa/Cairo',
           'Africa/Casablanca',
           'Africa/Ceuta',
           'Africa/Conakry',
           'Africa/Dakar',
           'Africa/Dar_es_Salaam',
           'Africa/Djibouti',
           'Africa/Douala',
           'Africa/El_Aaiun',
           'Africa/Freetown',
           'Africa/Gaborone',
           'Africa/Harare',
           'Africa/Johannesburg',
           'Africa/Juba',
           'Africa/Kampala',
           'Africa/Khartoum',
           'Africa/Kigali',
           'Africa/Kinshasa',
           'Africa/Lagos',
           'Africa/Libreville',
           'Africa/Lome',
           'Africa/Luanda',
           'Africa/Lubumbashi',
           'Africa/Lusaka',
           'Africa/Malabo',
           'Africa/Maputo',
           'Africa/Maseru',
           'Africa/Mbabane',
           'Africa/Mogadishu',
           'Africa/Monrovia',
           'Africa/Nairobi',
           'Africa/Ndjamena',
           'Africa/Niamey',
           'Africa/Nouakchott',
           'Africa/Ouagadougou',
           'Africa/Porto-Novo',
           'Africa/Sao_Tome',
           'Africa/Timbuktu',
           'Africa/Tripoli',
           'Africa/Tunis',
           'Africa/Windhoek',
           'America/Adak',
           'America/Anchorage',
           'America/Anguilla',
           'America/Antigua',
           'America/Araguaina',
           'America/Argentina/Buenos_Aires',
           'America/Argentina/Catamarca',
           'America/Argentina/ComodRivadavia',
           'America/Argentina/Cordoba',
           'America/Argentina/Jujuy',
           'America/Argentina/La_Rioja',
           'America/Argentina/Mendoza',
           'America/Argentina/Rio_Gallegos',
           'America/Argentina/Salta',
           'America/Argentina/San_Juan',
           'America/Argentina/San_Luis',
           'America/Argentina/Tucuman',
           'America/Argentina/Ushuaia',
           'America/Aruba',
           'America/Asuncion',
           'America/Atikokan',
           'America/Atka',
           'America/Bahia',
           'America/Bahia_Banderas',
           'America/Barbados',
           'America/Belem',
           'America/Belize',
           'America/Blanc-Sablon',
           'America/Boa_Vista',
           'America/Bogota',
           'America/Boise',
           'America/Buenos_Aires',
           'America/Cambridge_Bay',
           'America/Campo_Grande',
           'America/Cancun',
           'America/Caracas',
           'America/Catamarca',
           'America/Cayenne',
           'America/Cayman',
           'America/Chicago',
           'America/Chihuahua',
           'America/Coral_Harbour',
           'America/Cordoba',
           'America/Costa_Rica',
           'America/Creston',
           'America/Cuiaba',
           'America/Curacao',
           'America/Danmarkshavn',
           'America/Dawson',
           'America/Dawson_Creek',
           'America/Denver',
           'America/Detroit',
           'America/Dominica',
           'America/Edmonton',
           'America/Eirunepe',
           'America/El_Salvador',
           'America/Ensenada',
           'America/Fort_Nelson',
           'America/Fort_Wayne',
           'America/Fortaleza',
           'America/Glace_Bay',
           'America/Godthab',
           'America/Goose_Bay',
           'America/Grand_Turk',
           'America/Grenada',
           'America/Guadeloupe',
           'America/Guatemala',
           'America/Guayaquil',
           'America/Guyana',
           'America/Halifax',
           'America/Havana',
           'America/Hermosillo',
           'America/Indiana/Indianapolis',
           'America/Indiana/Knox',
           'America/Indiana/Marengo',
           'America/Indiana/Petersburg',
           'America/Indiana/Tell_City',
           'America/Indiana/Vevay',
           'America/Indiana/Vincennes',
           'America/Indiana/Winamac',
           'America/Indianapolis',
           'America/Inuvik',
           'America/Iqaluit',
           'America/Jamaica',
           'America/Jujuy',
           'America/Juneau',
           'America/Kentucky/Louisville',
           'America/Kentucky/Monticello',
           'America/Knox_IN',
           'America/Kralendijk',
           'America/La_Paz',
           'America/Lima',
           'America/Los_Angeles',
           'America/Louisville',
           'America/Lower_Princes',
           'America/Maceio',
           'America/Managua',
           'America/Manaus',
           'America/Marigot',
           'America/Martinique',
           'America/Matamoros',
           'America/Mazatlan',
           'America/Mendoza',
           'America/Menominee',
           'America/Merida',
           'America/Metlakatla',
           'America/Mexico_City',
           'America/Miquelon',
           'America/Moncton',
           'America/Monterrey',
           'America/Montevideo',
           'America/Montreal',
           'America/Montserrat',
           'America/Nassau',
           'America/New_York',
           'America/Nipigon',
           'America/Nome',
           'America/Noronha',
           'America/North_Dakota/Beulah',
           'America/North_Dakota/Center',
           'America/North_Dakota/New_Salem',
           'America/Nuuk',
           'America/Ojinaga',
           'America/Panama',
           'America/Pangnirtung',
           'America/Paramaribo',
           'America/Phoenix',
           'America/Port-au-Prince',
           'America/Port_of_Spain',
           'America/Porto_Acre',
           'America/Porto_Velho',
           'America/Puerto_Rico',
           'America/Punta_Arenas',
           'America/Rainy_River',
           'America/Rankin_Inlet',
           'America/Recife',
           'America/Regina',
           'America/Resolute',
           'America/Rio_Branco',
           'America/Rosario',
           'America/Santa_Isabel',
           'America/Santarem',
           'America/Santiago',
           'America/Santo_Domingo',
           'America/Sao_Paulo',
           'America/Scoresbysund',
           'America/Shiprock',
           'America/Sitka',
           'America/St_Barthelemy',
           'America/St_Johns',
           'America/St_Kitts',
           'America/St_Lucia',
           'America/St_Thomas',
           'America/St_Vincent',
           'America/Swift_Current',
           'America/Tegucigalpa',
           'America/Thule',
           'America/Thunder_Bay',
           'America/Tijuana',
           'America/Toronto',
           'America/Tortola',
           'America/Vancouver',
           'America/Virgin',
           'America/Whitehorse',
           'America/Winnipeg',
           'America/Yakutat',
           'America/Yellowknife',
           'Antarctica/Casey',
           'Antarctica/Davis',
           'Antarctica/DumontDUrville',
           'Antarctica/Macquarie',
           'Antarctica/Mawson',
           'Antarctica/McMurdo',
           'Antarctica/Palmer',
           'Antarctica/Rothera',
           'Antarctica/South_Pole',
           'Antarctica/Syowa',
           'Antarctica/Troll',
           'Antarctica/Vostok',
           'Arctic/Longyearbyen',
           'Asia/Aden',
           'Asia/Almaty',
           'Asia/Amman',
           'Asia/Anadyr',
           'Asia/Aqtau',
           'Asia/Aqtobe',
           'Asia/Ashgabat',
           'Asia/Ashkhabad',
           'Asia/Atyrau',
           'Asia/Baghdad',
           'Asia/Bahrain',
           'Asia/Baku',
           'Asia/Bangkok',
           'Asia/Barnaul',
           'Asia/Beirut',
           'Asia/Bishkek',
           'Asia/Brunei',
           'Asia/Calcutta',
           'Asia/Chita',
           'Asia/Choibalsan',
           'Asia/Chongqing',
           'Asia/Chungking',
           'Asia/Colombo',
           'Asia/Dacca',
           'Asia/Damascus',
           'Asia/Dhaka',
           'Asia/Dili',
           'Asia/Dubai',
           'Asia/Dushanbe',
           'Asia/Famagusta',
           'Asia/Gaza',
           'Asia/Harbin',
           'Asia/Hebron',
           'Asia/Ho_Chi_Minh',
           'Asia/Hong_Kong',
           'Asia/Hovd',
           'Asia/Irkutsk',
           'Asia/Istanbul',
           'Asia/Jakarta',
           'Asia/Jayapura',
           'Asia/Jerusalem',
           'Asia/Kabul',
           'Asia/Kamchatka',
           'Asia/Karachi',
           'Asia/Kashgar',
           'Asia/Kathmandu',
           'Asia/Katmandu',
           'Asia/Khandyga',
           'Asia/Kolkata',
           'Asia/Krasnoyarsk',
           'Asia/Kuala_Lumpur',
           'Asia/Kuching',
           'Asia/Kuwait',
           'Asia/Macao',
           'Asia/Macau',
           'Asia/Magadan',
           'Asia/Makassar',
           'Asia/Manila',
           'Asia/Muscat',
           'Asia/Nicosia',
           'Asia/Novokuznetsk',
           'Asia/Novosibirsk',
           'Asia/Omsk',
           'Asia/Oral',
           'Asia/Phnom_Penh',
           'Asia/Pontianak',
           'Asia/Pyongyang',
           'Asia/Qatar',
           'Asia/Qostanay',
           'Asia/Qyzylorda',
           'Asia/Rangoon',
           'Asia/Riyadh',
           'Asia/Saigon',
           'Asia/Sakhalin',
           'Asia/Samarkand',
           'Asia/Seoul',
           'Asia/Shanghai',
           'Asia/Singapore',
           'Asia/Srednekolymsk',
           'Asia/Taipei',
           'Asia/Tashkent',
           'Asia/Tbilisi',
           'Asia/Tehran',
           'Asia/Tel_Aviv',
           'Asia/Thimbu',
           'Asia/Thimphu',
           'Asia/Tokyo',
           'Asia/Tomsk',
           'Asia/Ujung_Pandang',
           'Asia/Ulaanbaatar',
           'Asia/Ulan_Bator',
           'Asia/Urumqi',
           'Asia/Ust-Nera',
           'Asia/Vientiane',
           'Asia/Vladivostok',
           'Asia/Yakutsk',
           'Asia/Yangon',
           'Asia/Yekaterinburg',
           'Asia/Yerevan',
           'Atlantic/Azores',
           'Atlantic/Bermuda',
           'Atlantic/Canary',
           'Atlantic/Cape_Verde',
           'Atlantic/Faeroe',
           'Atlantic/Faroe',
           'Atlantic/Jan_Mayen',
           'Atlantic/Madeira',
           'Atlantic/Reykjavik',
           'Atlantic/South_Georgia',
           'Atlantic/St_Helena',
           'Atlantic/Stanley',
           'Australia/ACT',
           'Australia/Adelaide',
           'Australia/Brisbane',
           'Australia/Broken_Hill',
           'Australia/Canberra',
           'Australia/Currie',
           'Australia/Darwin',
           'Australia/Eucla',
           'Australia/Hobart',
           'Australia/LHI',
           'Australia/Lindeman',
           'Australia/Lord_Howe',
           'Australia/Melbourne',
           'Australia/NSW',
           'Australia/North',
           'Australia/Perth',
           'Australia/Queensland',
           'Australia/South',
           'Australia/Sydney',
           'Australia/Tasmania',
           'Australia/Victoria',
           'Australia/West',
           'Australia/Yancowinna',
           'Brazil/Acre',
           'Brazil/DeNoronha',
           'Brazil/East',
           'Brazil/West',
           'CET',
           'CST6CDT',
           'Canada/Atlantic',
           'Canada/Central',
           'Canada/Eastern',
           'Canada/Mountain',
           'Canada/Newfoundland',
           'Canada/Pacific',
           'Canada/Saskatchewan',
           'Canada/Yukon',
           'Chile/Continental',
           'Chile/EasterIsland',
           'Cuba',
           'EET',
           'EST',
           'EST5EDT',
           'Egypt',
           'Eire',
           'Etc/GMT',
           'Etc/GMT+0',
           'Etc/GMT+1',
           'Etc/GMT+10',
           'Etc/GMT+11',
           'Etc/GMT+12',
           'Etc/GMT+2',
           'Etc/GMT+3',
           'Etc/GMT+4',
           'Etc/GMT+5',
           'Etc/GMT+6',
           'Etc/GMT+7',
           'Etc/GMT+8',
           'Etc/GMT+9',
           'Etc/GMT-0',
           'Etc/GMT-1',
           'Etc/GMT-10',
           'Etc/GMT-11',
           'Etc/GMT-12',
           'Etc/GMT-13',
           'Etc/GMT-14',
           'Etc/GMT-2',
           'Etc/GMT-3',
           'Etc/GMT-4',
           'Etc/GMT-5',
           'Etc/GMT-6',
           'Etc/GMT-7',
           'Etc/GMT-8',
           'Etc/GMT-9',
           'Etc/GMT0',
           'Etc/Greenwich',
           'Etc/UCT',
           'Etc/UTC',
           'Etc/Universal',
           'Etc/Zulu',
           'Europe/Amsterdam',
           'Europe/Andorra',
           'Europe/Astrakhan',
           'Europe/Athens',
           'Europe/Belfast',
           'Europe/Belgrade',
           'Europe/Berlin',
           'Europe/Bratislava',
           'Europe/Brussels',
           'Europe/Bucharest',
           'Europe/Budapest',
           'Europe/Busingen',
           'Europe/Chisinau',
           'Europe/Copenhagen',
           'Europe/Dublin',
           'Europe/Gibraltar',
           'Europe/Guernsey',
           'Europe/Helsinki',
           'Europe/Isle_of_Man',
           'Europe/Istanbul',
           'Europe/Jersey',
           'Europe/Kaliningrad',
           'Europe/Kiev',
           'Europe/Kirov',
           'Europe/Lisbon',
           'Europe/Ljubljana',
           'Europe/London',
           'Europe/Luxembourg',
           'Europe/Madrid',
           'Europe/Malta',
           'Europe/Mariehamn',
           'Europe/Minsk',
           'Europe/Monaco',
           'Europe/Moscow',
           'Europe/Nicosia',
           'Europe/Oslo',
           'Europe/Paris',
           'Europe/Podgorica',
           'Europe/Prague',
           'Europe/Riga',
           'Europe/Rome',
           'Europe/Samara',
           'Europe/San_Marino',
           'Europe/Sarajevo',
           'Europe/Saratov',
           'Europe/Simferopol',
           'Europe/Skopje',
           'Europe/Sofia',
           'Europe/Stockholm',
           'Europe/Tallinn',
           'Europe/Tirane',
           'Europe/Tiraspol',
           'Europe/Ulyanovsk',
           'Europe/Uzhgorod',
           'Europe/Vaduz',
           'Europe/Vatican',
           'Europe/Vienna',
           'Europe/Vilnius',
           'Europe/Volgograd',
           'Europe/Warsaw',
           'Europe/Zagreb',
           'Europe/Zaporozhye',
           'Europe/Zurich',
           'GB',
           'GB-Eire',
           'GMT',
           'GMT+0',
           'GMT-0',
           'GMT0',
           'Greenwich',
           'HST',
           'Hongkong',
           'Iceland',
           'Indian/Antananarivo',
           'Indian/Chagos',
           'Indian/Christmas',
           'Indian/Cocos',
           'Indian/Comoro',
           'Indian/Kerguelen',
           'Indian/Mahe',
           'Indian/Maldives',
           'Indian/Mauritius',
           'Indian/Mayotte',
           'Indian/Reunion',
           'Iran',
           'Israel',
           'Jamaica',
           'Japan',
           'Kwajalein',
           'Libya',
           'MET',
           'MST',
           'MST7MDT',
           'Mexico/BajaNorte',
           'Mexico/BajaSur',
           'Mexico/General',
           'NZ',
           'NZ-CHAT',
           'Navajo',
           'PRC',
           'PST8PDT',
           'Pacific/Apia',
           'Pacific/Auckland',
           'Pacific/Bougainville',
           'Pacific/Chatham',
           'Pacific/Chuuk',
           'Pacific/Easter',
           'Pacific/Efate',
           'Pacific/Enderbury',
           'Pacific/Fakaofo',
           'Pacific/Fiji',
           'Pacific/Funafuti',
           'Pacific/Galapagos',
           'Pacific/Gambier',
           'Pacific/Guadalcanal',
           'Pacific/Guam',
           'Pacific/Honolulu',
           'Pacific/Johnston',
           'Pacific/Kanton',
           'Pacific/Kiritimati',
           'Pacific/Kosrae',
           'Pacific/Kwajalein',
           'Pacific/Majuro',
           'Pacific/Marquesas',
           'Pacific/Midway',
           'Pacific/Nauru',
           'Pacific/Niue',
           'Pacific/Norfolk',
           'Pacific/Noumea',
           'Pacific/Pago_Pago',
           'Pacific/Palau',
           'Pacific/Pitcairn',
           'Pacific/Pohnpei',
           'Pacific/Ponape',
           'Pacific/Port_Moresby',
           'Pacific/Rarotonga',
           'Pacific/Saipan',
           'Pacific/Samoa',
           'Pacific/Tahiti',
           'Pacific/Tarawa',
           'Pacific/Tongatapu',
           'Pacific/Truk',
           'Pacific/Wake',
           'Pacific/Wallis',
           'Pacific/Yap',
           'Poland',
           'Portugal',
           'ROC',
           'ROK',
           'Singapore',
           'Turkey',
           'UCT',
           'US/Alaska',
           'US/Aleutian',
           'US/Arizona',
           'US/Central',
           'US/East-Indiana',
           'US/Eastern',
           'US/Hawaii',
           'US/Indiana-Starke',
           'US/Michigan',
           'US/Mountain',
           'US/Pacific',
           'US/Samoa',
           'UTC',
           'Universal',
           'W-SU',
           'WET',
           'Zulu']

TIMEZONE_MODEL = tuple(zip(timzone, timzone))


CURRENCY_MODEL = [
    ('AED', 'AED (د.إ)', 'د.إ'),
    ('AFN', 'AFN (؋)', '؋'),
    ('ALL', 'ALL (L)', 'L'),
    ('AMD', 'AMD (֏)', '֏'),
    ('ANG', 'ANG (ƒ)', 'ƒ'),
    ('AOA', 'AOA (Kz)', 'Kz'),
    ('ARS', 'ARS ($)', '$'),
    ('AUD', 'AUD ($)', '$'),
    ('AWG', 'AWG (ƒ)', 'ƒ'),
    ('AZN', 'AZN (₼)', '₼'),
    ('BAM', 'BAM (KM)', 'KM'),
    ('BBD', 'BBD ($)', '$'),
    ('BDT', 'BDT (৳)', '৳'),
    ('BGN', 'BGN (лв)', 'лв'),
    ('BHD', 'BHD (.د.ب)', '.د.ب,'),
    ('BIF', 'BIF (FBu)', 'FBu'),
    ('BMD', 'BMD ($)', '$'),
    ('BND', 'BND ($)', '$'),
    ('BOB', 'BOB ($b)', '$b'),
    ('BRL', 'BRL (R$)', 'R$'),
    ('BSD', 'BSD ($)', '$'),
    ('BTC', 'BTC (฿)', '฿'),
    ('BTN', 'BTN (Nu.)', 'Nu.'),
    ('BWP', 'BWP (P)', 'P'),
    ('BYR', 'BYR (Br)', 'Br'),
    ('BYN', 'BYN (Br)', 'Br'),
    ('BZD', 'BZD (BZ$)', 'BZ$'),
    ('CAD', 'CAD ($)', '$'),
    ('CDF', 'CDF (FC)', 'FC'),
    ('CHF', 'CHF (CHF)', 'CHF'),
    ('CLP', 'CLP ($)', '$'),
    ('CNY', 'CNY (¥)', '¥'),
    ('COP', 'COP ($)', '$'),
    ('CRC', 'CRC (₡)', '₡'),
    ('CUC', 'CUC ($)', '$'),
    ('CUP', 'CUP (₱)', '₱'),
    ('CVE', 'CVE ($)', '$'),
    ('CZK', 'CZK (Kč)', 'Kč'),
    ('DJF', 'DJF (Fdj)', 'Fdj'),
    ('DKK', 'DKK (kr)', 'kr'),
    ('DOP', 'DOP (RD$)', 'RD$'),
    ('DZD', 'DZD (دج)', 'دج'),
    ('EEK', 'EEK (kr)', 'kr'),
    ('EGP', 'EGP (£)', '£'),
    ('ERN', 'ERN (Nfk)', 'Nfk'),
    ('ETB', 'ETB (Br)', 'Br'),
    ('ETH', 'ETH (Ξ)', 'Ξ'),
    ('EUR', 'EUR (€)', '€'),
    ('FJD', 'FJD ($)', '$'),
    ('FKP', 'FKP (£)', '£'),
    ('GBP', 'GBP (£)', '£'),
    ('GEL', 'GEL (₾)', '₾'),
    ('GGP', 'GGP (£)', '£'),
    ('GHC', 'GHC (₵)', '₵'),
    ('GHS', 'GHS (GH₵)', 'GH₵'),
    ('GIP', 'GIP (£)', '£'),
    ('GMD', 'GMD (D)', 'D'),
    ('GNF', 'GNF (FG)', 'FG'),
    ('GTQ', 'GTQ (Q)', 'Q'),
    ('GYD', 'GYD ($)', '$'),
    ('HKD', 'HKD ($)', '$'),
    ('HNL', 'HNL (L)', 'L'),
    ('HRK', 'HRK (kn)', 'kn'),
    ('HTG', 'HTG (G)', 'G'),
    ('HUF', 'HUF (Ft)', 'Ft'),
    ('IDR', 'IDR (Rp)', 'Rp'),
    ('ILS', 'ILS (₪)', '₪'),
    ('IMP', 'IMP (£)', '£'),
    ('INR', 'INR (₹)', '₹'),
    ('IQD', 'IQD (ع.د)', 'ع.د'),
    ('IRR', 'IRR (﷼)', '﷼'),
    ('ISK', 'ISK (kr)', 'kr'),
    ('JEP', 'JEP (£)', '£'),
    ('JMD', 'JMD (J$)', 'J$'),
    ('JOD', 'JOD (JD)', 'JD'),
    ('JPY', 'JPY (¥)', '¥'),
    ('KES', 'KES (KSh)', 'KSh'),
    ('KGS', 'KGS (лв)', 'лв'),
    ('KHR', 'KHR (៛)', '៛'),
    ('KMF', 'KMF (CF)', 'CF'),
    ('KPW', 'KPW (₩)', '₩'),
    ('KRW', 'KRW (₩)', '₩'),
    ('KWD', 'KWD (KD)', 'KD'),
    ('KYD', 'KYD ($)', '$'),
    ('KZT', 'KZT (₸)', '₸'),
    ('LAK', 'LAK (₭)', '₭'),
    ('LBP', 'LBP (£)', '£'),
    ('LKR', 'LKR (₨)', '₨'),
    ('LRD', 'LRD ($)', '$'),
    ('LSL', 'LSL (M)', 'M'),
    ('LTC', 'LTC (Ł)', 'Ł'),
    ('LTL', 'LTL (Lt)', 'Lt'),
    ('LVL', 'LVL (Ls)', 'Ls'),
    ('LYD', 'LYD (LD)', 'LD'),
    ('MAD', 'MAD (MAD)', 'MAD'),
    ('MDL', 'MDL (lei)', 'lei'),
    ('MGA', 'MGA (Ar)', 'Ar'),
    ('MKD', 'MKD (ден)', 'ден'),
    ('MMK', 'MMK (K)', 'K'),
    ('MNT', 'MNT (₮)', '₮'),
    ('MOP', 'MOP (MOP$)', 'MOP$'),
    ('MRO', 'MRO (UM)', 'UM'),
    ('MRU', 'MRU (UM)', 'UM'),
    ('MUR', 'MUR (₨)', '₨'),
    ('MVR', 'MVR (Rf)', 'Rf'),
    ('MWK', 'MWK (MK)', 'MK'),
    ('MXN', 'MXN ($)', '$'),
    ('MYR', 'MYR (RM)', 'RM'),
    ('MZN', 'MZN (MT)', 'MT'),
    ('NAD', 'NAD ($)', '$'),
    ('NGN', 'NGN (₦)', '₦'),
    ('NIO', 'NIO (C$)', 'C$'),
    ('NOK', 'NOK (kr)', 'kr'),
    ('NPR', 'NPR (₨)', '₨'),
    ('NZD', 'NZD ($)', '$'),
    ('OMR', 'OMR (﷼)', '﷼'),
    ('PAB', 'PAB (B/.)', 'B/.'),
    ('PEN', 'PEN (S/.)', 'S/.'),
    ('PGK', 'PGK (K)', 'K'),
    ('PHP', 'PHP (₱)', '₱'),
    ('PKR', 'PKR (₨)', '₨'),
    ('PLN', 'PLN (zł)', 'zł'),
    ('PYG', 'PYG (Gs)', 'Gs'),
    ('QAR', 'QAR (﷼)', '﷼'),
    ('RMB', 'RMB (￥)', '￥'),
    ('RON', 'RON (lei)', 'lei'),
    ('RSD', 'RSD (Дин.)', 'Дин.'),
    ('RUB', 'RUB (₽)', '₽'),
    ('RWF', 'RWF (R₣)', 'R₣'),
    ('SAR', 'SAR (﷼)', '﷼'),
    ('SBD', 'SBD ($)', '$'),
    ('SCR', 'SCR (₨)', '₨'),
    ('SDG', 'SDG (ج.س.)', 'ج.س.'),
    ('SEK', 'SEK (kr)', 'kr'),
    ('SGD', 'SGD ($)', '$'),
    ('SHP', 'SHP (£)', '£'),
    ('SLL', 'SLL (Le)', 'Le'),
    ('SOS', 'SOS (S)', 'S'),
    ('SRD', 'SRD ($)', '$'),
    ('SSP', 'SSP (£)', '£'),
    ('STD', 'STD (Db)', 'Db'),
    ('STN', 'STN (Db)', 'Db'),
    ('SVC', 'SVC ($)', '$'),
    ('SYP', 'SYP (£)', '£'),
    ('SZL', 'SZL (E)', 'E'),
    ('THB', 'THB (฿)', '฿'),
    ('TJS', 'TJS (SM)', 'SM'),
    ('TMT', 'TMT (T)', 'T'),
    ('TND', 'TND (د.ت)', 'د.ت'),
    ('TOP', 'TOP (T$)', 'T$'),
    ('TRL', 'TRL (₤)', '₤'),
    ('TRY', 'TRY (₺)', '₺'),
    ('TTD', 'TTD (TT$)', 'TT$'),
    ('TVD', 'TVD ($)', '$'),
    ('TWD', 'TWD (NT$)', 'NT$'),
    ('TZS', 'TZS (TSh)', 'TSh'),
    ('UAH', 'UAH (₴)', '₴'),
    ('UGX', 'UGX (USh)', 'USh'),
    ('USD', 'USD ($)', '$'),
    ('UYU', 'UYU ($U)', '$U'),
    ('UZS', 'UZS (лв)', 'лв'),
    ('VEF', 'VEF (Bs)', 'Bs'),
    ('VND', 'VND (₫)', '₫'),
    ('VUV', 'VUV (VT)', 'VT'),
    ('WST', 'WST (WS$)', 'WS$'),
    ('XAF', 'XAF (FCFA)', 'FCFA'),
    ('XBT', 'XBT (Ƀ)', 'Ƀ'),
    ('XCD', 'XCD ($)', '$'),
    ('XOF', 'XOF (CFA)', 'CFA'),
    ('XPF', 'XPF (₣)', '₣'),
    ('YER', 'YER (﷼)', '﷼'),
    ('ZAR', 'ZAR (R)', 'R'),
    ('ZWD', 'ZWD (Z$)', 'Z$'),
]

CURRENCY_CODES = [val[0] for val in CURRENCY_MODEL]

STRIPE_CREDIT_CARD_TEST_FINGERPRINT = [
    ('****************', "6feVS6A52lIqUG2i"),  # Visa (debit)
    ('****************', "Xp8toe5WkFXsjSdK"),  # Mastercard
    ('***************', "Z0dVVxM0O6bxiuyd"),  # American Express
    ('***************', "fG7msDuxHPPiLdUg"),  # American Express
    ('****************', "dWuxVJOnKhOMKYtd"),  # Discover
    ('3566002020360505', "9QE9E6dOuVHnfDDv"),  # JCB
]

GENERAL_CURRENCY = [
    ('AED', 'AED (د.إ)'),
    ('AFN', 'AFN (؋)'),
    ('ALL', 'ALL (L)'),
    ('AMD', 'AMD (֏)'),
    ('ANG', 'ANG (ƒ)'),
    ('AOA', 'AOA (Kz)'),
    ('ARS', 'ARS ($)'),
    ('AUD', 'AUD ($)'),
    ('AWG', 'AWG (ƒ)'),
    ('AZN', 'AZN (₼)'),
    ('BAM', 'BAM (KM)'),
    ('BBD', 'BBD ($)'),
    ('BDT', 'BDT (৳)'),
    ('BGN', 'BGN (лв)'),
    ('BHD', 'BHD (.د.ب)'),
    ('BIF', 'BIF (FBu)'),
    ('BMD', 'BMD ($)'),
    ('BND', 'BND ($)'),
    ('BOB', 'BOB ($b)'),
    ('BRL', 'BRL (R$)'),
    ('BSD', 'BSD ($)'),
    ('BTC', 'BTC (฿)'),
    ('BTN', 'BTN (Nu.)'),
    ('BWP', 'BWP (P)'),
    ('BYR', 'BYR (Br)'),
    ('BYN', 'BYN (Br)'),
    ('BZD', 'BZD (BZ$)'),
    ('CAD', 'CAD ($)'),
    ('CDF', 'CDF (FC)'),
    ('CHF', 'CHF (CHF)'),
    ('CLP', 'CLP ($)'),
    ('CNY', 'CNY (¥)'),
    ('COP', 'COP ($)'),
    ('CRC', 'CRC (₡)'),
    ('CUC', 'CUC ($)'),
    ('CUP', 'CUP (₱)'),
    ('CVE', 'CVE ($)'),
    ('CZK', 'CZK (Kč)'),
    ('DJF', 'DJF (Fdj)'),
    ('DKK', 'DKK (kr)'),
    ('DOP', 'DOP (RD$)'),
    ('DZD', 'DZD (دج)'),
    ('EEK', 'EEK (kr)'),
    ('EGP', 'EGP (£)'),
    ('ERN', 'ERN (Nfk)'),
    ('ETB', 'ETB (Br)'),
    ('ETH', 'ETH (Ξ)'),
    ('EUR', 'EUR (€)'),
    ('FJD', 'FJD ($)'),
    ('FKP', 'FKP (£)'),
    ('GBP', 'GBP (£)'),
    ('GEL', 'GEL (₾)'),
    ('GGP', 'GGP (£)'),
    ('GHC', 'GHC (₵)'),
    ('GHS', 'GHS (GH₵)'),
    ('GIP', 'GIP (£)'),
    ('GMD', 'GMD (D)'),
    ('GNF', 'GNF (FG)'),
    ('GTQ', 'GTQ (Q)'),
    ('GYD', 'GYD ($)'),
    ('HKD', 'HKD ($)'),
    ('HNL', 'HNL (L)'),
    ('HRK', 'HRK (kn)'),
    ('HTG', 'HTG (G)'),
    ('HUF', 'HUF (Ft)'),
    ('IDR', 'IDR (Rp)'),
    ('ILS', 'ILS (₪)'),
    ('IMP', 'IMP (£)'),
    ('INR', 'INR (₹)'),
    ('IQD', 'IQD (ع.د)'),
    ('IRR', 'IRR (﷼)'),
    ('ISK', 'ISK (kr)'),
    ('JEP', 'JEP (£)'),
    ('JMD', 'JMD (J$)'),
    ('JOD', 'JOD (JD)'),
    ('JPY', 'JPY (¥)'),
    ('KES', 'KES (KSh)'),
    ('KGS', 'KGS (лв)'),
    ('KHR', 'KHR (៛)'),
    ('KMF', 'KMF (CF)'),
    ('KPW', 'KPW (₩)'),
    ('KRW', 'KRW (₩)'),
    ('KWD', 'KWD (KD)'),
    ('KYD', 'KYD ($)'),
    ('KZT', 'KZT (₸)'),
    ('LAK', 'LAK (₭)'),
    ('LBP', 'LBP (£)'),
    ('LKR', 'LKR (₨)'),
    ('LRD', 'LRD ($)'),
    ('LSL', 'LSL (M)'),
    ('LTC', 'LTC (Ł)'),
    ('LTL', 'LTL (Lt)'),
    ('LVL', 'LVL (Ls)'),
    ('LYD', 'LYD (LD)'),
    ('MAD', 'MAD (MAD)'),
    ('MDL', 'MDL (lei)'),
    ('MGA', 'MGA (Ar)'),
    ('MKD', 'MKD (ден)'),
    ('MMK', 'MMK (K)'),
    ('MNT', 'MNT (₮)'),
    ('MOP', 'MOP (MOP$)'),
    ('MRO', 'MRO (UM)'),
    ('MRU', 'MRU (UM)'),
    ('MUR', 'MUR (₨)'),
    ('MVR', 'MVR (Rf)'),
    ('MWK', 'MWK (MK)'),
    ('MXN', 'MXN ($)'),
    ('MYR', 'MYR (RM)'),
    ('MZN', 'MZN (MT)'),
    ('NAD', 'NAD ($)'),
    ('NGN', 'NGN (₦)'),
    ('NIO', 'NIO (C$)'),
    ('NOK', 'NOK (kr)'),
    ('NPR', 'NPR (₨)'),
    ('NZD', 'NZD ($)'),
    ('OMR', 'OMR (﷼)'),
    ('PAB', 'PAB (B/.)'),
    ('PEN', 'PEN (S/.)'),
    ('PGK', 'PGK (K)'),
    ('PHP', 'PHP (₱)'),
    ('PKR', 'PKR (₨)'),
    ('PLN', 'PLN (zł)'),
    ('PYG', 'PYG (Gs)'),
    ('QAR', 'QAR (﷼)'),
    ('RMB', 'RMB (￥)'),
    ('RON', 'RON (lei)'),
    ('RSD', 'RSD (Дин.)'),
    ('RUB', 'RUB (₽)'),
    ('RWF', 'RWF (R₣)'),
    ('SAR', 'SAR (﷼)'),
    ('SBD', 'SBD ($)'),
    ('SCR', 'SCR (₨)'),
    ('SDG', 'SDG (ج.س.)'),
    ('SEK', 'SEK (kr)'),
    ('SGD', 'SGD ($)'),
    ('SHP', 'SHP (£)'),
    ('SLL', 'SLL (Le)'),
    ('SOS', 'SOS (S)'),
    ('SRD', 'SRD ($)'),
    ('SSP', 'SSP (£)'),
    ('STD', 'STD (Db)'),
    ('STN', 'STN (Db)'),
    ('SVC', 'SVC ($)'),
    ('SYP', 'SYP (£)'),
    ('SZL', 'SZL (E)'),
    ('THB', 'THB (฿)'),
    ('TJS', 'TJS (SM)'),
    ('TMT', 'TMT (T)'),
    ('TND', 'TND (د.ت)'),
    ('TOP', 'TOP (T$)'),
    ('TRL', 'TRL (₤)'),
    ('TRY', 'TRY (₺)'),
    ('TTD', 'TTD (TT$)'),
    ('TVD', 'TVD ($)'),
    ('TWD', 'TWD (NT$)'),
    ('TZS', 'TZS (TSh)'),
    ('UAH', 'UAH (₴)'),
    ('UGX', 'UGX (USh)'),
    ('USD', 'USD ($)'),
    ('UYU', 'UYU ($U)'),
    ('UZS', 'UZS (лв)'),
    ('VEF', 'VEF (Bs)'),
    ('VND', 'VND (₫)'),
    ('VUV', 'VUV (VT)'),
    ('WST', 'WST (WS$)'),
    ('XAF', 'XAF (FCFA)'),
    ('XBT', 'XBT (Ƀ)'),
    ('XCD', 'XCD ($)'),
    ('XOF', 'XOF (CFA)'),
    ('XPF', 'XPF (₣)'),
    ('YER', 'YER (﷼)'),
    ('ZAR', 'ZAR (R)'),
    ('ZWD', 'ZWD (Z$)')
]

ORDERS_COLUMNS_DISPLAY = {
    "order_id": {
        "en": "ID",
        "ja": "ID"
    },
    "number_item": {
        "en": "Number of Items",
        "ja": "商品数"
    },
    "currency": {
        "en": "Currency",
        "ja": "通貨"
    },
    "total_price": {
        "en": "Total Price",
        "ja": "合計金額"
    },
    "total_price_without_tax": {
        "en": "Total Price Without Tax",
        "ja": "税抜合計価格"
    },
    "line_item_tax": {
        "en": "Line Item - Tax Rate",
        "ja": "商品項目 - 税率"
    },
    "order_type": {
        "en": "Order Type",
        "ja": "受注タイプ"
    },
    "order_at": {
        "en": "Order At",
        "ja": "受注日時"
    },
    "created_at": {
        "en": "Created At",
        "ja": "作成日時"
    },
    "updated_at": {
        "en": "Updated At",
        "ja": "更新日時"
    },
    # Removed "status" field
    "discounts": {
        "en": "Discount",
        "ja": "割引"
    },
    "shipping_cost": {
        "en": "Shipping Cost",
        "ja": "配送費"
    },
    "customer": {
        "en": "Customer",
        "ja": "顧客"
    },
    "customer|name": {
        "en": "Customer - Full Name (Contact)",
        "ja": "顧客 - 姓名"
    },
    "customer|first_name": {
        "en": "Customer - First Name",
        "ja": "顧客 - 名前"
    },
    "customer|last_name": {
        "en": "Customer - Last Name",
        "ja": "顧客 - 苗字"
    },
    "customer|email": {
        "en": "Customer - Email",
        "ja": "顧客 - Eメール"
    },
    "line_item_name": {
        "en": "Line Item - Names",
        "ja": "商品項目 - 名前"
    },
    "delivery_status": {
        "en": "Status",
        "ja": "ステータス"
    },
    "tax_applied_to": {
        "en": "Tax Applied To",
        "ja": "適用税金"
    },
    "platform_id": {
        "en": "Platform ID",
        "ja": "プラットフォームID"
    },
    "memo": {
        "en": "Notes",
        "ja": "備考"
    },

    # added value from table mapping
    "price_of_items": {
        "en": "Price Of Items",
        "ja": "商品の価格"
    },
    "item_id": {
        "en": "Item",
        "ja": "商品"
    },
    "company": {
        "en": "Company",
        "ja": "企業"
    },
    "contact": {
        "en": "Contact",
        "ja": "連絡先"
    },
    "shopturboitemsorders": {
        "en": "Items",
        "ja": "商品"
    },
    "line_item_price": {  # property
        "en": "Line Item - Price",
        "ja": "商品項目 - 価格"
    },
    "line_item_status": {
        "en": "Line Item - Status",
        "ja": "商品項目 - ステータス"
    },
    'line_item': {
        'en': 'Line Item',
        'ja': '商品項目',
    },
    "source_item": {
        "en": "Source Item",
        "ja": "参照商品"
    },
    "source_item__inventory__total_inventory": {
        "en": "Source Item - Inventory Amount - Total",
        "ja": "参照商品 - 在庫量 - 合計"
    },
    "source_item__inventory__available_amount": {
        "en": "Source Item - Inventory Amount - Available",
        "ja": "参照商品 - 在庫量 - 販売可能"
    },
    "source_item__inventory__unavailable_amount": {
        "en": "Source Item - Inventory Amount - Unavailable",
        "ja": "参照商品 - 在庫量 - 利用不可"
    },
    "source_item__inventory__committed_amount": {
        "en": "Source Item - Inventory Amount - Committed",
        "ja": "参照商品 - 在庫量 - 確定済み"
    },
    "order-tax": {
        "en": "Order - Tax Rate",
        "ja": "受注 - 税率"
    },
    "company_name": {
        "en": "Company Name",
        "ja": "会社名"
    },
    "contact_name": {
        "en": "Contact Name",
        "ja": "連絡先名"
    },
    "company_id": {
        "en": "Company",
        "ja": "企業"
    },
    "contact_id": {
        "en": "Contact",
        "ja": "連絡先"
    },
    "line_item_quantity": {
        "en": "Line Item - Quantity",
        "ja": "商品項目 - 数量"
    },
    "line_item_name_quantity": {
        "en": "Line Item - Name and Quantity",
        "ja": "商品項目 - 名前と数量"
    },
    "line_item_inventory_location": {
        "en": "Inventory - Location",
        "ja": "在庫 - ロケーション"
    },
    'kanban_order': {
        'en': 'Kanban Order',
        'ja': 'カンバンの順序'
    },
    'shipping_cost_tax_status': {
        'en': 'Shipping Cost Tax Status',
        'ja': '送料課税状況',
    },
    'item_price_order': {
        'en': 'Item Price Order',
        'ja': '商品価格',
    },
    'subscription': {
        'en': 'Subscription',
        'ja': 'サブスクリプション',
    },
    'payment_link': {
        'en': 'Payment Link',
        'ja': '支払いリンク'
    },
    'payment_status': {
        'en': 'Payment Status',
        'ja': '支払い状況'
    },
    "warehouse_inventory_amount": {
        "en": "Warehouse Inventory Amount",
        "ja": "倉庫在庫数"
    },

    # Association
    'inventory_transactions': {
        'en': 'Inventory Transactions',
        'ja': '入出庫'
    },
    "invoice": {
        "en": "Invoice",
        "ja": "売上請求"
    },
    "estimate": {
        "en": "Estimate",
        "ja": "見積"
    },
    "owner": {
        "en": 'Owner',
        "ja": '所有者'
    }

}

ORDERS_TAX_APPLIED_TO_DISPLAY = {
    "all": {
        "en": "Items + Shipping Fee",
        "ja": "商品＋送料"
    },
    "only_items": {
        "en": "Only Items",
        "ja": "商品のみ"
    },
    "only_shipping": {
        "en": "Only Shipping",
        "ja": "送料のみ"
    },
}

ORDERS_STATUS_DISPLAY = {
    "active": {
        "en": "Active",
        "ja": "アクティブ"
    },
    "archived": {
        "en": "Archived",
        "ja": "アーカイブ"
    },
    "draft": {
        "en": "Draft",
        "ja": "下書き"
    },
    "order_received": {
        "en": "Order Received",
        "ja": "受注済み"
    },
    "order_delivered": {
        "en": "Order Fullfilled",
        "ja": "納品済み"
    },
    "invoice_created": {
        "en": "Invoice Sent",
        "ja": "請求済み"
    },
    "payment_received": {
        "en": "Payment Collected",
        "ja": "入金済み"
    },
    "cancellation_processed": {
        "en": "Cancellation Processed",
        "ja": "キャンセル済み"
    },

}

SEARCH_COLUMNS_DISPLAY = {
    "company__name": {
        "en": "Customer (Company) - Name",
        "ja": "顧客（企業） - 名前"
    },
    "company__address": {
        "en": "Customer (Company) - Address",
        "ja": "顧客（企業） - 住所"
    },
    "company__email": {
        "en": "Customer (Company) - Email",
        "ja": "顧客（企業） - メールアドレス"
    },
    "company__url": {
        "en": "Customer (Company) - Url",
        "ja": "顧客（企業） - URL"
    },
    "company__status": {
        "en": "Customer (Company) - Status",
        "ja": "顧客（企業) - ステータス"
    },


    "contact__name": {
        "en": "Customer (Contact) - Full Name",
        "ja": "顧客（連絡先） - 名前"
    },
    "contact__first_name": {
        "en": "Customer (Contact) - First Name",
        "ja": "顧客（連絡先） - 名前"
    },
    "contact__last_name": {
        "en": "Customer (Contact) - Last Name",
        "ja": "顧客（連絡先）- 苗字"
    },
    "contact__status": {
        "en": "Customer (Contact) - Status",
        "ja": "顧客（連絡先） - ステータス"
    },
    "contact__email": {
        "en": "Customer (Contact) - Email",
        "ja": "顧客（連絡先） - 電子メール"
    },
    "contact__phone_number": {
        "en": "Customer (Contact) - Phone Number",
        "ja": "顧客（連絡先） - 電話番号"
    },
    "contact__image_url": {
        "en": "Customer (Contact) - Image URL",
        "ja": "顧客（連絡先） - 画像URL"
    },
    "contact__company": {
        "en": "Customer (Contact) - Company",
        "ja": "顧客（連絡先） - 企業"
    },

    "company": {
        "en": "Customer (Company)",
        "ja": "顧客（企業)"
    },
    "contact": {
        "en": "Customer (Contact)",
        "ja": "顧客（連絡先)"
    },
    "items": {
        "en": "Items",
        "ja": "商品"
    },
}


CASE_STATUS_DISPLAY = {
    "todo": {
        "en": "TODO",
        "ja": "未着手"
    },
    "doing": {
        "en": "DOING",
        "ja": "進行中"
    },
    "done": {
        "en": "DONE",
        "ja": "完了"
    },
}

DEFAULT_ORDERS_TYPE_DISPLAY = {
    "default": {
        "en": "Default",
        "ja": "デフォルト",
        "value": "customer,delivery_status,line_item_name,total_price,total_price_without_tax,status,created_at,order_at,line_item_tax,line_item_price,order-tax"
    },
}

DEFAULT_SLIPS_TYPE_DISPLAY = {
    "sales_slips": {
        "en": "Sales Slips",
        "ja": "売上伝票",
        "value": "customers,input_item,start_date,send_from"
    },
    "withdraw_slips": {
        "en": "Withdraw Slips",
        "ja": "出金伝票",
        "value": "customers,input_item,start_date,send_from"
    },
    "deposit_slip": {
        "en": "Deposit Slip",
        "ja": "入金伝票",
        "value": "customers,input_item,start_date,send_from"
    },
    "transfer_slip": {
        "en": "Transfer Slip",
        "ja": "振替伝票",
        "value": "input_item,start_date,send_from"
    },
    "delivery_slips": {
        "en": "Delivery Slips",
        "ja": "出荷伝票",
        "value": "customers,input_item,start_date,send_from"
    },
}

DEFAULT_TAX_DISPLAY = {
    "unified_tax": {
        "en": "Based Tax",
        "ja": "ごとの課税",
    },
    "item_based_tax": {
        "en": "Item Based Tax",
        "ja": "商品ごとの課税",
    },
    "non_tax": {
        "en": "No Tax",
        "ja": "税金なし",
    },
}


SUBSCRIPTIONS_STATUS_DISPLAY = {
    "draft": {
        "en": "Draft",
        "ja": "下書き"
    },
    "active": {
        "en": "Active",
        "ja": "アクティブ"
    },
    "archived": {
        "en": "Archived",
        "ja": "アーカイブ"
    },
    "paused": {
        "en": "Paused",
        "ja": "停止"
    },
    "canceled": {
        "en": "Canceled",
        "ja": "キャンセル"
    },
    "expired": {
        "en": "Expired",
        "ja": "有効期限切れ"
    },
}


TASKS_STATUS_DISPLAY = {
    "todo": {
        "en": "To Do",
        "ja": "未着手"
    },
    "doing": {
        "en": "Doing",
        "ja": "進行中"
    },
    "done": {
        "en": "Done",
        "ja": "完了"
    }
}

TASK_HISTORY_STATUS_DISPLAY = {
    "pending": {
        "en": "Pending",
        "ja": "保留中"
    },
    "running": {
        "en": "Running",
        "ja": "実行中"
    },
    "completed": {
        "en": "Completed",
        "ja": "完了"
    },
    "canceled": {
        "en": "Canceled",
        "ja": "キャンセル"
    },
}

CONTACTS_COLUMNS_DISPLAY = {
    "contact_id": {
        "en": "ID",
        "ja": "ID"
    },
    "name": {
        "en": "Full Name",
        "ja": "姓名"
    },
    "first_name": {
        "en": "First Name",
        "ja": "名前"
    },
    "last_name": {
        "en": "Last Name",
        "ja": "苗字"
    },
    "location": {
        "en": "Location",
        "ja": "ロケーション"
    },
    "lists": {
        "en": "Contact Lists",
        "ja": "連絡先リスト"
    },
    "company": {
        "en": "Company",
        "ja": "企業"
    },
    # Removed "status" field
    "created_at": {
        "en": "Created At",
        "ja": "作成日時"
    },

    "updated_at": {
        "en": "Updated At",
        "ja": "更新日時"
    },

    "phone_number": {
        "en": "Phone Number",
        "ja": "電話番号"
    },

    "image_url": {
        "en": "Profile Picture",
        "ja": "プロフィール画像"
    },
    "email": {
        "en": "Email",
        "ja": "メールアドレス"
    },
    "url": {
        "en": "URL",
        "ja": "URL"
    },
    "customers__companies__name": {
        "en": "Customer (Company) - Name",
        "ja": "顧客（企業） - 名前"
    },

    "associate#company": {
        "en": "Company (Association)",
        "ja": "企業（アソシエーション）",
    },
    "image_file": {
        "en": "Contact Picture",
        "ja": "連絡先写真"
    },
    "contact_list": {
        "en": "Contact List",
        "ja": "連絡先リスト"
    }

}

CONVERSATIONS_COLUMNS_DISPLAY = {
    "usage_status": {
        "en": "Usage Status",
        "ja": "利用ステータス"
    },
    "status": {
        "en": "Status",
        "ja": "ステータス"
    },
    "channel": {
        "en": "Channel",
        "ja": "チャネル"
    },
    'last_message_body': {
        'en': 'Message Body',
        'ja': 'メッセージ本文'
    },
    'last_message_contact_name': {
        'en': 'Message Contact Name',
        'ja': 'メッセージ連絡先名'
    },
    'last_message_contact_email': {
        'en': 'Message Contact Email',
        'ja': 'メッセージ連絡先メール'
    },
    'channel__name': {
        'en': 'Channel Name',
        'ja': 'チャンネル名'
    }
}
CALENDAR_COLUMNS_DISPLAY = {
    "id": {
        "en": "ID",
        "ja": "ID"
    }
}
JOURNAL_COLUMNS_DISPLAY = {
    "usage_status": {
        "en": "Usage Status",
        "ja": "利用ステータス"
    },
    "counter_category": {
        "en": "Counter Category",
        "ja": "相手勘定科目"
    },
    "category": {
        "en": "Account",
        "ja": "勘定科目"
    },
    "tax_category": {
        "en": "Tax Category",
        "ja": "税カテゴリ"
    },
    "settle_choice": {
        "en": "Transaction Account",
        "ja": "勘定科目"
    },
    "settle_journal": {
        "en": "Settlement Status",
        "ja": "決済ステータス"
    },
    "notes": {
        "en": "Notes",
        "ja": "メモ"
    },
    "id": {
        "en": "ID",
        "ja": "ID"
    },
    "id_journal": {
        "en": "ID",
        "ja": "ID"
    },
    "transaction_date": {
        "en": "Date of Occurrence",
        "ja": "発生日"
    },
    "partner": {
        "en": "Partner",
        "ja": "取引先"
    },
    "partner_contact": {
        "en": "Partner (Contact)",
        "ja": "取引先 (連絡先)"
    },
    "partner_company": {
        "en": "Partner (Company)",
        "ja": "取引先 (企業)"
    },

    "contact_id": {
        "en": "Partner (Contact)",
        "ja": "取引先 (連絡先)"
    },
    "company_id": {
        "en": "Partner (Company)",
        "ja": "取引先 (企業)"
    },

    "amount": {
        "en": "Amount Without Tax",
        "ja": "税抜金額"
    },
    "amount_after_settlement": {
        "en": "Unsettled Amount",
        "ja": "未決済金額"
    },
    "currency": {
        "en": "Currency",
        "ja": "通貨"
    },
    "payment": {
        "en": "Expense",
        "ja": "経費"
    },
    "invoice": {
        "en": "Invoice",
        "ja": "請求"
    },
    "subscription": {
        "en": "Subscription",
        "ja": "契約"
    },
    "journalentry": {
        "en": "Journal Entries",
        "ja": "仕訳"
    },
    "settlement_date": {
        "en": "Settlement Date",
        "ja": "決済日"
    },
    "settlement_amount": {
        "en": "Settlement Amount",
        "ja": "決済額"
    },
    "settlement_account": {
        "en": "Settlement Account",
        "ja": "決済口座"
    },
    "tax_rate": {
        "en": "Tax Rate",
        "ja": "税率"
    },
    "amount_with_tax": {
        "en": "Amount With Tax",
        "ja": "税込金額"
    },
    "amount_credit_with_tax": {
        "en": "Amount Credit With Tax",
        "ja": "税金の金額"
    },
    "amount_credit": {
        "en": "Amount Credit",
        "ja": "貸方金額"
    },

    "amount_debit": {
        "en": "Amount Debit",
        "ja": "借方金額"
    },
    "created_at": {
        "en": "Created At",
        "ja": "作成日時"
    },
    "updated_at": {
        "en": "Updated At",
        "ja": "更新日時"
    },
    "due_date": {
        "en": "Due Date",
        "ja": "支払期限"
    },
    "owner": {
        "en": 'Owner',
        "ja": '所有者'
    },
    "line_transaction": {
        "en": 'Line Transaction',
        "ja": '明細取引'
    }
}

SETTLE_STATUS = [
    ('unsettled', False),
    ('settled', True),
]
KNOWLEDGE_COLUMNS_DISPLAY = {
    "knowledge_id": {
        "en": "ID",
        "ja": "ID"
    },
    "user": {
        "en": "Created By",
        "ja": "作成者"
    },
    "header": {
        "en": "Title",
        "ja": "タイトル"
    },
    "access": {
        "en": "Access",
        "ja": "アクセス"
    },
    "action": {
        "en": "Action",
        "ja": "アクション"
    },
}

FILEBOX_COLUMNS_DISPLAY = {
    "file_id": {
        "en": "ID",
        "ja": "ID"
    },
    "name": {
        "en": "Name",
        "ja": "名前"
    },
    "access": {
        "en": "Access",
        "ja": "アクセス"
    },
    "action": {
        "en": "Action",
        "ja": "アクション"
    },
    "updated_at": {
        "en": "Updated At",
        "ja": "更新日時"
    },
    "created_at": {
        "en": "Created At",
        "ja": "作成日時"
    },
}

EVENT_COLUMNS_DISPLAY = {
    'sanka_campaign_id': {
        "en": "ID",
        "ja": "ID"
    },
    'sanka_adgroup_id': {
        "en": "Ad Group ID",
        "ja": "広告グループID"
    },
    'sanka_ad_id': {
        "en": "Ad ID",
        "ja": "広告ID"
    },
    'sanka_channel': {
        "en": "Channel",
        "ja": "チャネル"
    },
    'url': {
        "en": "URL",
        "ja": "URL"
    },
    'fbclid': {
        "en": "Facebook Click ID",
        "ja": "Facebook Click ID"
    },
    'gclid': {
        "en": "Google Click ID",
        "ja": "Google Click ID"
    },
    'utm_id': {
        "en": "UTM ID",
        "ja": "UTM ID"
    },
    'utm_campaign': {
        "en": "UTM Campaign",
        "ja": "UTM Campaign"
    },
    'utm_source': {
        "en": "UTM Source",
        "ja": "UTM Source"
    },
    'utm_medium': {
        "en": "UTM Medium",
        "ja": "UTM Medium"
    },
    'utm_term': {
        "en": "UTM Term",
        "ja": "UTM Term"
    },
    'utm_content': {
        "en": "UTM Content",
        "ja": "UTM Content"
    },
    "updated_at": {
        "en": "Updated At",
        "ja": "更新日時"
    },
    "created_at": {
        "en": "Created At",
        "ja": "作成日時"
    },
}
JOBS_COLUMNS_DISPLAY = {
    "job_id": {
        "en": "ID",
        "ja": "ID"
    },
    "title": {
        "en": "Title",
        "ja": "タイトル"
    },
    "status": {
        "en": "Status",
        "ja": "ステータス"
    },
    "job_type": {
        "en": "Type",
        "ja": "タイプ"
    },
    "preview": {
        "en": "Preview",
        "ja": "プレビュー"
    },
    "description": {
        "en": "Description",
        "ja": "説明"
    },
    "applications": {
        "en": "Applications",
        "ja": "応募者"
    },
    "interview": {
        "en": "Interviews",
        "ja": "面接"
    },
    "scorecard": {
        "en": "Scorecards",
        "ja": "スコアカード"
    },
    "location": {
        "en": "Work Location",
        "ja": "勤務地"
    },
    "usage_status": {
        "en": "Usage Status",
        "ja": "利用ステータス"
    },
    "created_at": {
        "en": "Created At",
        "ja": "作成日時"
    }
}

JOB_STATUS_DISPLAY = {
    "hc_approval": {
        "en": "Headcount Approval",
        "ja": "求人応募承認"
    },
    "job_requisition": {
        "en": "Job Requisition",
        "ja": "求人情報作成"
    },
    "candidate_sourcing": {
        "en": "Candidate Sourcing",
        "ja": "求職者募集"
    },
    "interview_scheduling": {
        "en": "Interview Scheduling",
        "ja": "面接調整"
    },
    "job_offer": {
        "en": "Job Offer",
        "ja": "内定"
    },
    "onboarding": {
        "en": "Onboarding",
        "ja": "入社手続き"
    },
    "job_closing": {
        "en": "Job Closing",
        "ja": "求人終了"
    },
}

JOB_TYPE_DISPLAY = {
    "full_time": {
        "en": "Full Time Employee",
        "ja": "正社員"
    },
    "contractor": {
        "en": "Contractor",
        "ja": "契約社員"
    },
    "part_time": {
        "en": "Part Time",
        "ja": "パートタイム"
    },
    "intern": {
        "en": "Intern",
        "ja": "インターン"
    },
}

APPLICANT_DISPLAY_STATUS = {
    "listed": {
        "en": "Registered",
        "ja": "登録済み"
    },
    "contacted": {
        "en": "Contacted",
        "ja": "連絡済み"
    },
    "interview": {
        "en": "Interview",
        "ja": "面接"
    },
    "onboard": {
        "en": "Onboard",
        "ja": "入社手続き"
    },
    "all": {
        "en": "Total",
        "ja": "合計"
    }
}

JOBS_COLUMNS_DISPLAY = JOBS_COLUMNS_DISPLAY | JOB_STATUS_DISPLAY | JOB_TYPE_DISPLAY | APPLICANT_DISPLAY_STATUS

CONTRACT_COLUMNS_DISPLAY = {
    'contract_id': {
        "en": "ID",
        "ja": "ID"
    },
    "name": {
        "en": "Name",
        "ja": "名前"
    },
    'status': {
        "en": "Status",
        "ja": "ステータス"
    },
    'signer': {
        "en": "Signer",
        "ja": "署名者"
    },
    'file': {
        "en": "Original Document",
        "ja": "締結前の書類"
    },
    'signed_document': {
        "en": "Signed Document",
        "ja": "締結済みの書類"
    },
    "updated_at": {
        "en": "Updated At",
        "ja": "更新日時"
    },
    "created_at": {
        "en": "Date Created",
        "ja": "作成日時"
    },
    "usage_status": {
        "en": "Usage Status",
        "ja": "利用ステータス"
    },
    "owner": {
        "en": "Owner",
        "ja": "所有者",
    }
}

IT_COLUMNS_DISPLAY = {
    "it_id": {
        "en": "ID",
        "ja": "ID"
    },
    "name": {
        "en": "IT Name",
        "ja": "IT名"
    },
    "user": {
        "en": "User",
        "ja": "ユーザー"
    },
    "cost": {
        "en": "Cost",
        "ja": "料金"
    },
    "frequency": {
        "en": "Frequency",
        "ja": "頻度"
    },

    "os_type": {
        "en": "OS Type",
        "ja": "OSタイプ"
    },
    "os_version": {
        "en": "OS version",
        "ja": "OSバージョン"
    },
    "serial_number": {
        "en": "Serial Number",
        "ja": "シリアルナンバー"
    },
    "storage_volume": {
        "en": "Storage Volume",
        "ja": "ストレージ容量"
    },
    "memory": {
        "en": "Memory",
        "ja": "メモリ"
    },

    "it_type": {
        "en": "Type",
        "ja": "タイプ"
    },

    "created_at": {
        "en": "Created At",
        "ja": "作成日時"
    }
}

PORTAL_COLUMNS_DISPLAY = {
    "portal_id": {
        "en": "ID",
        "ja": "ID"
    },
    "name": {
        "en": "Name",
        "ja": "名前"
    },
    "description": {
        "en": "Description",
        "ja": "説明"
    },
    "members": {
        "en": "Members",
        "ja": "メンバー"
    },
    "feeds": {
        "en": "Feeds",
        "ja": "フィード"
    },
    "action": {
        "en": "Action",
        "ja": "アクション"
    },
    "design": {
        "en": "Design",
        "ja": "デザイン"
    },
    "created_at": {
        "en": "Created At",
        "ja": "作成日時"
    },
}

JOURNAL_ACCOUNT = [
    ('1', 'Cash'),
    ('2', 'Bank Account'),
    ('3', 'Employee Fund')
]
JOURNAL_TAX_CATEGORY = [
    ('10', 'Taxable Sales 10%'),
    ('8', 'Taxable Sales 8%'),
    ('0', 'Non Taxable')
]
JOURNAL_ACCOUNT_DISPLAY = {
    "1": {
        "en": "Cash",
        "ja": "現金"
    },
    "2": {
        "en": "Bank Account",
        "ja": "銀行口座"
    },
    "3": {
        "en": "Employee Fund",
        "ja": "従業員資金"
    }

}

BASIC_COLUMNS_DISPLAY = {
    "usage_status": {
        "en": "Usage Status",
        "ja": "利用ステータス"
    },
    "status": {
        "en": "Status",
        "ja": "ステータス"
    },
    "id": {
        "en": "ID",
        "ja": "ID"
    }
}

USER_MANAGEMENT_COLUMNS_DISPLAY = {
    "id_user": {
        "en": "ID",
        "ja": "ID"
    },
    "name": {
        "en": "Name",
        "ja": "名前"
    },
    "email": {
        "en": "Email Address",
        "ja": "メールアドレス"
    },
    "role": {
        "en": "Role",
        "ja": "役割"
    },
    "location": {
        "en": "Address",
        "ja": "住所"
    },
    "date_from": {
        "en": "Start Date",
        "ja": "就業開始日"
    },
    "date_to": {
        "en": "End Date",
        "ja": "就業終了日"
    },
    "registration_date": {
        "en": "Registration Date",
        "ja": "登録日"
    },
    "created_at": {
        "en": "Created At",
        "ja": "作成日時"
    },
    "updated_at": {
        "en": "Updated At",
        "ja": "更新日時"
    },
    "view_only_password": {
        "en": "Generated Password",
        "ja": "生成されたパスワード"
    }
}


WORKER_COLUMNS_DISPLAY = {
    "id_worker": {
        "en": "ID",
        "ja": "ID"
    },
    "name": {
        "en": "Name",
        "ja": "名前"
    },
    "user": {
        "en": "Sanka User",
        "ja": "Sankaユーザー"
    },
    "manager": {
        "en": "Manager",
        "ja": "マネージャー"
    },
    "team": {
        "en": "Team",
        "ja": "チーム"
    },
    "location": {
        "en": "Address",
        "ja": "住所"
    },
    "date_from": {
        "en": "Start Date",
        "ja": "就業開始日"
    },
    "date_to": {
        "en": "End Date",
        "ja": "就業終了日"
    },
    "email": {
        "en": "Email Address",
        "ja": "メールアドレス"
    },
    "status": {
        "en": "Status",
        "ja": "ステータス"
    },
    "usage_status": {
        "en": "Usage Status",
        "ja": "利用ステータス"
    },
    "sanka_user": {
        "en": "Sanka User",
        "ja": "Sankaユーザー"
    }
}

COMPANY_COLUMNS_DISPLAY = {
    'url': {
        'en': 'URL',
        'ja': 'URL'
    },
    "company_id": {
        "en": "ID",
        "ja": "ID"
    },
    "name": {
        "en": "Company Name",
        "ja": "企業名"
    },
    "address": {
        "en": "Company Address",
        "ja": "企業住所"
    },
    "email": {
        "en": "Email",
        "ja": "Eメール"
    },
    "phone_number": {
        "en": "Phone Number",
        "ja": "電話番号"
    },
    "image_file": {
        "en": "Company Picture",
        "ja": "企業写真"
    },
    "created_at": {
        "en": "Date Created",
        "ja": "作成日時"
    },
    "updated_at": {
        "en": "Updated At",
        "ja": "更新日時"
    },

    "lists": {
        "en": "Company Lists",
        "ja": "企業リスト"
    }
}

CAMPAIGN_COLUMNS_DISPLAY = [
    ('id_ads', 'ID'),
    ('name', 'Name'),
    ('account_id', 'Account ID'),
    ('account_name', 'Account Name'),
    ('platform', 'Platform'),
    ('final_url', 'Final URL'),
    ('budget', 'Budget'),
    ('budget_period', 'Budget Period'),
    ('currency', 'Currency'),
    ('period_started', 'Period Started'),
    ('period_ended', 'Period Ended'),
    ('average_cost', 'Average Cost'),
    ('average_cpc', 'Average CPC'),
    ('average_cpc', 'Average CPC'),
    ('average_cpe', 'Average CPE'),
    ('average_cpm', 'Average CPM'),
    ('ctr', 'CTR'),
    ('clicks', 'Clicks'),
    ('conversions', 'Conversions'),
    ('cost_per_conversion', 'Cost Per Conversion'),
    ('cost_micros', 'Cost Micros'),
    ('impressions', 'Impressions'),
    ('cost_per_result', 'Cost Per Result'),
    ('amount_spent', 'Amount Spent'),
    ('status', 'Status'),
    ('created_at', 'Created At'),
    ('orders', 'Orders'),
    ('revenue', 'Revenue'),
    ('signup', 'Signup'),
    ('ltv', 'LTV'),
    ('roas', 'ROAS'),
]

COLUMNS_CAPITALIZE_LAST_WORD = [
    'ctr',
    'average cpc',
    'average cpe',
    'average cpm',
    'ltv',
    'cvr',
    'url'
]

KANBAN_COLUMNS_DISPLAY = {
    "unlisted": {
        "en": "Others",
        "ja": "その他"
    },
}
ITEMS_COLUMNS_DISPLAY = {
    "name": {
        "en": "Item Name",
        "ja": "商品名"
    },
    "item_id": {
        "en": "ID",
        "ja": "ID"
    },
    "supplier": {
        "en": "Supplier",
        "ja": "仕入先"
    },
    "supplier__contact__name": {
        "en": "Supplier (Contact) - Name",
        "ja": "仕入先（連絡先） - 名前"
    },
    "supplier__company__name": {
        "en": "Supplier (Company) - Name",
        "ja": "仕入先（企業） - 名前"
    },
    "supplier__contact__first_name": {
        "en": "Supplier (Contact) - First Name",
        "ja": "仕入先（連絡先） - ファーストネーム"
    },
    "supplier__contact__last_name": {
        "en": "Supplier (Contact) - Last Name",
        "ja": "仕入先（連絡先）- 苗字"
    },
    "platform": {
        "en": "Platform",
        "ja": "プラットホーム"
    },
    "description": {
        "en": "Item Description",
        "ja": "商品説明"
    },
    "currency": {
        "en": "Sales Price Currency",
        "ja": "販売価格通貨"
    },
    "price": {
        "en": "Sales Price",
        "ja": "販売価格"
    },
    "tax": {
        "en": "Sales Price Tax Rate",
        "ja": "販売価格税率"
    },

    "purchase_price_currency": {
        "en": "Purchase Price Currency",
        "ja": "購買価格通貨"
    },
    "purchase_price": {
        "en": "Purchase Price",
        "ja": "購買価格"
    },
    "tax_purchase_price_of_items": {
        "en": "Purchase Price Tax Rate",
        "ja": "購買価格税率"
    },

    "status": {
        "en": "Usage Status",
        "ja": "利用ステータス"
    },
    "file": {
        "en": "File",
        "ja": "ファイル"
    },
    "created_at": {
        "en": "Created at",
        "ja": "作成日時"
    },
    "updated_at": {
        "en": "Updated at",
        "ja": "更新日時"
    },
    "owner": {
        "en": 'Owner',
        "ja": '所有者'
    },
    # added value from table mapping
    "price_of_items": {
        "en": "Sales Price",
        "ja": "販売価格"
    },
    "tax_price_of_items": {
        "en": "Item Tax Rate",
        "ja": "販売価格税率"
    },

    "purchase_price_of_items": {
        "en": "Purchase Price",
        "ja": "購買価格"
    },
    "variations": {
        "en": "Variations",
        "ja": "バリエーション"
    },
    "variations_sku": {
        "en": "Variations SKU",
        "ja": "バリエーションSKU"
    },
    "inventory__total_inventory": {
        "en": "Inventory Amount - Total",
        "ja": "在庫量 - 合計"
    },
    "inventory__available_amount": {
        "en": "Inventory Amount - Available",
        "ja": "在庫量 - 販売可能"
    },
    "inventory__unavailable_amount": {
        "en": "Inventory Amount - Unavailable",
        "ja": "在庫量 - 利用不可"
    },
    "inventory__committed_amount": {
        "en": "Inventory Amount - Committed",
        "ja": "在庫量 - 確定済み"
    },
    "total_inventory": {
        "en": "Inventory Amount - Total",
        "ja": "在庫量 - 合計"
    },
    "available_inventory_amount": {
        "en": "Inventory Amount - Available",
        "ja": "在庫量 - 販売可能"
    },
    "unavailable_inventory_amount": {
        "en": "Inventory Amount - Unavailable",
        "ja": "在庫量 - 利用不可"
    },
    "committed_inventory_amount": {
        "en": "Inventory Amount - Committed",
        "ja": "在庫量 - 確定済み"
    },
    "platform_ids": {
        "en": "Platform IDs",
        "ja": "プラットフォームID"
    },
    "active": {
        "en": "Active",
        "ja": "アクティブ"
    },
    "archived": {
        "en": "Archived",
        "ja": "アーカイブ"
    },
    "item_amount": {
        "en": "Item Amount",
        "ja": "商品数"
    },
    "warehouse_inventory_amount": {
        "en": "Warehouse Inventory Amount",
        "ja": "倉庫在庫数"
    },

}


COMPONENT_COLUMNS_DISPLAY = {
    "quantity": {
        "en": "Quantity",
        "ja": "数量"
    },
}

INVENTORY_COLUMNS_DISPLAY = {
    "inventory_id": {
        "en": "ID",
        "ja": "ID"
    },
    "transaction_id": {
        "en": "ID",
        "ja": "ID"
    },
    "transaction_type": {
        "en": "Transaction Type",
        "ja": "入出庫タイプ"
    },
    "transaction_date": {
        "en": "Transaction Date",
        "ja": "入出庫日"
    },
    "currency": {
        "en": "Currency",
        "ja": "通貨"
    },
    "item": {
        "en": "Item",
        "ja": "商品"
    },
    "items": {
        "en": "Item",
        "ja": "商品"
    },
    "item_id": {
        "en": "Item ID",
        "ja": "商品ID"
    },
    "inventory": {
        "en": "Inventory ID",
        "ja": "在庫ID"
    },
    "inventory_type": {
        "en": "Inventory Type",
        "ja": "在庫タイプ"
    },
    "price": {
        "en": "Unit Value",
        "ja": "単価"
    },
    "date": {
        "en": "Date",
        "ja": "日付"
    },
    "amount": {
        "en": "Amount",
        "ja": "数量"
    },
    "user": {
        "en": "Created By",
        "ja": "作成者"
    },
    "status": {
        "en": "Usage Status",
        "ja": "利用ステータス"
    },
    "inventory_status": {
        "en": "Inventory Status",
        "ja": "在庫ステータス"
    },
    "available": {
        "en": "Inventory - Available",
        "ja": "在庫量 -販売可能"
    },
    "committed": {
        "en": "Inventory - Committed",
        "ja": "在庫量 - 確定済み"
    },
    "unavailable": {
        "en": "Inventory - Unavailable",
        "ja": "在庫量 - 販売不可"
    },
    "total_inventory": {
        "en": "Inventory Amount",
        "ja": "在庫数"
    },
    "inventory_value": {
        "en": "Inventory Value",
        "ja": "在庫価値"
    },
    "updated_at": {
        "en": "Updated At",
        "ja": "更新日時"
    },
    "created_at": {
        "en": "Created At",
        "ja": "作成日時"
    },
    "location": {
        "en": "Location",
        "ja": "ロケーション"
    },
    "name": {
        "en": "Name",
        "ja": "名前"
    },
    "warehouse": {
        "en": "Location",
        "ja": "ロケーション"
    },
    "id_iw": {
        "en": "Location ID",
        "ja": "ロケーションID"
    },
    "id_iv": {
        "en": "ID",
        "ja": "ID"
    },
    "transaction_amount": {
        "en": "Inventory Quantity",
        "ja": "在庫数"
    },
    "item__name": {
        "en": "Item - Name",
        "ja": "商品- 名前"
    },
    "platform_ids": {
        "en": "Platform IDs",
        "ja": "プラットフォームID"
    },
    "active": {
        "en": "Active",
        "ja": "アクティブ"
    },
    "archived": {
        "en": "Archived",
        "ja": "アーカイブ"
    },
    "unit_price": {
        "en": "Unit Price",
        "ja": "単価",
    },
    "inventorytransaction__transaction_date": {
        "en": "Transaction Date",
        "ja": "入出庫日",
    },
    "initial_value": {
        "ja": "初期在庫数",
        "en": "Initial Inventory Amount",
    },
    "owner": {
        "en": 'Owner',
        "ja": '所有者'
    },
}
INVENTORY_TRANSACTION_COLUMNS_DISPLAY = {
    "inventory_id": {
        "en": "Inventory ID",
        "ja": "在庫ID"
    },
    "transaction_id": {
        "en": "ID",
        "ja": "ID"
    },
    "transaction_type": {
        "en": "Transaction Type",
        "ja": "入出庫タイプ"
    },
    "transaction_date": {
        "en": "Transaction Time",
        "ja": "入出庫日時"
    },
    "item": {
        "en": "Item",
        "ja": "商品"
    },
    "item_id": {
        "en": "Item ID",
        "ja": "商品ID"
    },
    "inventory": {
        "en": "Inventory",
        "ja": "在庫"
    },
    "inventory_type": {
        "en": "Inventory Type",
        "ja": "在庫タイプ"
    },
    "date": {
        "en": "Date",
        "ja": "日付"
    },
    "amount": {
        "en": "Transaction Amount",
        "ja": "入出庫数"
    },
    "price": {
        "en": "Unit Value",
        "ja": "単価"
    },
    "user": {
        "en": "Created By",
        "ja": "作成者"
    },
    "usage_status": {
        "en": "Usage Status",
        "ja": "利用ステータス"
    },
    "average_price": {
        "en": "Average Price",
        "ja": "平均価格"
    },
    "available": {
        "en": "Inventory - Available",
        "ja": "在庫量 - 販売可能"
    },
    "committed": {
        "en": "Inventory - Committed",
        "ja": "在庫量 - 確定済み"
    },
    "unavailable": {
        "en": "Inventory - Unavailable",
        "ja": "在庫量 - 販売不可"
    },
    "total_inventory": {
        "en": "Inventory Amount",
        "ja": "在庫数"
    },
    "inventory_value": {
        "en": "Inventory Value",
        "ja": "在庫価値"
    },
    "updated_at": {
        "en": "Updated At",
        "ja": "更新日時"
    },
    "created_at": {
        "en": "Created At",
        "ja": "作成日時"
    },
    "location": {
        "en": "Location",
        "ja": "ロケーション"
    },
    "name": {
        "en": "Name",
        "ja": "名前"
    },
    "warehouse": {
        "en": "Location",
        "ja": "ロケーション"
    },
    "id_iw": {
        "en": "ID",
        "ja": "ID"
    },
    "id_it": {
        "en": "ID",
        "ja": "ID"
    },
    "transaction_amount": {
        "en": "Inventory Quantity",
        "ja": "在庫数"
    },
    "total_price": {
        "en": "Total Price",
        "ja": "合計金額"
    },
    'in': {
        "ja": "入庫",
        "en": "Stock In",
    },
    'out': {
        "ja": '出庫',
        "en": 'Stock Out',
    },
    'adjust': {
        "ja": '調整',
        "en": 'Adjustment',
    },
    "owner": {
        "en": 'Owner',
        "ja": '所有者'
    },
    # Association
    "orders": {
        "en": "Orders",
        "ja": "受注"
    },
    "purchase_orders": {
        'en': 'Purchase Orders',
        'ja': '発注',
    },
}


SUBSCRIPTIONS_COLUMNS_DISPLAY = {
    "subscriptions_id": {
        "en": "ID",
        "ja": "ID"
    },
    "item_name": {
        "en": "Items",
        "ja": "商品"
    },
    "line_item": {
        'en': 'Line Item',
        'ja': '商品項目',
    },
    "durations": {
        "en": "Durations",
        "ja": "期間"
    },
    "contact": {
        "en": "Contact",
        "ja": "連絡先"
    },
    "company": {
        "en": "Company",
        "ja": "企業"
    },
    "contact_id": {
        "en": "Contact",
        "ja": "連絡先"
    },
    "company_id": {
        "en": "Company",
        "ja": "企業"
    },
    "customer": {
        "en": "Customer",
        "ja": "顧客"
    },
    'platform_display_name': {
        'en': 'Platform Name',
        'ja': 'プラットフォーム名',
    },
    "status": {
        "en": "Usage Status",
        "ja": "利用ステータス"
    },
    "subscription_status": {
        "en": "Status",
        "ja": "ステータス"
    },
    "price": {
        "en": "Price",
        "ja": "価格"
    },
    "quantity": {
        "en": "Quantity",
        "ja": "量"
    },
    "total_price_without_tax": {
        "en": "Total Price Without Tax",
        "ja": "税抜合計価格"
    },
    "total_price": {
        "en": "Total Price",
        "ja": "合計金額"
    },
    "created_at": {
        "en": "Created At",
        "ja": "作成日時"
    },
    "updated_at": {
        "en": "Updated At",
        "ja": "更新日時"
    },
    'prior_to_next': {
        'en': 'Prior To Next',
        'ja': '次回までの期間',
    },
    'prior_to_time': {
        'en': 'Prior To Time',
        'ja': '次回までの時間',
    },
    'shipping_cost_tax_status': {
        'en': 'Shipping Cost Tax Status',
        'ja': '送料課税状況',
    },
    "owner": {
        "en": 'Owner',
        "ja": '所有者'
    },


    # added value from table mapping,
    "start_durations": {
        "en": "Start Durations Date",
        "ja": "期間開始日"
    },
    "end_durations": {
        "en": "End Durations Date",
        "ja": "期間終了日"
    },
    "price_of_items": {
        "en": "Price Of Items",
        "ja": "商品の価格"
    },
    "frequency": {
        "en": "Billing Cycle Frequency",
        "ja": "請求サイクル頻度"
    },
    "frequency_time": {
        "en": "Billing Cycle Frequency - Unit",
        "ja": "請求サイクル頻度 - 単位"
    },
    "item_id": {
        "en": "Item ID",
        "ja": "商品ID"
    },
    "number_item": {
        "en": "Number of Items",
        "ja": "商品数"
    },
    "currency": {
        "en": "Currency",
        "ja": "通貨"
    },
    "tax": {
        "en": "Tax Rate",
        "ja": "税率"
    },
    'start_date': {
        'en': 'Start Date',
        'ja': '開始日',
    },
    'end_date': {
        'en': 'End Date',
        'ja': '終了日',
    },
    'last_ordered_date': {
        'en': 'Last Order Date',
        'ja': '最終注文日'
    },
    'last_billed_date': {
        'en': 'Last Billed Date',
        'ja': '最終請求日'
    },
    'next_order_date': {
        'en': 'Next Order Date',
        'ja': '次回注文日'
    },
    'next_bill_date': {
        'en': 'Next Billing Date',
        'ja': '次回請求日'
    },
    'line_item': {
        'en': 'Line Item',
        'ja': '商品項目',
    },

    # Stripe
    'subscription_id': {
        'en': 'Subscription ID',
        'ja': 'サブスクリプションID',
    },
    'platform_status': {
        'en': 'Status',
        'ja': 'ステータス',
    },
    'payment_id': {
        'en': 'Payment ID',
        'ja': '支払いID',
    },
    'payment_link': {
        'en': 'Payment Link',
        'ja': '支払いリンク',
    },
    'manage_payment_link': {
        'en': 'Manage Payment Link',
        'ja': '支払いリンクの管理',
    },
    'next_billing_price': {
        'en': 'Next Billing Price',
        'ja': '次回請求金額',
    },

    # Association
    "orders": {
        "en": "Orders",
        "ja": "受注"
    },
    "journalentry": {
        "en": "Journal Entry",
        "ja": "仕訳"
    },
    "journal_entry": {
        "en": "Journal Entry",
        "ja": "仕訳"
    },
    'invoices': {
        "en": "Invoices",
        "ja": "売上請求",
    }
}
SUBSCRIPTIONS_COLUMNS_DISPLAY = SUBSCRIPTIONS_COLUMNS_DISPLAY | SUBSCRIPTIONS_STATUS_DISPLAY

DEALS_COLUMNS_DISPLAY = {
    "deal_id": {
        "en": "ID",
        "ja": "ID"
    },
    "name": {
        "en": "Case Name",
        "ja": "案件名"
    },
    "contact": {
        "en": "Contact",
        "ja": "連絡先"
    },
    "company": {
        "en": "Company",
        "ja": "企業"
    },
    "contact_id": {  # import csv mapping
        "en": "Contact",
        "ja": "連絡先"
    },
    "company_id": {  # import csv mapping
        "en": "Company",
        "ja": "企業"
    },

    "status": {
        "en": "Usage Status",
        "ja": "利用ステータス"
    },
    "updated_at": {
        "en": "Updated At",
        "ja": "更新日時"
    },
    "created_at": {
        "en": "Date Created",
        "ja": "作成日時"
    },
    "customer": {
        "en": "Customer",
        "ja": "顧客"
    },
    "case_status": {
        "en": "Case Status",
        "ja": "案件ステータス"
    },
    'line_item': {
        'en': 'Line Item',
        'ja': '商品項目',
    },
    "estimates": {
        "en": "Estimates",
        "ja": "見積"
    },
    "invoices": {
        "en": "Invoices",
        "ja": "売上請求"
    },
    "tasks": {
        "en": "Tasks",
        "ja": "タスク"
    },
    "active": {
        "en": "Active",
        "ja": "アクティブ"
    },
    "archived": {
        "en": "Archived",
        "ja": "アーカイブ"
    },
    "owner": {
        "en": 'Owner',
        "ja": '所有者'
    },
}
DEALS_COLUMNS_DISPLAY = DEALS_COLUMNS_DISPLAY | CASE_STATUS_DISPLAY
PANEL_COLUMNS_DISPLAY = {
    "panel_id": {
        "en": "ID",
        "ja": "ID"
    },
    "name": {
        "en": "Report Name",
        "ja": "レポート名"
    },
    "panel_type": {
        "en": "Report Type",
        "ja": "レポートタイプ"
    },
    "is_deleted": {
        "en": "Usage Status",
        "ja": "利用ステータス"
    }
}

CHART_TYPE_NAME = {
    'followers': "Followers",
    'twitter_followers': "Followers",
    'following': "Followings",
    'number_of_posts': "Total Post",
    'impressions': "Impressions",
    'retweets': "Total Retweets",
    'likes': "Likes",
    'clicks': "Clicks",
    'engagement': "Engagements",
    'posts': "Post Performance",
    'replies': "Reply Performance",
    'floor_price': "Floor Price",
    'floor_cap': "Floor Cap",
    'token_owner': "Token Owner",
    'num_members': "Number of Members",

    'num_visitors': "Number of Visitors",
    'num_communicators': "Number of Communicators",
    'num_active_members': "Number of Active Members",

}

CHART_CHANNEL_STAT = ['followers', 'twitter_followers', 'following',
                      'floor_cap', 'floor_price', 'num_members', 'num_visitors', 'num_communicators']
CHART_POST_STAT = ['number_of_posts', 'impressions', 'retweets',
                   'likes', 'engagement', 'clicks', 'num_active_members']

BG_COLOR = ['rgba(79, 201, 218, 0.2)', 'rgba(247, 35, 125, 0.2)',
            'rgba(33, 191, 25, 0.2)', 'rgba(220, 220, 8, 0.2)', ]
BG_COLOR_BORDER = ['rgba(79, 201, 218, 1)', 'rgba(247, 35, 125, 1)',
                   'rgba(33, 191, 25, 1)', 'rgba(220, 220, 8, 1)', ]


GOOGLE_BASE_SCOPE = 'https://www.googleapis.com/auth/userinfo.email'
GOOGLE_API_URL = "https://www.googleapis.com/oauth2/v3/userinfo?alt=json&access_token={}"
GOOGLE_SCOPES = {
    "gmail": ['https://mail.google.com/'],
    "g-analytics": ['https://www.googleapis.com/auth/analytics'],
    "g-search-console": ['https://www.googleapis.com/auth/webmasters'],
    "g-spreadsheet":  ['https://www.googleapis.com/auth/spreadsheets'],
    "google-workspace": ['https://www.googleapis.com/auth/admin.directory.user'],
    "g-calendar": ['https://www.googleapis.com/auth/calendar'],
    "gdrive": ['https://www.googleapis.com/auth/drive'],
    "gmb": ['https://www.googleapis.com/auth/business.manage']
}

TYPE_TO_RULE = {
    'choice': [
        'includes',
        'includes_all',
        'excludes',
        'is_empty',
        'is_not_empty',
    ],
    'string': [
        'is',
        'is_not',
        'contains',
        'does_not_contain',
        'starts_with',
        'ends_with',
        'is_empty',
        'is_not_empty',
        'includes',
        'excludes',
    ],
    'person': [
        'contains',
        'does_not_contain',
        'is_empty',
        'is_not_empty',
    ],
    'project': [
        'contains',
        'does_not_contain',
        'is_empty',
        'is_not_empty',
    ],
    'date': [
        'start_date',
        'end_date',
        'date_range',
        'more_than_x_days',
    ],
    'date_time': [
        'start_date',
        'end_date',
        'date_range',
        'more_than_x_days',
    ],
    'date_range': [
        'start_date',
        'end_date',
        'date_range',
        'more_than_x_days',
    ],
    'number': [
        'less_than',
        'less_than_equal',
        'equal',
        'greater_than',
        'greater_than_equal',
        'between',
    ],
    'invoices': [
        'draft',
        'paid',
        'sent'
    ],
    'estimates': [
        'draft',
        'sent'
    ],
    'expenses': [
        'draft',
        'submitted',
        'approved',
        'reimbursed'
    ],
    'frequency': [
        'one_time',
        'recurring'
    ],
    'id': [
        'is',
        'csv_contains'
    ],
    'object': [
        'is_empty',
        'is_not_empty',
    ],
    'warehouse': [
        'is',
        'is_not',
        'contains',
        'does_not_contain',
        'is_empty',
        'is_not_empty',
        'includes',
        'excludes',
    ],
    'integration_choice': [
        'is',
        'is_not_empty',
        'is_empty',
    ]
}


GLOBAL_CURRENCY = [
    {
        'slug': 'AED',
        'display': 'AED (د.إ)'
    },
    {
        'slug': 'AFN',
        'display': 'AFN (؋)'
    },
    {
        'slug': 'ALL',
        'display': 'ALL (L)'
    },
    {
        'slug': 'AMD',
        'display': 'AMD (֏)'
    },
    {
        'slug': 'ANG',
        'display': 'ANG (ƒ)'
    },
    {
        'slug': 'AOA',
        'display': 'AOA (Kz)'
    },
    {
        'slug': 'ARS',
        'display': 'ARS ($)'
    },
    {
        'slug': 'AUD',
        'display': 'AUD ($)'
    },
    {
        'slug': 'AWG',
        'display': 'AWG (ƒ)'
    },
    {
        'slug': 'AZN',
        'display': 'AZN (₼)'
    },
    {
        'slug': 'BAM',
        'display': 'BAM (KM)'
    },
    {
        'slug': 'BBD',
        'display': 'BBD ($)'
    },
    {
        'slug': 'BDT',
        'display': 'BDT (৳)'
    },
    {
        'slug': 'BGN',
        'display': 'BGN (лв)'
    },
    {
        'slug': 'BHD',
        'display': 'BHD (.د.ب)'
    },
    {
        'slug': 'BIF',
        'display': 'BIF (FBu)'
    },
    {
        'slug': 'BMD',
        'display': 'BMD ($)'
    },
    {
        'slug': 'BND',
        'display': 'BND ($)'
    },
    {
        'slug': 'BOB',
        'display': 'BOB ($b)'
    },
    {
        'slug': 'BRL',
        'display': 'BRL (R$)'
    },
    {
        'slug': 'BSD',
        'display': 'BSD ($)'
    },
    {
        'slug': 'BTC',
        'display': 'BTC (฿)'
    },
    {
        'slug': 'BTN',
        'display': 'BTN (Nu.)'
    },
    {
        'slug': 'BWP',
        'display': 'BWP (P)'
    },
    {
        'slug': 'BYR',
        'display': 'BYR (Br)'
    },
    {
        'slug': 'BYN',
        'display': 'BYN (Br)'
    },
    {
        'slug': 'BZD',
        'display': 'BZD (BZ$)'
    },
    {
        'slug': 'CAD',
        'display': 'CAD ($)'
    },
    {
        'slug': 'CDF',
        'display': 'CDF (FC)'
    },
    {
        'slug': 'CHF',
        'display': 'CHF (CHF)'
    },
    {
        'slug': 'CLP',
        'display': 'CLP ($)'
    },
    {
        'slug': 'CNY',
        'display': 'CNY (¥)'
    },
    {
        'slug': 'COP',
        'display': 'COP ($)'
    },
    {
        'slug': 'CRC',
        'display': 'CRC (₡)'
    },
    {
        'slug': 'CUC',
        'display': 'CUC ($)'
    },
    {
        'slug': 'CUP',
        'display': 'CUP (₱)'
    },
    {
        'slug': 'CVE',
        'display': 'CVE ($)'
    },
    {
        'slug': 'CZK',
        'display': 'CZK (Kč)'
    },
    {
        'slug': 'DJF',
        'display': 'DJF (Fdj)'
    },
    {
        'slug': 'DKK',
        'display': 'DKK (kr)'
    },
    {
        'slug': 'DOP',
        'display': 'DOP (RD$)'
    },
    {
        'slug': 'DZD',
        'display': 'DZD (دج)'
    },
    {
        'slug': 'EEK',
        'display': 'EEK (kr)'
    },
    {
        'slug': 'EGP',
        'display': 'EGP (£)'
    },
    {
        'slug': 'ERN',
        'display': 'ERN (Nfk)'
    },
    {
        'slug': 'ETB',
        'display': 'ETB (Br)'
    },
    {
        'slug': 'ETH',
        'display': 'ETH (Ξ)'
    },
    {
        'slug': 'EUR',
        'display': 'EUR (€)'
    },
    {
        'slug': 'FJD',
        'display': 'FJD ($)'
    },
    {
        'slug': 'FKP',
        'display': 'FKP (£)'
    },
    {
        'slug': 'GBP',
        'display': 'GBP (£)'
    },
    {
        'slug': 'GEL',
        'display': 'GEL (₾)'
    },
    {
        'slug': 'GGP',
        'display': 'GGP (£)'
    },
    {
        'slug': 'GHC',
        'display': 'GHC (₵)'
    },
    {
        'slug': 'GHS',
        'display': 'GHS (GH₵)'
    },
    {
        'slug': 'GIP',
        'display': 'GIP (£)'
    },
    {
        'slug': 'GMD',
        'display': 'GMD (D)'
    },
    {
        'slug': 'GNF',
        'display': 'GNF (FG)'
    },
    {
        'slug': 'GTQ',
        'display': 'GTQ (Q)'
    },
    {
        'slug': 'GYD',
        'display': 'GYD ($)'
    },
    {
        'slug': 'HKD',
        'display': 'HKD ($)'
    },
    {
        'slug': 'HNL',
        'display': 'HNL (L)'
    },
    {
        'slug': 'HRK',
        'display': 'HRK (kn)'
    },
    {
        'slug': 'HTG',
        'display': 'HTG (G)'
    },
    {
        'slug': 'HUF',
        'display': 'HUF (Ft)'
    },
    {
        'slug': 'IDR',
        'display': 'IDR (Rp)'
    },
    {
        'slug': 'ILS',
        'display': 'ILS (₪)'
    },
    {
        'slug': 'IMP',
        'display': 'IMP (£)'
    },
    {
        'slug': 'INR',
        'display': 'INR (₹)'
    },
    {
        'slug': 'IQD',
        'display': 'IQD (ع.د)'
    },
    {
        'slug': 'IRR',
        'display': 'IRR (﷼)'
    },
    {
        'slug': 'ISK',
        'display': 'ISK (kr)'
    },
    {
        'slug': 'JEP',
        'display': 'JEP (£)'
    },
    {
        'slug': 'JMD',
        'display': 'JMD (J$)'
    },
    {
        'slug': 'JOD',
        'display': 'JOD (JD)'
    },
    {
        'slug': 'JPY',
        'display': 'JPY (¥)'
    },
    {
        'slug': 'KES',
        'display': 'KES (KSh)'
    },
    {
        'slug': 'KGS',
        'display': 'KGS (лв)'
    },
    {
        'slug': 'KHR',
        'display': 'KHR (៛)'
    },
    {
        'slug': 'KMF',
        'display': 'KMF (CF)'
    },
    {
        'slug': 'KPW',
        'display': 'KPW (₩)'
    },
    {
        'slug': 'KRW',
        'display': 'KRW (₩)'
    },
    {
        'slug': 'KWD',
        'display': 'KWD (KD)'
    },
    {
        'slug': 'KYD',
        'display': 'KYD ($)'
    },
    {
        'slug': 'KZT',
        'display': 'KZT (₸)'
    },
    {
        'slug': 'LAK',
        'display': 'LAK (₭)'
    },
    {
        'slug': 'LBP',
        'display': 'LBP (£)'
    },
    {
        'slug': 'LKR',
        'display': 'LKR (₨)'
    },
    {
        'slug': 'LRD',
        'display': 'LRD ($)'
    },
    {
        'slug': 'LSL',
        'display': 'LSL (M)'
    },
    {
        'slug': 'LTC',
        'display': 'LTC (Ł)'
    },
    {
        'slug': 'LTL',
        'display': 'LTL (Lt)'
    },
    {
        'slug': 'LVL',
        'display': 'LVL (Ls)'
    },
    {
        'slug': 'LYD',
        'display': 'LYD (LD)'
    },
    {
        'slug': 'MAD',
        'display': 'MAD (MAD)'
    },
    {
        'slug': 'MDL',
        'display': 'MDL (lei)'
    },
    {
        'slug': 'MGA',
        'display': 'MGA (Ar)'
    },
    {
        'slug': 'MKD',
        'display': 'MKD (ден)'
    },
    {
        'slug': 'MMK',
        'display': 'MMK (K)'
    },
    {
        'slug': 'MNT',
        'display': 'MNT (₮)'
    },
    {
        'slug': 'MOP',
        'display': 'MOP (MOP$)'
    },
    {
        'slug': 'MRO',
        'display': 'MRO (UM)'
    },
    {
        'slug': 'MRU',
        'display': 'MRU (UM)'
    },
    {
        'slug': 'MUR',
        'display': 'MUR (₨)'
    },
    {
        'slug': 'MVR',
        'display': 'MVR (Rf)'
    },
    {
        'slug': 'MWK',
        'display': 'MWK (MK)'
    },
    {
        'slug': 'MXN',
        'display': 'MXN ($)'
    },
    {
        'slug': 'MYR',
        'display': 'MYR (RM)'
    },
    {
        'slug': 'MZN',
        'display': 'MZN (MT)'
    },
    {
        'slug': 'NAD',
        'display': 'NAD ($)'
    },
    {
        'slug': 'NGN',
        'display': 'NGN (₦)'
    },
    {
        'slug': 'NIO',
        'display': 'NIO (C$)'
    },
    {
        'slug': 'NOK',
        'display': 'NOK (kr)'
    },
    {
        'slug': 'NPR',
        'display': 'NPR (₨)'
    },
    {
        'slug': 'NZD',
        'display': 'NZD ($)'
    },
    {
        'slug': 'OMR',
        'display': 'OMR (﷼)'
    },
    {
        'slug': 'PAB',
        'display': 'PAB (B/.)'
    },
    {
        'slug': 'PEN',
        'display': 'PEN (S/.)'
    },
    {
        'slug': 'PGK',
        'display': 'PGK (K)'
    },
    {
        'slug': 'PHP',
        'display': 'PHP (₱)'
    },
    {
        'slug': 'PKR',
        'display': 'PKR (₨)'
    },
    {
        'slug': 'PLN',
        'display': 'PLN (zł)'
    },
    {
        'slug': 'PYG',
        'display': 'PYG (Gs)'
    },
    {
        'slug': 'QAR',
        'display': 'QAR (﷼)'
    },
    {
        'slug': 'RMB',
        'display': 'RMB (￥)'
    },
    {
        'slug': 'RON',
        'display': 'RON (lei)'
    },
    {
        'slug': 'RSD',
        'display': 'RSD (Дин.)'
    },
    {
        'slug': 'RUB',
        'display': 'RUB (₽)'
    },
    {
        'slug': 'RWF',
        'display': 'RWF (R₣)'
    },
    {
        'slug': 'SAR',
        'display': 'SAR (﷼)'
    },
    {
        'slug': 'SBD',
        'display': 'SBD ($)'
    },
    {
        'slug': 'SCR',
        'display': 'SCR (₨)'
    },
    {
        'slug': 'SDG',
        'display': 'SDG (ج.س.)'
    },
    {
        'slug': 'SEK',
        'display': 'SEK (kr)'
    },
    {
        'slug': 'SGD',
        'display': 'SGD ($)'
    },
    {
        'slug': 'SHP',
        'display': 'SHP (£)'
    },
    {
        'slug': 'SLL',
        'display': 'SLL (Le)'
    },
    {
        'slug': 'SOS',
        'display': 'SOS (S)'
    },
    {
        'slug': 'SRD',
        'display': 'SRD ($)'
    },
    {
        'slug': 'SSP',
        'display': 'SSP (£)'
    },
    {
        'slug': 'STD',
        'display': 'STD (Db)'
    },
    {
        'slug': 'STN',
        'display': 'STN (Db)'
    },
    {
        'slug': 'SVC',
        'display': 'SVC ($)'
    },
    {
        'slug': 'SYP',
        'display': 'SYP (£)'
    },
    {
        'slug': 'SZL',
        'display': 'SZL (E)'
    },
    {
        'slug': 'THB',
        'display': 'THB (฿)'
    },
    {
        'slug': 'TJS',
        'display': 'TJS (SM)'
    },
    {
        'slug': 'TMT',
        'display': 'TMT (T)'
    },
    {
        'slug': 'TND',
        'display': 'TND (د.ت)'
    },
    {
        'slug': 'TOP',
        'display': 'TOP (T$)'
    },
    {
        'slug': 'TRL',
        'display': 'TRL (₤)'
    },
    {
        'slug': 'TRY',
        'display': 'TRY (₺)'
    },
    {
        'slug': 'TTD',
        'display': 'TTD (TT$)'
    },
    {
        'slug': 'TVD',
        'display': 'TVD ($)'
    },
    {
        'slug': 'TWD',
        'display': 'TWD (NT$)'
    },
    {
        'slug': 'TZS',
        'display': 'TZS (TSh)'
    },
    {
        'slug': 'UAH',
        'display': 'UAH (₴)'
    },
    {
        'slug': 'UGX',
        'display': 'UGX (USh)'
    },
    {
        'slug': 'USD',
        'display': 'USD ($)'
    },
    {
        'slug': 'UYU',
        'display': 'UYU ($U)'
    },
    {
        'slug': 'UZS',
        'display': 'UZS (лв)'
    },
    {
        'slug': 'VEF',
        'display': 'VEF (Bs)'
    },
    {
        'slug': 'VND',
        'display': 'VND (₫)'
    },
    {
        'slug': 'VUV',
        'display': 'VUV (VT)'
    },
    {
        'slug': 'WST',
        'display': 'WST (WS$)'
    },
    {
        'slug': 'XAF',
        'display': 'XAF (FCFA)'
    },
    {
        'slug': 'XBT',
        'display': 'XBT (Ƀ)'
    },
    {
        'slug': 'XCD',
        'display': 'XCD ($)'
    },
    {
        'slug': 'XOF',
        'display': 'XOF (CFA)'
    },
    {
        'slug': 'XPF',
        'display': 'XPF (₣)'
    },
    {
        'slug': 'YER',
        'display': 'YER (﷼)'
    },
    {
        'slug': 'ZAR',
        'display': 'ZAR (R)'
    },
    {
        'slug': 'ZWD',
        'display': 'ZWD (Z$)'
    }
]

THREAD_STATUS_CHOICES = [
    ('todo', 'To Do'),
    ('doing', 'Doing'),
    ('done', 'Done'),
]

THREAD_STATUS_DISPLAY = {
    "todo": {
        "en": "To Do",
        "ja": "予定"
    },
    "doing": {
        "en": "Doing",
        "ja": "対応中"
    },
    "done": {
        "en": "Done",
        "ja": "完了"
    }
}
PURCHASE_ORDER_STATUS_DISPLAY = {
    "draft": {
        "en": "Draft",
        "ja": "下書き"
    },
    "sent": {
        "en": "Sent",
        "ja": "送信済み"
    },
    "internal_approved": {
        "en": "Internal Approved",
        "ja": "内部承認済み"
    },
    "supplier_approved": {
        "en": "Approved by Supplier",
        "ja": "仕入先承認済み"
    },
    "in_stock": {
        "en": "Stocked In",
        "ja": "入庫済み"
    },
    "paid": {
        "en": "Paid",
        "ja": "支払済み"
    }
}


OBJECTS_CF_TYPE = [
    ('text', 'Text'),
    ('text-area', 'Text Area'),
    ('choice', 'Choice'),
    ('hierarchy_choice', 'Hierarchical Choice'),
    ('tag', 'Tag'),
    ('number', 'Number'),
    ('image', 'Image'),
    ('image_group', 'Image Group'),
    ('file', 'File'),
    ('date', 'Date'),
    ('date_time', 'Date Time'),
    ('date_range', 'Date Range'),
    ('user', 'User'),
    ('formula', 'Formula'),
    ('bill_objects', 'Bill Object'),
    ('invoice_objects', 'Invoice Object'),
    ('order_objects', 'Order Object'),
    ('contact', 'Contact Object'),
    ('company', 'Company Object'),
    ('customer', 'Customer Object'),
    ('purchase_order', 'Purchase Order Object'),
    ('subscription', 'Subscription Object'),
    ('warehouse_objects', 'Warehouse Object'),
    ('task', 'Task Object'),
    ('user_management', 'User Management Object'),
    ('billing_cycle', 'Billing Cycle'),
    ('phone', 'Phone Number'),
    ('price-table', 'Price Table'),
    ('shipping_info', 'Shipping Info'),
    ('property_sync', 'Property Sync'),
    ('language', 'Language'),
    ('currency', 'Currency'),
    ('point', 'Point'),
]

CONTACT_CF_TYPE = OBJECTS_CF_TYPE + [
    ('contact_list', 'Contact List'),
]

INVENTORY_CF_TYPE = OBJECTS_CF_TYPE + [
    ('components', 'Components'),
]

SHOPTURBO_NUMBER_FORMAT = [
    ('number', 'Number'),
    ('%', '%'),
    # Convert 3 tuples from currency_model to 2 tuples (Follow django choice rule))
    *[(currency[0].lower(), currency[0].upper()) for currency in CURRENCY_MODEL]
]

# Temporary Set
KEYWORD_SEED_CATEGORY = ['AI', 'beauty', 'company', 'concept', 'design', 'eco', 'ecommerce', 'education', 'fashion', 'finance', 'fitness', 'food', 'gaming', 'health', 'home',
                         'lifestyle', 'luxury', 'marketing', 'media', 'pets', 'product', 'sales', 'science', 'social', 'software', 'sports', 'startup', 'technology', 'travel']
KEYWORD_SEED_CATEGORY_JP = ['AI', 'ビューティー', '会社', 'コンセプト', 'デザイン', 'エコ', 'Eコマース', '教育', 'ファッション', '金融', 'フィットネス', 'フード', 'ゲーム', '健康', '家庭',
                            'ライフスタイル', '高級', 'マーケティング', 'メディア', 'ペット', '製品', '販売', '科学', 'ソーシャル', 'ソフトウェア', 'スポーツ', 'スタートアップ', 'テクノロジー', '旅行']


ESTIMATE_STATUS = [
    ('draft', 'Draft'),
    ('sent', 'Sent'),
    ('approved', 'Approved'),
    ('order_received', 'Order Received'),
    ('paid', 'Paid'),
]
COMMERCE_STATUS = [
    ('draft', 'Draft'),
    ('sent', 'Sent'),
    ('scheduled', 'Scheduled'),
    ('paid', 'Paid'),
]


BILLS_TAX_OPTION = [
    ('unified_tax', 'Unified Tax'),
    ('item_based_tax', 'Item Based Tax'),
    ('non_tax', 'No Tax'),
]

COMMERCE_STATUS_DISPLAY = {
    "draft": {
        "en": "Draft",
        "ja": "下書き",
    },
    "sent": {
        "en": "Sent",
        "ja": "送信済み",
    },
    "scheduled": {
        "en": "Scheduled",
        "ja": "スケジュール済み",
    },
    "paid": {
        "en": "Paid",
        "ja": "支払済み",
    }
}
EXPENSE_STATUS_DISPLAY = {
    "draft": {
        "en": "Draft",
        "ja": "下書き",
    },
    "submitted": {
        "en": "Submitted",
        "ja": "提出済み",
    },
    "approved": {
        "en": "Approved",
        "ja": "承認済み",
    },
    "reimbursed": {
        "en": "Reimbursed",
        "ja": "払い戻し済み",
    }
}
BILL_STATUS_DISPLAY = {
    "draft": {
        "en": "Draft",
        "ja": "下書き",
    },
    "received": {
        "en": "Received",
        "ja": "受領済み",
    },
    "approved": {
        "en": "Approved",
        "ja": "承認済み",
    },
    "paid": {
        "en": "Paid",
        "ja": "支払い済み",
    }
}
ESTIMATE_STATUS_DISPLAY = {
    "draft": {
        "en": "Draft",
        "ja": "下書き",
    },
    "sent": {
        "en": "Sent",
        "ja": "送信済み",
    },
    "approved": {
        "en": "Approved",
        "ja": "承認済み",
    },
    "order_received": {
        "en": "Ordered",
        "ja": "受注",
    },
    "paid": {
        "en": "Paid",
        "ja": "支払済み",
    },
}

ASSOCIATE_COLUMNS_DISPLAY = {
    "associate#commerce_orders": {
        "en": "Order (Association)",
        "ja": "受注（アソシエーション）",
    },
    "associate#invoices": {
        "en": "Invoice (Association)",
        "ja": "請求書（アソシエーション）",
    },
    "associate#customer_case": {
        "en": "Case (Association)",
        "ja": "案件（アソシエーション）",
    },
    "associate#estimates": {
        "en": "Estimate (Association)",
        "ja": "見積（アソシエーション）",
    },
    "associate#receipts": {
        "en": "Payment (Association)",
        "ja": "入金（アソシエーション）",
    },
    "associate#purchase_orders": {
        "en": "Purchase Order (Association)",
        "ja": "発注（アソシエーション）",
    },
}

INVOICE_COLUMNS_DISPLAY = {
    "input_item": {
        "en": "Input Items",
        "ja": "入力アイテム"
    },
    "customers": {
        "en": "Customer",
        "ja": "顧客"
    },
    "customers__contact__name": {
        "en": "Customer (Contact) - Name",
        "ja": "顧客（連絡先） - 名前"
    },
    "customers__company__name": {
        "en": "Customer (Company) - Name",
        "ja": "顧客（企業） - 名前"
    },
    "customers__contact__first_name": {
        "en": "Customer (Contact) - First Name",
        "ja": "顧客（連絡先） - 名前"
    },
    "customers__contact__last_name": {
        "en": "Customer (Contact) - Last Name",
        "ja": "顧客（連絡先）- 苗字"
    },
    "contact_id": {
        "en": "Contact ID",
        "ja": "連絡先ID",
    },
    "company_id": {
        "en": "Company ID",
        "ja": "企業ID"
    },
    "id_inv": {
        "en": "ID",
        "ja": "ID"
    },
    "start_date": {
        "en": "Issuing Date",
        "ja": "発行日"
    },
    "due_date": {
        "en": "Due Date",
        "ja": "支払期日"
    },
    "date_received": {
        "en": "Received Date",
        "ja": "受領日"
    },
    "status": {
        "en": "Status",
        "ja": "ステータス"
    },
    "total_price": {
        "en": "Total Price",
        "ja": "合計金額"
    },
    "total_price_without_tax": {
        "en": "Total Price Without Tax",
        "ja": "税抜合計価格"
    },
    "usage_status": {
        "en": "Usage Status",
        "ja": "利用ステータス"
    },
    "currency": {
        "en": "Currency",
        "ja": "通貨",
    },
    "item": {
        "en": "Line Items",
        "ja": "商品項目",
    },
    "item_id": {
        "en": "Item ID",
        "ja": "商品ID",
    },
    "item_name": {
        "en": "Item Name",
        "ja": "商品名",
    },
    "item_price": {
        "en": "Item Price",
        "ja": "商品価格",
    },
    "item_amount": {
        "en": "Amount",
        "ja": "数量",
    },
    "notes": {
        "en": "Notes",
        "ja": "備考欄",
    },
    "send_from": {
        "en": "Send From",
        "ja": "送信元",
    },
    "email": {
        "en": "Email",
        "ja": "Eメール",
    },
    "tax_item_rate": {
        "en": "Item Tax Rate",
        "ja": "商品税率",
    },
    "tax_rate": {
        "en": "Tax Rate (%)",
        "ja": "税率 (%)",
    },
    "tax_option": {
        "en": "Tax Option",
        "ja": "税オプション",
    },
    "tax_inclusive": {
        "en": "Tax Inclusive",
        "ja": "税込",
    },
    "discount": {
        "en": "Discount",
        "ja": "割引",
    },
    "discount_option": {
        "en": "Discount Option",
        "ja": "割引オプション",
    },
    "discount_rate": {
        "en": "Discount Rate",
        "ja": "割引率",
    },
    "discount_type": {
        "en": "Discount Type",
        "ja": "割引タイプ",
    },
    "order_association": {
        "en": "Order (Association) - ID",
        "ja": "受注（アソシエーション） - ID",
    },
    "amount_item": {
        "en": "Amount",
        "ja": "数量",
    },
    "amount_price": {
        "en": "Unit Price",
        "ja": "単価",
    },
    "company": {
        "en": "Company",
        "ja": "企業",
    },
    "contact": {
        "en": "Contact",
        "ja": "連絡先",
    },
    "tax_list": {
        "en": "Tax Rate",
        "ja": "税率",
    },
    "created_at": {
        "en": "Created At",
        "ja": "作成日時"
    },
    "updated_at": {
        "en": "Updated At",
        "ja": "更新日時"
    },
    "journal_entry": {
        "en": "Journal Entry",
        "ja": "仕訳"
    },
    "custom_price": {
        "en": "Custom Price",
        "ja": "カスタム価格",
    },
    "journalentry": {
        "en": "Journal Entry",
        "ja": "仕訳"
    },
    "line_item": {
        "en": "Line Item",
        "ja": "商品項目",
    },
    "subscription": {
        "en": "Subscription",
        "ja": "サブスクリプション",
    },
    "platform_invoice_id": {
        "en": "Invoice ID",
        "ja": "請求書ID",
    },
    "payment_link": {
        "en": "Payment Link",
        "ja": "支払いリンク",
    },
    "owner": {
        "en": "Owner",
        "ja": "所有者",
    }
} | ASSOCIATE_COLUMNS_DISPLAY

ESTIMATE_COLUMNS_DISPLAY = {
    "input_item": {
        "en": "Input Items",
        "ja": "入力アイテム"
    },
    "customers": {
        "en": "Customer",
        "ja": "顧客"
    },
    "customers__contact__name": {
        "en": "Customer (Contact) - Name",
        "ja": "顧客（連絡先） - 名前"
    },
    "customers__company__name": {
        "en": "Customer (Company) - Name",
        "ja": "顧客（企業） - 名前"
    },
    "customers__contact__first_name": {
        "en": "Customer (Contact) - First Name",
        "ja": "顧客（連絡先） - 名前"
    },
    "customers__contact__last_name": {
        "en": "Customer (Contact) - Last Name",
        "ja": "顧客（連絡先）- 苗字"
    },
    "contact_id": {
        "en": "Contact ID",
        "ja": "連絡先ID",
    },
    "company_id": {
        "en": "Company ID",
        "ja": "企業ID"
    },
    "id_est": {
        "en": "ID",
        "ja": "ID"
    },
    "start_date": {
        "en": "Issuing Date",
        "ja": "発行日"
    },
    "due_date": {
        "en": "Expiry Date",
        "ja": "有効期限"
    },
    "date_received": {
        "en": "Received Date",
        "ja": "受領日"
    },
    "status": {
        "en": "Status",
        "ja": "ステータス"
    },
    "total_price": {
        "en": "Total Price",
        "ja": "合計金額"
    },
    "total_price_without_tax": {
        "en": "Total Price Without Tax",
        "ja": "税抜合計価格"
    },
    "usage_status": {
        "en": "Usage Status",
        "ja": "利用ステータス"
    },
    "currency": {
        "en": "Currency",
        "ja": "通貨",
    },
    "item": {
        "en": "Line Items",
        "ja": "商品項目",
    },
    "item_id": {
        "en": "Item ID",
        "ja": "商品ID",
    },
    "item_name": {
        "en": "Item Name",
        "ja": "商品名",
    },
    "item_price": {
        "en": "Item Price",
        "ja": "商品価格",
    },
    "item_amount": {
        "en": "Amount",
        "ja": "数量",
    },
    "notes": {
        "en": "Notes",
        "ja": "備考欄",
    },
    "send_from": {
        "en": "Send From",
        "ja": "送信元",
    },
    "email": {
        "en": "Email",
        "ja": "Eメール",
    },
    "cc_list": {
        "en": "Email CC",
        "ja": "電子メールCC",
    },
    "tax_item_rate": {
        "en": "Item Tax Rate",
        "ja": "商品税率",
    },
    "tax_rate": {
        "en": "Tax Rate (%)",
        "ja": "税率 (%)",
    },
    "tax_option": {
        "en": "Tax Option",
        "ja": "税オプション",
    },
    "tax_inclusive": {
        "en": "Tax Inclusive",
        "ja": "税込",
    },
    "discount": {
        "en": "Discount",
        "ja": "割引",
    },
    "discount_option": {
        "en": "Discount Option",
        "ja": "割引オプション",
    },
    "discount_rate": {
        "en": "Discount Rate",
        "ja": "割引率",
    },
    "discount_type": {
        "en": "Discount Type",
        "ja": "割引タイプ",
    },
    "order_association": {
        "en": "Order (Association) - ID",
        "ja": "受注（アソシエーション） - ID",
    },
    "amount_item": {
        "en": "Amount",
        "ja": "数量",
    },
    "amount_price": {
        "en": "Unit Price",
        "ja": "単価",
    },
    "company": {
        "en": "Company",
        "ja": "企業",
    },
    "contact": {
        "en": "Contact",
        "ja": "連絡先",
    },
    "tax_list": {
        "en": "Tax Rate",
        "ja": "税率",
    },
    "invoice": {
        "en": "Invoice",
        "ja": "売上請求",
    },
    "created_at": {
        "en": "Created At",
        "ja": "作成日時"
    },
    "updated_at": {
        "en": "Updated At",
        "ja": "更新日時"
    },
    "journal_entry": {
        "en": "Journal Entry",
        "ja": "仕訳"
    },
    "cost_option": {
        "en": "Cost Option",
        "ja": "コストオプション",
    },
    "custom_price": {
        "en": "Custom Price",
        "ja": "カスタム価格",
    },
    "journalentry": {
        "en": "Journal Entry",
        "ja": "仕訳"
    },
    "line_item": {
        "en": "Line Item",
        "ja": "商品項目",
    },
    "subscription": {
        "en": "Subscription",
        "ja": "サブスクリプション",
    },
    "platform_invoice_id": {
        "en": "Invoice ID",
        "ja": "請求書ID",
    },
    "payment_link": {
        "en": "Payment Link",
        "ja": "支払いリンク",
    },
    "owner": {
        "en": "Owner",
        "ja": "所有者",
    }
} | ASSOCIATE_COLUMNS_DISPLAY

# Receipt
PAYMENT_COLUMNS_DISPLAY = {
    "input_item": {
        "en": "Input Items",
        "ja": "入力アイテム"
    },
    "customers": {
        "en": "Customer",
        "ja": "顧客"
    },
    "customers__contact__name": {
        "en": "Customer (Contact) - Name",
        "ja": "顧客（連絡先） - 名前"
    },
    "customers__company__name": {
        "en": "Customer (Company) - Name",
        "ja": "顧客（企業） - 名前"
    },
    "customers__contact__first_name": {
        "en": "Customer (Contact) - First Name",
        "ja": "顧客（連絡先） - 名前"
    },
    "customers__contact__last_name": {
        "en": "Customer (Contact) - Last Name",
        "ja": "顧客（連絡先）- 苗字"
    },
    "contact_id": {
        "en": "Contact ID",
        "ja": "連絡先ID",
    },
    "company_id": {
        "en": "Company ID",
        "ja": "企業ID"
    },
    "id_rcp": {
        "en": "ID",
        "ja": "ID"
    },
    "start_date": {
        "en": "Payment Date",
        "ja": "入金日"
    },
    "due_date": {
        "en": "Due Date",
        "ja": "支払期日"
    },
    "date_received": {
        "en": "Received Date",
        "ja": "受領日"
    },
    "status": {
        "en": "Status",
        "ja": "ステータス"
    },
    "total_price": {
        "en": "Total Price",
        "ja": "合計金額"
    },
    "total_price_without_tax": {
        "en": "Total Price Without Tax",
        "ja": "税抜合計価格"
    },
    "usage_status": {
        "en": "Usage Status",
        "ja": "利用ステータス"
    },
    "currency": {
        "en": "Currency",
        "ja": "通貨",
    },
    "item": {
        "en": "Line Items",
        "ja": "商品項目",
    },
    "item_id": {
        "en": "Item ID",
        "ja": "商品ID",
    },
    "item_name": {
        "en": "Item Name",
        "ja": "商品名",
    },
    "item_price": {
        "en": "Item Price",
        "ja": "商品価格",
    },
    "item_amount": {
        "en": "Amount",
        "ja": "数量",
    },
    "notes": {
        "en": "Notes",
        "ja": "備考欄",
    },
    "send_from": {
        "en": "Send From",
        "ja": "送信元",
    },
    "email": {
        "en": "Email",
        "ja": "Eメール",
    },
    "tax_item_rate": {
        "en": "Item Tax Rate",
        "ja": "商品税率",
    },
    "tax_rate": {
        "en": "Tax Rate (%)",
        "ja": "税率 (%)",
    },
    "tax_option": {
        "en": "Tax Option",
        "ja": "税オプション",
    },
    "tax_inclusive": {
        "en": "Tax Inclusive",
        "ja": "税込",
    },
    "discount": {
        "en": "Discount",
        "ja": "割引",
    },
    "discount_option": {
        "en": "Discount Option",
        "ja": "割引オプション",
    },
    "discount_rate": {
        "en": "Discount Rate",
        "ja": "割引率",
    },
    "discount_type": {
        "en": "Discount Type",
        "ja": "割引タイプ",
    },
    "order_association": {
        "en": "Order (Association) - ID",
        "ja": "受注（アソシエーション） - ID",
    },
    "amount_item": {
        "en": "Amount",
        "ja": "数量",
    },
    "amount_price": {
        "en": "Unit Price",
        "ja": "単価",
    },
    "company": {
        "en": "Company",
        "ja": "企業",
    },
    "contact": {
        "en": "Contact",
        "ja": "連絡先",
    },
    "tax_list": {
        "en": "Tax Rate",
        "ja": "税率",
    },
    "invoice": {
        "en": "Invoice",
        "ja": "売上請求",
    },
    "created_at": {
        "en": "Created At",
        "ja": "作成日時"
    },
    "updated_at": {
        "en": "Updated At",
        "ja": "更新日時"
    },
    "journal_entry": {
        "en": "Journal Entry",
        "ja": "仕訳"
    },
    "cost_option": {
        "en": "Cost Option",
        "ja": "コストオプション",
    },
    "custom_price": {
        "en": "Custom Price",
        "ja": "カスタム価格",
    },
    "journalentry": {
        "en": "Journal Entry",
        "ja": "仕訳"
    },
    "line_item": {
        "en": "Line Item",
        "ja": "商品項目",
    },
    "subscription": {
        "en": "Subscription",
        "ja": "サブスクリプション",
    },
    "platform_invoice_id": {
        "en": "Invoice ID",
        "ja": "請求書ID",
    },
    "payment_link": {
        "en": "Payment Link",
        "ja": "支払いリンク",
    },
    "billing_type": {
        "en": "Payment Entry Type",
        "ja": "入金エントリタイプ",
    },
    "manual_price": {
        "en": "Payment Amount",
        "ja": "入金金額",
    },
    "owner": {
        "en": "Owner",
        "ja": "所有者",
    }
} | ASSOCIATE_COLUMNS_DISPLAY

DELIVERY_NOTE_COLUMNS_DISPLAY = {
    "input_item": {
        "en": "Input Items",
        "ja": "入力アイテム"
    },
    "customers": {
        "en": "Customer",
        "ja": "顧客"
    },
    "customers__contact__name": {
        "en": "Customer (Contact) - Name",
        "ja": "顧客（連絡先） - 名前"
    },
    "customers__company__name": {
        "en": "Customer (Company) - Name",
        "ja": "顧客（企業） - 名前"
    },
    "customers__contact__first_name": {
        "en": "Customer (Contact) - First Name",
        "ja": "顧客（連絡先） - 名前"
    },
    "customers__contact__last_name": {
        "en": "Customer (Contact) - Last Name",
        "ja": "顧客（連絡先）- 苗字"
    },
    "contact_id": {
        "en": "Contact ID",
        "ja": "連絡先ID",
    },
    "company_id": {
        "en": "Company ID",
        "ja": "企業ID"
    },
    "id_ds": {
        "en": "ID",
        "ja": "ID"
    },
    "start_date": {
        "en": "Issuing Date",
        "ja": "発行日"
    },
    "due_date": {
        "en": "Due Date",
        "ja": "支払期日"
    },
    "date_received": {
        "en": "Received Date",
        "ja": "受領日"
    },
    "status": {
        "en": "Status",
        "ja": "ステータス"
    },
    "total_price": {
        "en": "Total Price",
        "ja": "合計金額"
    },
    "total_price_without_tax": {
        "en": "Total Price Without Tax",
        "ja": "税抜合計価格"
    },
    "usage_status": {
        "en": "Usage Status",
        "ja": "利用ステータス"
    },
    "currency": {
        "en": "Currency",
        "ja": "通貨",
    },
    "item": {
        "en": "Line Items",
        "ja": "商品項目",
    },
    "item_id": {
        "en": "Item ID",
        "ja": "商品ID",
    },
    "item_name": {
        "en": "Item Name",
        "ja": "商品名",
    },
    "item_price": {
        "en": "Item Price",
        "ja": "商品価格",
    },
    "item_amount": {
        "en": "Amount",
        "ja": "数量",
    },
    "notes": {
        "en": "Notes",
        "ja": "備考欄",
    },
    "send_from": {
        "en": "Send From",
        "ja": "送信元",
    },
    "email": {
        "en": "Email",
        "ja": "Eメール",
    },
    "tax_item_rate": {
        "en": "Item Tax Rate",
        "ja": "商品税率",
    },
    "tax_rate": {
        "en": "Tax Rate (%)",
        "ja": "税率 (%)",
    },
    "tax_option": {
        "en": "Tax Option",
        "ja": "税オプション",
    },
    "tax_inclusive": {
        "en": "Tax Inclusive",
        "ja": "税込",
    },
    "discount": {
        "en": "Discount",
        "ja": "割引",
    },
    "discount_option": {
        "en": "Discount Option",
        "ja": "割引オプション",
    },
    "discount_rate": {
        "en": "Discount Rate",
        "ja": "割引率",
    },
    "discount_type": {
        "en": "Discount Type",
        "ja": "割引タイプ",
    },
    "order_association": {
        "en": "Order (Association) - ID",
        "ja": "受注（アソシエーション） - ID",
    },
    "amount_item": {
        "en": "Amount",
        "ja": "数量",
    },
    "amount_price": {
        "en": "Unit Price",
        "ja": "単価",
    },
    "company": {
        "en": "Company",
        "ja": "企業",
    },
    "contact": {
        "en": "Contact",
        "ja": "連絡先",
    },
    "tax_list": {
        "en": "Tax Rate",
        "ja": "税率",
    },
    "invoice": {
        "en": "Invoice",
        "ja": "売上請求",
    },
    "created_at": {
        "en": "Created At",
        "ja": "作成日時"
    },
    "updated_at": {
        "en": "Updated At",
        "ja": "更新日時"
    },
    "journal_entry": {
        "en": "Journal Entry",
        "ja": "仕訳"
    },
    "cost_option": {
        "en": "Cost Option",
        "ja": "コストオプション",
    },
    "custom_price": {
        "en": "Custom Price",
        "ja": "カスタム価格",
    },
    "journalentry": {
        "en": "Journal Entry",
        "ja": "仕訳"
    },
    "line_item": {
        "en": "Line Item",
        "ja": "商品項目",
    },
    "subscription": {
        "en": "Subscription",
        "ja": "サブスクリプション",
    },
    "platform_invoice_id": {
        "en": "Invoice ID",
        "ja": "請求書ID",
    },
    "payment_link": {
        "en": "Payment Link",
        "ja": "支払いリンク",
    },
    "owner": {
        "en": "Owner",
        "ja": "所有者",
    }
} | ASSOCIATE_COLUMNS_DISPLAY

SLIP_COLUMNS_DISPLAY = {
    "id_slip": {
        "en": "ID",
        "ja": "ID"
    },
    "input_item": {
        "en": "Input Items",
        "ja": "入力アイテム"
    },
    "customers": {
        "en": "Customer",
        "ja": "顧客"
    },
    "customers__contact__name": {
        "en": "Customer (Contact) - Name",
        "ja": "顧客（連絡先） - 名前"
    },
    "customers__company__name": {
        "en": "Customer (Company) - Name",
        "ja": "顧客（企業） - 名前"
    },
    "customers__contact__first_name": {
        "en": "Customer (Contact) - First Name",
        "ja": "顧客（連絡先） - 名前"
    },
    "customers__contact__last_name": {
        "en": "Customer (Contact) - Last Name",
        "ja": "顧客（連絡先）- 苗字"
    },
    "contact_id": {
        "en": "Contact ID",
        "ja": "連絡先ID",
    },
    "company_id": {
        "en": "Company ID",
        "ja": "企業ID"
    },
    "id_slp": {
        "en": "ID",
        "ja": "ID"
    },
    "start_date": {
        "en": "Issuing Date",
        "ja": "発行日"
    },
    "due_date": {
        "en": "Due Date",
        "ja": "支払期日"
    },
    "date_received": {
        "en": "Received Date",
        "ja": "受領日"
    },
    "status": {
        "en": "Status",
        "ja": "ステータス"
    },
    "slip_type": {
        "en": "Slip Type",
        "ja": "伝票種類"
    },
    "total_price": {
        "en": "Total Price",
        "ja": "合計金額"
    },
    "total_price_without_tax": {
        "en": "Total Price Without Tax",
        "ja": "税抜合計価格"
    },
    "usage_status": {
        "en": "Usage Status",
        "ja": "利用ステータス"
    },
    "currency": {
        "en": "Currency",
        "ja": "通貨",
    },
    "item": {
        "en": "Line Items",
        "ja": "商品項目",
    },
    "item_id": {
        "en": "Item ID",
        "ja": "商品ID",
    },
    "item_name": {
        "en": "Item Name",
        "ja": "商品名",
    },
    "item_price": {
        "en": "Item Price",
        "ja": "商品価格",
    },
    "item_amount": {
        "en": "Amount",
        "ja": "数量",
    },
    "notes": {
        "en": "Notes",
        "ja": "備考欄",
    },
    "send_from": {
        "en": "Send From",
        "ja": "送信元",
    },
    "email": {
        "en": "Email",
        "ja": "Eメール",
    },
    "tax_item_rate": {
        "en": "Item Tax Rate",
        "ja": "商品税率",
    },
    "tax_rate": {
        "en": "Tax Rate (%)",
        "ja": "税率 (%)",
    },
    "tax_option": {
        "en": "Tax Option",
        "ja": "税オプション",
    },
    "tax_inclusive": {
        "en": "Tax Inclusive",
        "ja": "税込",
    },
    "discount": {
        "en": "Discount",
        "ja": "割引",
    },
    "discount_option": {
        "en": "Discount Option",
        "ja": "割引オプション",
    },
    "discount_rate": {
        "en": "Discount Rate",
        "ja": "割引率",
    },
    "discount_type": {
        "en": "Discount Type",
        "ja": "割引タイプ",
    },
    "order_association": {
        "en": "Order (Association) - ID",
        "ja": "受注（アソシエーション） - ID",
    },
    "amount_item": {
        "en": "Amount",
        "ja": "数量",
    },
    "amount_price": {
        "en": "Unit Price",
        "ja": "単価",
    },
    "company": {
        "en": "Company",
        "ja": "企業",
    },
    "contact": {
        "en": "Contact",
        "ja": "連絡先",
    },
    "tax_list": {
        "en": "Tax Rate",
        "ja": "税率",
    },
    "invoice": {
        "en": "Invoice",
        "ja": "売上請求",
    },
    "created_at": {
        "en": "Created At",
        "ja": "作成日時"
    },
    "updated_at": {
        "en": "Updated At",
        "ja": "更新日時"
    },
    "journal_entry": {
        "en": "Journal Entry",
        "ja": "仕訳"
    },
    "cost_option": {
        "en": "Cost Option",
        "ja": "コストオプション",
    },
    "custom_price": {
        "en": "Custom Price",
        "ja": "カスタム価格",
    },
    "journalentry": {
        "en": "Journal Entry",
        "ja": "仕訳"
    },
    "line_item": {
        "en": "Line Item",
        "ja": "商品項目",
    },
    "subscription": {
        "en": "Subscription",
        "ja": "サブスクリプション",
    },
    "platform_invoice_id": {
        "en": "Invoice ID",
        "ja": "請求書ID",
    },
    "payment_link": {
        "en": "Payment Link",
        "ja": "支払いリンク",
    },
    "owner": {
        "en": "Owner",
        "ja": "所有者",
    }
} | ASSOCIATE_COLUMNS_DISPLAY

ORDERS_COLUMNS_DISPLAY = ORDERS_COLUMNS_DISPLAY | ORDERS_STATUS_DISPLAY | ORDERS_TAX_APPLIED_TO_DISPLAY | SEARCH_COLUMNS_DISPLAY | ASSOCIATE_COLUMNS_DISPLAY

SECTION_TYPES = [
    ('subheading', 'Subheading'),
    ('subtotal', 'Subtotal'),
]

BASIC_DEFAULT_COLUMNS = ["id"]
DEFAULT_COLUMNS_JOURNAL = ['partner', 'owner',
                           'transaction_date', 'tax_rate', 'category', 'notes']
DEFAULT_COLUMNS_CALENDAR = ['id']
DEFAULT_COLUMNS_KNOWLEDGE = ['knowledge_id', 'user', 'header', 'access']
DEFAULT_COLUMNS_FILEBOX = ['file_id', 'name', 'access']
DEFAULT_COLUMNS_PORTAL = ['portal_id', 'name',
                          'description', 'members', 'feeds', 'design', 'created_at']
DEFAULT_COLUMNS_JOBS = ['job_id', 'title', 'description', 'applications',
                        'interview', 'scorecard', 'status', 'job_type', 'preview', 'created_at']
DEFAULT_COLUMN_APPLICANT = ['job_applicant_id','name', 'email','status', 'current_company', 'resume', 'phone_number', 'url', 'created_at']
DEFAULT_COLUMNS_IT = ['it_id', 'name', 'user',
                      'cost', 'frequency', 'it_type', 'created_at']
DEFAULT_COLUMNS_CONTRACT = ['contract_id', 'owner', 'name', 'status',
                            'signer', 'file', 'signed_document', 'created_at']
DEFAULT_COLUMNS_ORDER = ["checkbox", "order_id", "customer", "delivery_status", "line_item", "total_price", "total_price_without_tax",
                         "created_at", "updated_at", "order_at", "line_item_price", "order-tax", "platform_id", "line_item_name_quantity", "line_item_quantity", "memo", "owner"]
DEFAULT_COLUMNS_SUBSCRIPTIONS = ["checkbox", "subscriptions_id", "item_name", "customer", "durations",
                                 "total_price", "total_price_without_tax", "subscription_status", "created_at", "updated_at", "line_item"]
DEFAULT_COLUMNS_INVOICE = ['id_inv', 'customers', 'start_date', 'due_date', 'status',
                           'total_price', 'total_price_without_tax', "line_item", 'created_at', 'updated_at']
DEFAULT_COLUMNS_ESTIMATE = ['id_est', 'customers', 'start_date', 'status', 'total_price',
                            'total_price_without_tax', "line_item", 'created_at', 'updated_at']
DEFAULT_COLUMNS_PURCHASE_ORDER = ["id_po", "owner", "status", "total_price", "total_price_without_tax", "date", "supplier",
                                  'line_item_name', 'line_item_status', "line_item", 'line_item_price', 'line_item_price_without_tax', 'send_from', 'notes']
DEFAULT_COLUMNS_BILLING = ['id_bill', 'owner','partner', 'description', 'status',
                           'amount', 'due_date', 'issued_date', 'file']
DEFAULT_COLUMNS_EXPENSE = ['id_pm', 'partner', 'owner',
                           'status', 'amount', 'due_date', 'reimburse_date', 'created_at', 'updated_at']
DEFAULT_COLUMNS_DELIVERY_SLIP = ['id_ds', 'customers', 'start_date', 'status',
                                 "total_price", "total_price_without_tax", "line_item", 'created_at', 'updated_at']
DEFAULT_COLUMNS_SLIP = ['id_slip', "slip_type", 'customers',
                        'start_date', "notes", "send_from", 'created_at', 'updated_at', 'line_item']
DEFAULT_COLUMNS_RECEIPT = ['id_rcp', 'customers', 'start_date', 'status',
                           "total_price", "total_price_without_tax", "line_item", 'created_at', 'updated_at']

DEFAULT_COLUMNS_TASK = ['title', 'owner','assignee', 'status',
                        'start_date', 'due_date', 'created_at', 'updated_at']
DEFAULT_COLUMNS_WORKFLOW = ["assignee", 'owner', "history",
                            "review", "last_run", "created_at", "updated_at"]

DEFAULT_COLUMNS_ITEM = ["checkbox", "item_id", "name", "description",
                        "price", "currency", "purchase_price", "purchase_price_currency", "supplier", "created_at", "updated_at","owner"]
DEFAULT_COLUMNS_INVENTORY = ["checkbox", "inventory_id", "item", "inventory_status",
                             "unit_price", "total_inventory", "date", "created_at", "updated_at","owner"]
DEFAULT_COLUMNS_WAREHOUSE = ["checkbox", "id_iw", "location", "warehouse",
                             "floor", "zone", "aisle", "rack", "shelf", "bin", 'created_at', 'updated_at','owner']
DEFAULT_COLUMNS_TRANSACTION = ["checkbox", "transaction_id", "transaction_date", "user",
                               "inventory", "transaction_type", "amount", "inventory_type", "created_at", "updated_at"]

DEFAULT_COLUMNS_CONTACTS = ['contact_id', 'image_url', 'name', 'email', 'company',
                            'phone_number', 'created_at', 'updated_at']  # Removed 'status' field
DEFAULT_COLUMNS_COMPANY = ['company_id', 'name', 'image_file', 'address',
                           'email', 'phone_number', 'created_at', 'updated_at']
DEFAULT_COLUMNS_CASE = ['deal_id', 'name', 'customer',
                        "case_status", 'line_item', 'created_at', 'updated_at','owner']
DEFAULT_COLUMNS_CONVERSATION = ['status']
DEFAULT_COLUMNS_WORKER = ['name', 'manager', 'user', 'status',
                          'usage_status', 'location', 'date_from', 'date_to', 'email']
DEFAULT_COLUMNS_WORKER_REVIEW = ['score', 'review']
DEFAULT_COLUMNS_ABSENCE = ['start_date', 'end_date',
                           'absence_type', 'status', 'requested_by', 'note', 'approved_by']
DEFAULT_COLUMNS_FORM = ['title', 'user', 'owner', 'visibility',
                        'entries', 'created_at', 'updated_at']
DEFAULT_COLUMNS_PANEL = ['name', 'owner', 'type_objects', 'panel_type',
                         'is_deleted', 'created_by', 'created_at', 'updated_at']
DEFAULT_COLUMNS_DASHBOARD = ['name', 'owner', 'created_at',
                             'panels', 'updated_at', 'created_by', 'updated_by']
DEFAULT_COLUMN_TIMEGENIE = ["name", "user", "start_time",
                            "end_time", "duration", "status", "usage_status"]
DEFAULT_COLUMNS_USER_MANAGEMENT = [
    'name', 'email', 'role', 'registration_date']

# DEFAULT FORM FIELDS (also to define the order of fields in the form)
DEFAULT_FORM_FIELDS_PURHCASE_ORDER = [
    'owner', 'supplier', 'date', 'line_item', 'status', 'send_from', 'notes']

DEFAULT_FORM_FIELD_COMMERCE = ['owner', 'customers', 'currency', 'line_item',
                              'start_date', 'due_date', 'status',  'send_from', 'notes']

DEFAULT_FORM_FIELD_DELIVERY_NOTE = ['owner', 'customers', 'currency', 'line_item',
                                    'start_date', 'status', 'send_from', 'notes']
DEFAULT_FORM_FIELD_SLIP = ['owner', 'customers', 'currency', 'line_item',
                           'start_date', 'status', 'send_from', 'notes']
DEFAULT_FORM_FIELD_ORDER = [
    "customer", "delivery_status", "line_item", "order_at", "memo","owner"]

DEFAULT_FORM_FIELD_ITEM = ["name", "description",
                           "price", "purchase_price", "supplier","owner"]
DEFAULT_FORM_FIELD_CONTACTS = [
    'first_name', 'last_name', 'image_url', 'email', 'company', 'phone_number']
DEFAULT_FORM_FIELD_COMPANY = ['name', 'address',
                              'email', 'url', 'phone_number', 'image_file']
DEFAULT_FORM_FIELD_CASE = ['name', 'customer', "case_status", "line_item", "owner"]

DEFAULT_FORM_FIELD_SUBSCRIPTIONS = [
    "owner","customer", "durations", "line_item", "subscription_status", "frequency", "frequency_time"]

DEFAULT_FORM_FIELD_INVENTORY = [
    "item", "inventory_status", "unit_price", "initial_value", "warehouse", "date", "owner"]

DEFAULT_FORM_FIELD_TRANSACTION = ["inventory", "owner","amount", "transaction_date"]
DEFAULT_FORM_FIELD_WAREHOUSE = [
    "location", "warehouse", "floor", "zone", "aisle", "rack", "shelf", "bin","owner"]
DEFAULT_FORM_FIELDS_EXPENSE = ['owner', 'partner', 'amount',
                               'due_date', 'reimburse_date', 'status', 'file']
DEFAULT_FORM_FIELD_BILL = ['partner', 'owner', 'description', 'status',
                           'amount', 'due_date', 'issued_date', 'file']

DEFAULT_FORM_FIELD_TASK = ['title', 'owner', 'assignee', 'status',
                           'start_date', 'due_date', 'description', 'line_item', 'sub_tasks']

DEFAULT_FORM_FIELD_JOURNAL = ['partner', 'owner', 'transaction_date','tax_rate', 
                              'line_transaction', 'notes']

DEFAULT_FORM_FIELD_TIMEGENIE = ['name', 'user', 'start_time',
                                'end_time', 'duration', 'status', 'usage_status']
DEFAULT_FORM_FIELD_APPLICANT =  ['name', 'email','status', 'current_company', 'resume', 'phone_number', 'url']
SANKA_ID_COLUMN = {
    "contacts": "contact_id",
    "company": "company_id",
    "customer_case": "deal_id",
    "commerce_orders": "order_id",
    "commerce_subscription": "subscriptions_id",
    "jobs": 'job_id',
}

BILL_OBJECTS = ['invoices', 'receipts', 'estimates', 'slips', 'delivery_slips']
ADDITIONAL_SUPPLIER_CUSTOMER_PROPERTY = [
    '__company__name', '__contact__name', '__contact__first_name', '__contact__last_name']

PURCHASE_ORDER_COLUMNS_DISPLAY = {
    "id_po": {
        "en": "ID",
        "ja": "ID"
    },
    "name": {
        "en": "Name",
        "ja": "名前"
    },
    "status": {
        "en": "Status",
        "ja": "ステータス"
    },
    "total_price": {
        "en": "Total Price",
        "ja": "合計金額"
    },
    "total_price_without_tax": {
        "en": "Total Price Without Tax",
        "ja": "税抜合計価格"
    },
    "date": {
        "en": "Date",
        "ja": "日付"
    },
    "usage_status": {
        "en": "Usage Status",
        "ja": "利用ステータス"
    },
    "tax_rate": {
        "en": "Tax Rate",
        "ja": "税率"
    },
    "tax_option": {
        "en": "Tax Option",
        "ja": "税オプション"
    },
    "currency": {
        "en": "Currency",
        "ja": "通貨"
    },
    "file": {
        "en": "File",
        "ja": "ファイル"
    },
    "send_from": {
        "en": "Send From",
        "ja": "送信元"
    },
    "notes": {
        "en": "Notes",
        "ja": "備考欄"
    },
    "purchaseitem_id": {
        "en": "Items IDs",
        "ja": "商品ID"
    },
    "item_id": {  # ==? it is for csv_upload (import csv)
        "en": "Items IDs",
        "ja": "商品ID"
    },
    "supplier": {
        "en": "Supplier",
        "ja": "仕入先"
    },
    "supplier__contact__name": {
        "en": "Supplier (Contact) - Name",
        "ja": "仕入先（連絡先） - 名前"
    },
    "supplier__company__name": {
        "en": "Supplier (Company) - Name",
        "ja": "仕入先（企業） - 名前"
    },
    "supplier__contact__first_name": {
        "en": "Supplier (Contact) - First Name",
        "ja": "仕入先（連絡先） - ファーストネーム"
    },
    "supplier__contact__last_name": {
        "en": "Supplier (Contact) - Last Name",
        "ja": "仕入先（連絡先）- 苗字"
    },
    'line_item': {
        'en': 'Line Item',
        'ja': '商品項目',
    },
    "line_item_name": {
        "en": "Line Item - Name",
        "ja": "商品項目 - 名前"
    },
    "line_item_quantity": {
        "en": "Line Item - Quantity",
        "ja": "商品項目 - 数量"
    },
    "line_item_name_quantity": {
        "en": "Line Item - Name and Quantity",
        "ja": "商品項目 - 名前と数量"
    },
    "line_item_status": {
        "en": "Line Item - Status",
        "ja": "商品項目 - ステータス"
    },
    "line_item_tax": {
        "en": "Line Item - Tax Rate",
        "ja": "商品項目 - 税率"
    },
    "line_item_price": {
        "en": "Line Item - Total Purchase Price",
        "ja": "商品項目 - 合計購入価格"
    },
    "line_item_price_without_tax": {
        "en": "Line Item - Purchase Price Without Tax",
        "ja": "商品項目 - 税抜き購買価格"
    },
    "source_item": {
        "en": "Source Item",
        "ja": "参照商品"
    },
    "supplier_contact": {
        "en": "Supplier (Contact)",
        "ja": "仕入先（連絡先）"
    },
    "supplier_company": {
        "en": "Supplier (Company)",
        "ja": "仕入先（企業）"
    },
    "id_item": {
        "en": "ID",
        "ja": "ID"
    },
    "amount": {
        "en": "Price",
        "ja": "価格"
    },
    "note": {
        "en": "Notes",
        "ja": "備考欄"
    },
    "id_bill": {
        "en": "ID",
        "ja": "ID"
    },
    "title": {
        "en": "Name",
        "ja": "名前"
    },
    "description": {
        "en": "Description",
        "ja": "説明"
    },
    "due_date": {
        "en": "Payment Due",
        "ja": "支払い期日"
    },
    "issued_date": {
        "en": "Issues Date",
        "ja": "発行日"
    },
    "company": {
        "en": "Company",
        "ja": "企業"
    },
    "updated_at": {
        "en": "Updated At",
        "ja": "更新日時"
    },
    "inventory_transactions": {
        "en": "Inventory Transactions",
        "ja": "入出庫"
    },
    "number_of_items": {
        "en": "Number Of Items",
        "ja": "商品数"
    },
    "price_of_items": {
        "en": "Price of Items",
        "ja": "商品の価格"
    },
    "created_at": {
        "en": "Created At",
        "ja": "作成日時"
    },
    "updated_at": {
        "en": "Updated At",
        "ja": "更新日時"
    },
    'owner': {
        "en": "Owner",
        "ja": "所有者",
    },

}


DISPLAY_COLUMNS_BILL = {
    "id_bill": {
        "en": "ID",
        "ja": "ID"
    },
    'title': {
        'ja': '名前',
        'en': 'Name',
    },
    'partner': {
        'ja': '取引先',
        'en': 'Partner',
    },
    'description': {
        'ja': '説明',
        'en': 'Description',
    },
    'status': {
        'ja': 'ステータス',
        'en': 'Status',
    },
    'amount': {
        'ja': '合計金額',
        'en': 'Amount',
    },
    'due_date': {
        'ja': '支払い期日',
        'en': 'Payment Due',
    },
    'issued_date': {
        'ja': '発行日',
        'en': 'Issues Date',
    },
    'file': {
        'ja': '請求ファイル',
        'en': 'Billing File',
    },
    'currency': {
        "en": "Currency",
        "ja": "通貨",
    },
    "usage_status": {
        "en": "Usage Status",
        "ja": "利用ステータス"
    },

    'number_item': {
        "en": "Number of Items",
        "ja": "商品数"
    },

    'item_price_bill': {
        'ja': '商品価格',
        'en': 'Item Price',
    },

    'contact_id': {
        'ja': '取引先(連絡先)',
        'en': 'Partner (Contact)',
    },
    'company_id': {
        'ja': '取引先(企業)',
        'en': 'Partner (Company)',
    },
    'created_at': {
        'ja': '作成日',
        'en': 'Created At',
    },
    'updated_at': {
        'ja': '更新日',
        'en': 'Updated At',
    },
    "journal_entry": {
        "en": "Accounting Transaction",
        "ja": "仕訳"
    },
    'owner': {
        "en": "Owner",
        "ja": "所有者",
    },
}

BILLING_COLUMNS_DISPLAY = DISPLAY_COLUMNS_BILL | SEARCH_COLUMNS_DISPLAY

DISPLAY_COLUMNS_EXPENSE = {
    "id_pm": {
        "en": "ID",
        "ja": "ID"
    },
    'partner': {
        'ja': '取引先',
        'en': 'Partner',
    },
    'submitter__first_name': {
        'ja': '提出者',
        'en': 'Submitter',
    },
    'owner': {
        "en": "Owner",
        "ja": "所有者",
    },
    'description': {
        'ja': '説明',
        'en': 'Description',
    },
    'status': {
        'ja': 'ステータス',
        'en': 'Status',
    },
    'amount': {
        'ja': '合計金額',
        'en': 'Total Amount',
    },
    'reimburse_date': {
        'ja': '払い戻し日',
        'en': 'Reimburse Date',
    },
    'due_date': {
        'ja': '支払い日',
        'en': 'Payment Date',
    },
    'file': {
        'ja': '領収書ファイル',
        'en': 'Receipt File',
    },
    'currency': {
        "en": "Currency",
        "ja": "通貨",
    },
    'base_currency': {
        "en": "Base Currency",
        "ja": "ベース通貨",
    },
    'category': {
        "en": "Category",
        "ja": "カテゴリー",
    },
    "usage_status": {
        "en": "Usage Status",
        "ja": "利用ステータス"
    },
    "journalentry": {
        "en": "Journal Entries",
        "ja": "仕訳"
    },
    "created_at": {
        "en": "Created Date",
        "ja": "作成日時",
    },
    "updated_at": {
        "en": "Updated Date",
        "ja": "更新日時",
    },
    'journal_entry': {
        "en": "Journal Entry",
        "ja": "仕訳",
    },
    'contact_id': {  # csv upload
        'ja': '取引先(連絡先)',
        'en': 'Partner (Contact)',
    },
    'company_id': {  # csv upload
        'ja': '取引先(企業)',
        'en': 'Partner (Company)',
    }

}

EXPENSE_COLUMNS_DISPLAY = DISPLAY_COLUMNS_EXPENSE | SEARCH_COLUMNS_DISPLAY


DISPLAY_COLUMNS_ACTION_STATUS = {
    "initialized": {
        "en": "Initialized",
        "ja": "初期化済み",
    },
    "failed": {
        "en": "Failed",
        "ja": "失敗",
    },
    "review": {
        "en": "Review",
        "ja": "レビュー",
    },
    "scheduled": {
        "en": "Scheduled",
        "ja": "予定済み",
    },
    "running": {
        "en": "Running",
        "ja": "実行中",
    },
    "waiting_trigger": {
        "en": "Waiting_trigger",
        "ja": "トリガー待ち",
    },
    "stop_trigger": {
        "en": "Stop_trigger",
        "ja": "トリガー停止",
    },
    "skipped": {
        "en": "Skipped",
        "ja": "スキップ済み",
    },
    "success": {
        "en": "Success",
        "ja": "成功",
    },
}

DISPLAY_COLUMNS_TASK = {
    "title": {
        "en": "Task",
        "ja": "タスク",
    },
    "assignee": {
        "en": "Assignee",
        "ja": "担当者",
    },
    "status": {
        "en": "Status",
        "ja": "ステータス",
    },
    "created_at": {
        "en": "Created Date",
        "ja": "作成日時",
    },
    "start_date": {
        "en": "Start Date",
        "ja": "開始日",
    },
    "due_date": {
        "en": "Due Date",
        "ja": "期日",
    },
    "line_item": {
        "en": "Line Item",
        "ja": "商品項目",
    },
    "sub_tasks": {
        "en": "Sub Tasks",
        "ja": "サブタスク",
    },
    "updated_at": {
        "en": "Updated Date",
        "ja": "更新日時",
    },
    "description": {
        "en": "Description",
        "ja": "説明",
    },
    "usage_status": {
        "en": "Usage Status",
        "ja": "利用ステータス",
    },
    "owner": {
        "en" : 'Owner',
        "ja": '所有者'
    }
}

DISPLAY_COLUMNS_WORKFLOW = {
    "name": {
        "en": "Name",
        "ja": "名前",
    },
    "trigger": {
        "en": "Trigger",
        "ja": "トリガー",
    },
    "assignee": {
        "en": "User",
        "ja": "ユーザー",
    },
    "history": {
        "en": "History",
        "ja": "履歴",
    },
    "review": {
        "en": "Review",
        "ja": "レビュー",
    },
    "updated_at": {
        "en": "Updated At",
        "ja": "更新日時"
    },
    "created_at": {
        "en": "Created Date",
        "ja": "作成日時",
    },
    "last_run": {
        "en": "Last Run",
        "ja": "最終実行",
    },
    "owner": {
        "en" : 'Owner',
        "ja": '所有者'
    }
}

DISPLAY_COLUMNS_WAREHOUSE = {
    "id_iw": {
        "en": "ID",
        "ja": "ID"
    },
    "location": {
        "en": "Location",
        "ja": "ロケーション"
    },
    'warehouse':
    {
        "en": 'Warehouse',
        "ja": '倉庫',
    },
    'floor':
    {
        "en": 'Floor',
        "ja": 'フロア',
    },
    'zone':
    {
        "en": 'Zone',
        "ja": 'ゾーン',
    },
    'aisle':
    {
        "en": 'Aisle',
        "ja": '列',
    },
    'rack':
    {
        "en": 'Rack',
        "ja": '連',
    },
    'shelf':
    {
        "en": 'Shelf',
        "ja": '段',
    },
    'bin':
    {
        "en": 'Bin',
        "ja": '間口',
    },
    "updated_at": {
        "en": "Updated At",
        "ja": "更新日時"
    },
    "created_at": {
        "en": "Created At",
        "ja": "作成日時"
    },
    "updated_at": {
        "en": "Updated At",
        "ja": "更新日時"
    },
    "usage_status": {
        "en": "Usage Status",
        "ja": "利用ステータス"
    },
    "owner": {
        "en" : 'Owner',
        "ja": '所有者'
    }
}

DISPLAY_COLUMNS_ = {
    "id_iw": {
        "en": "ID",
        "ja": "ID"
    },
    "name": {
        "en": "Name",
        "ja": "名前"
    },
    "location": {
        "en": "Location",
        "ja": "ロケーション"
    },
    "created_at": {
        "en": "Created At",
        "ja": "作成日時"
    },
}

DEFAULT_COLUMN_CAMPAIGN = ['channel', 'body', 'status',
                           'created_at', 'scheduled_at', 'published_at']
DEFAULT_COLUMN_ADS = ['id_ads', 'name', 'account_name', 'budget',
                      'period_started', 'period_ended', 'status',  'average_cost', 'conversions']
DEFAULT_COLUMN_SOCIAL_MEDIA_ALL_POSTS = [
    'body', 'channel', 'status', 'created_at', 'url', 'scheduled_at', 'published_at']
DEFAULT_COLUMN_SOCIAL_MEDIA_DRAFTS = [
    'category', 'body', 'channel', 'status', 'created_at']
DEFAULT_COLUMN_SOCIAL_MEDIA_DRAFTS = [
    'category', 'body', 'channel', 'created_at']
DEFAULT_COLUMN_SOCIAL_MEDIA_REVIEWS = [
    'category', 'body', 'channel', 'created_at']
DEFAULT_COLUMN_SOCIAL_MEDIA_SCHEDULED = [
    'category', 'body', 'channel', 'scheduled_at']
DEFAULT_COLUMN_SOCIAL_MEDIA_ERRORS = [
    'category', 'body', 'channel', 'scheduled_at']
DEFAULT_COLUMN_SOCIAL_MEDIA_PUBLISHED = [
    'category', 'body', 'channel', 'url', 'published_at']
DEFAULT_COLUMN_EMAIL = ['title', 'channel', 'status', 'contact_list',
                        'opens', 'delivers', 'scheduled_at', 'created_at', 'published_at']

DISPLAY_COLUMNS_ADS = {
    "id_ads": {
        "en": "ID",
        "ja": "ID"
    },
    "name": {
        "en": "Name",
        "ja": "名前",
    },
    "account_name": {
        "en": "Account Name",
        "ja": "アカウント名",
    },
    "budget": {
        "en": "Budget",
        "ja": "予算",
    },
    "period_started": {
        "en": "Period Started",
        "ja": "期間が始まりました",
    },
    "period_ended": {
        "en": "Period Ended",
        "ja": "期間が終了しました",
    },
    "status": {
        "en": "Status",
        "ja": "ステータス",
    },
    "average_cost": {
        "en": "Average Cost",
        "ja": "平均コスト",
    },
    "conversions": {
        "en": "Conversions",
        "ja": "変換",
    },
    "usage_status": {
        "en": "Usage Status",
        "ja": "利用ステータス"
    },
    "average_cpc": {
        "en": "Average CPC",
        "ja": "平均 CPC",
    },
    "average_cpe": {
        "en": "Average CPE",
        "ja": "平均 CPE",
    },
    "average_cpm": {
        "en": "Average CPM",
        "ja": "平均 CPM",
    },
    "ctr": {
        "en": "CTR",
        "ja": "CTR",
    },
    "clicks": {
        "en": "Clicks",
        "ja": "クリック",
    },
    "cost_per_conversion": {
        "en": "Cost Per Conversion",
        "ja": "コンバージョンあたりのコスト",
    },
    "impressions": {
        "en": "Impressions",
        "ja": "印象",
    },
    "amount_spent": {
        "en": "Amount Spent",
        "ja": "費やした金額",
    }
}

DISPLAY_COLUMNS_SOCIAL_MEDIA = {
    'body': {
        'en': 'Content',
        'ja': 'コンテンツ'
    },
    'channel': {
        'en': 'Channel',
        'ja': 'チャンネル'
    },
    'status': {
        'en': 'Status',
        'ja': 'ステータス'
    },
    'created_at': {
        'en': 'Created At',
        'ja': '作成日'
    },
    'url': {
        'en': 'URL',
        'ja': 'URL'
    },
    'scheduled_at': {
        'en': 'Scheduled Date',
        'ja': '投稿予定日'
    },
    'published_at': {
        'en': 'Published Date',
        'ja': '投稿日'
    }
}

DISPLAY_COLUMNS_EMAIL = {
    'title': {
        'en': 'Email Subject',
        'ja': 'メールの件名',
    },
    'channel': {
        'en': 'Channel',
        'ja': 'チャンネル',
    },
    'status': {
        'en': 'Status',
        'ja': 'ステータス',
    },
    'created_at': {
        'en': 'Created At',
        'ja': '作成日',
    },
    'contact_list': {
        'en': 'Contact List',
        'ja': '連絡先リスト',
    },
    'opens': {
        'en': 'Opens',
        'ja': '開封数',
    },
    'delivers': {
        'en': 'Delivers',
        'ja': '到達数',
    },
    'scheduled_at': {
        'en': 'Scheduled Date',
        'ja': '投稿予定日',
    },
    'published_at': {
        'en': 'Published Date',
        'ja': '投稿日',
    }
}

SHOPIFY_COUNTRY_CODE = {
    "AC": {
        "en": "Ascension Island",
        "ja": "アセンション島"
    },
    "AD": {
        "en": "Andorra",
        "ja": "アンドラ"
    },
    "AE": {
        "en": "United Arab Emirates",
        "ja": "アラブ首長国連邦"
    },
    "AF": {
        "en": "Afghanistan",
        "ja": "アフガニスタン"
    },
    "AG": {
        "en": "Antigua & Barbuda",
        "ja": "アンティグア・バーブーダ"
    },
    "AI": {
        "en": "Anguilla",
        "ja": "アンギラ"
    },
    "AL": {
        "en": "Albania",
        "ja": "アルバニア"
    },
    "AM": {
        "en": "Armenia",
        "ja": "アルメニア"
    },
    "AN": {
        "en": "Netherlands Antilles",
        "ja": "オランダ領アンティル"
    },
    "AO": {
        "en": "Angola",
        "ja": "アンゴラ"
    },
    "AR": {
        "en": "Argentina",
        "ja": "アルゼンチン"
    },
    "AT": {
        "en": "Austria",
        "ja": "オーストリア"
    },
    "AU": {
        "en": "Australia",
        "ja": "オーストラリア"
    },
    "AW": {
        "en": "Aruba",
        "ja": "アルバ"
    },
    "AX": {
        "en": "Åland Islands",
        "ja": "オーランド諸島"
    },
    "AZ": {
        "en": "Azerbaijan",
        "ja": "アゼルバイジャン"
    },
    "BA": {
        "en": "Bosnia & Herzegovina",
        "ja": "ボスニア・ヘルツェゴビナ"
    },
    "BB": {
        "en": "Barbados",
        "ja": "バルバドス"
    },
    "BD": {
        "en": "Bangladesh",
        "ja": "バングラデシュ"
    },
    "BE": {
        "en": "Belgium",
        "ja": "ベルギー"
    },
    "BF": {
        "en": "Burkina Faso",
        "ja": "ブルキナファソ"
    },
    "BG": {
        "en": "Bulgaria",
        "ja": "ブルガリア"
    },
    "BH": {
        "en": "Bahrain",
        "ja": "バーレーン"
    },
    "BI": {
        "en": "Burundi",
        "ja": "ブルンジ"
    },
    "BJ": {
        "en": "Benin",
        "ja": "ベナン"
    },
    "BL": {
        "en": "St. Barthélemy",
        "ja": "サン・バルテルミー島"
    },
    "BM": {
        "en": "Bermuda",
        "ja": "バミューダ"
    },
    "BN": {
        "en": "Brunei",
        "ja": "ブルネイ"
    },
    "BO": {
        "en": "Bolivia",
        "ja": "ボリビア"
    },
    "BQ": {
        "en": "Caribbean Netherlands",
        "ja": "カリブ海オランダ"
    },
    "BR": {
        "en": "Brazil",
        "ja": "ブラジル"
    },
    "BS": {
        "en": "Bahamas",
        "ja": "バハマ"
    },
    "BT": {
        "en": "Bhutan",
        "ja": "ブータン"
    },
    "BV": {
        "en": "Bouvet Island",
        "ja": "ブーベ島"
    },
    "BW": {
        "en": "Botswana",
        "ja": "ボツワナ"
    },
    "BY": {
        "en": "Belarus",
        "ja": "ベラルーシ"
    },
    "BZ": {
        "en": "Belize",
        "ja": "ベリーズ"
    },
    "CA": {
        "en": "Canada",
        "ja": "カナダ"
    },
    "CC": {
        "en": "Cocos (Keeling) Islands",
        "ja": "ココス (キーリング) 諸島"
    },
    "CD": {
        "en": "Congo - Kinshasa",
        "ja": "コンゴ - キンシャサ"
    },
    "CF": {
        "en": "Central African Republic",
        "ja": "中央アフリカ共和国"
    },
    "CG": {
        "en": "Congo - Brazzaville",
        "ja": "コンゴ - ブラザビル"
    },
    "CH": {
        "en": "Switzerland",
        "ja": "スイス"
    },
    "CI": {
        "en": "Côte d'Ivoire",
        "ja": "コートジボワール"
    },
    "CK": {
        "en": "Cook Islands",
        "ja": "クック諸島"
    },
    "CL": {
        "en": "Chile",
        "ja": "チリ"
    },
    "CM": {
        "en": "Cameroon",
        "ja": "カメルーン"
    },
    "CN": {
        "en": "China",
        "ja": "中国"
    },
    "CO": {
        "en": "Colombia",
        "ja": "コロンビア"
    },
    "CR": {
        "en": "Costa Rica",
        "ja": "コスタリカ"
    },
    "CU": {
        "en": "Cuba",
        "ja": "キューバ"
    },
    "CV": {
        "en": "Cape Verde",
        "ja": "カーボベルデ"
    },
    "CW": {
        "en": "Curaçao",
        "ja": "キュラソー"
    },
    "CX": {
        "en": "Christmas Island",
        "ja": "クリスマス島"
    },
    "CY": {
        "en": "Cyprus",
        "ja": "キプロス"
    },
    "CZ": {
        "en": "Czechia",
        "ja": "チェコ"
    },
    "DE": {
        "en": "Germany",
        "ja": "ドイツ"
    },
    "DJ": {
        "en": "Djibouti",
        "ja": "ジブチ"
    },
    "DK": {
        "en": "Denmark",
        "ja": "デンマーク"
    },
    "DM": {
        "en": "Dominica",
        "ja": "ドミニカ"
    },
    "DO": {
        "en": "Dominican Republic",
        "ja": "ドミニカ共和国"
    },
    "DZ": {
        "en": "Algeria",
        "ja": "アルジェリア"
    },
    "EC": {
        "en": "Ecuador",
        "ja": "エクアドル"
    },
    "EE": {
        "en": "Estonia",
        "ja": "エストニア"
    },
    "EG": {
        "en": "Egypt",
        "ja": "エジプト"
    },
    "EH": {
        "en": "Western Sahara",
        "ja": "西サハラ"
    },
    "ER": {
        "en": "Eritrea",
        "ja": "エリトリア"
    },
    "ES": {
        "en": "Spain",
        "ja": "スペイン"
    },
    "ET": {
        "en": "Ethiopia",
        "ja": "エチオピア"
    },
    "FI": {
        "en": "Finland",
        "ja": "フィンランド"
    },
    "FJ": {
        "en": "Fiji",
        "ja": "フィジー"
    },
    "FK": {
        "en": "Falkland Islands",
        "ja": "フォークランド諸島"
    },
    "FO": {
        "en": "Faroe Islands",
        "ja": "フェロー諸島"
    },
    "FR": {
        "en": "France",
        "ja": "フランス"
    },
    "GA": {
        "en": "Gabon",
        "ja": "ガボン"
    },
    "GB": {
        "en": "United Kingdom",
        "ja": "イギリス"
    },
    "GD": {
        "en": "Grenada",
        "ja": "グレナダ"
    },
    "GE": {
        "en": "Georgia",
        "ja": "ジョージア"
    },
    "GF": {
        "en": "French Guiana",
        "ja": "フランス領ギアナ"
    },
    "GG": {
        "en": "Guernsey",
        "ja": "ガーンジー島"
    },
    "GH": {
        "en": "Ghana",
        "ja": "ガーナ"
    },
    "GI": {
        "en": "Gibraltar",
        "ja": "ジブラルタル"
    },
    "GL": {
        "en": "Greenland",
        "ja": "グリーンランド"
    },
    "GM": {
        "en": "Gambia",
        "ja": "ガンビア"
    },
    "GN": {
        "en": "Guinea",
        "ja": "ギニア"
    },
    "GP": {
        "en": "Guadeloupe",
        "ja": "グアドループ"
    },
    "GQ": {
        "en": "Equatorial Guinea",
        "ja": "赤道ギニア"
    },
    "GR": {
        "en": "Greece",
        "ja": "ギリシャ"
    },
    "GS": {
        "en": "South Georgia & South Sandwich Islands",
        "ja": "サウスジョージア島とサウスサンドイッチ諸島"
    },
    "GT": {
        "en": "Guatemala",
        "ja": "グアテマラ"
    },
    "GW": {
        "en": "Guinea-Bissau",
        "ja": "ギニアビサウ"
    },
    "GY": {
        "en": "Guyana",
        "ja": "ガイアナ"
    },
    "HK": {
        "en": "Hong Kong SAR",
        "ja": "香港特別行政区"
    },
    "HM": {
        "en": "Heard & McDonald Islands",
        "ja": "ハード島とマクドナルド諸島"
    },
    "HN": {
        "en": "Honduras",
        "ja": "ホンジュラス"
    },
    "HR": {
        "en": "Croatia",
        "ja": "クロアチア"
    },
    "HT": {
        "en": "Haiti",
        "ja": "ハイチ"
    },
    "HU": {
        "en": "Hungary",
        "ja": "ハンガリー"
    },
    "ID": {
        "en": "Indonesia",
        "ja": "インドネシア"
    },
    "IE": {
        "en": "Ireland",
        "ja": "アイルランド"
    },
    "IL": {
        "en": "Israel",
        "ja": "イスラエル"
    },
    "IM": {
        "en": "Isle of Man",
        "ja": "マン島"
    },
    "IN": {
        "en": "India",
        "ja": "インド"
    },
    "IO": {
        "en": "British Indian Ocean Territory",
        "ja": "英領インド洋領土"
    },
    "IQ": {
        "en": "Iraq",
        "ja": "イラク"
    },
    "IR": {
        "en": "Iran",
        "ja": "イラン"
    },
    "IS": {
        "en": "Iceland",
        "ja": "アイスランド"
    },
    "IT": {
        "en": "Italy",
        "ja": "イタリア"
    },
    "JE": {
        "en": "Jersey",
        "ja": "ジャージー"
    },
    "JM": {
        "en": "Jamaica",
        "ja": "ジャマイカ"
    },
    "JO": {
        "en": "Jordan",
        "ja": "ヨルダン"
    },
    "JP": {
        "en": "Japan",
        "ja": "日本"
    },
    "KE": {
        "en": "Kenya",
        "ja": "ケニア"
    },
    "KG": {
        "en": "Kyrgyzstan",
        "ja": "キルギス"
    },
    "KH": {
        "en": "Cambodia",
        "ja": "カンボジア"
    },
    "KI": {
        "en": "Kiribati",
        "ja": "キリバス"
    },
    "KM": {
        "en": "Comoros",
        "ja": "コモロ"
    },
    "KN": {
        "en": "St. Kitts & Nevis",
        "ja": "セントクリストファー・ネイビス"
    },
    "KP": {
        "en": "North Korea",
        "ja": "北朝鮮"
    },
    "KR": {
        "en": "South Korea",
        "ja": "韓国"
    },
    "KW": {
        "en": "Kuwait",
        "ja": "クウェート"
    },
    "KY": {
        "en": "Cayman Islands",
        "ja": "ケイマン諸島"
    },
    "KZ": {
        "en": "Kazakhstan",
        "ja": "カザフスタン"
    },
    "LA": {
        "en": "Laos",
        "ja": "ラオス"
    },
    "LB": {
        "en": "Lebanon",
        "ja": "レバノン"
    },
    "LC": {
        "en": "St. Lucia",
        "ja": "セントルシア"
    },
    "LI": {
        "en": "Liechtenstein",
        "ja": "リヒテンシュタイン"
    },
    "LK": {
        "en": "Sri Lanka",
        "ja": "スリランカ"
    },
    "LR": {
        "en": "Liberia",
        "ja": "リベリア"
    },
    "LS": {
        "en": "Lesotho",
        "ja": "レソト"
    },
    "LT": {
        "en": "Lithuania",
        "ja": "リトアニア"
    },
    "LU": {
        "en": "Luxembourg",
        "ja": "ルクセンブルク"
    },
    "LV": {
        "en": "Latvia",
        "ja": "ラトビア"
    },
    "LY": {
        "en": "Libya",
        "ja": "リビア"
    },
    "MA": {
        "en": "Morocco",
        "ja": "モロッコ"
    },
    "MC": {
        "en": "Monaco",
        "ja": "モナコ"
    },
    "MD": {
        "en": "Moldova",
        "ja": "モルドバ"
    },
    "ME": {
        "en": "Montenegro",
        "ja": "モンテネグロ"
    },
    "MF": {
        "en": "St. Martin",
        "ja": "セント・マーティン"
    },
    "MG": {
        "en": "Madagascar",
        "ja": "マダガスカル"
    },
    "MK": {
        "en": "North Macedonia",
        "ja": "北マケドニア"
    },
    "ML": {
        "en": "Mali",
        "ja": "マリ"
    },
    "MM": {
        "en": "Myanmar (Burma)",
        "ja": "ミャンマー (ビルマ)"
    },
    "MN": {
        "en": "Mongolia",
        "ja": "モンゴル"
    },
    "MO": {
        "en": "Macao SAR",
        "ja": "マカオ特別行政区"
    },
    "MQ": {
        "en": "Martinique",
        "ja": "マルティニーク"
    },
    "MR": {
        "en": "Mauritania",
        "ja": "モーリタニア"
    },
    "MS": {
        "en": "Montserrat",
        "ja": "モントセラト"
    },
    "MT": {
        "en": "Malta",
        "ja": "マルタ"
    },
    "MU": {
        "en": "Mauritius",
        "ja": "モーリシャス"
    },
    "MV": {
        "en": "Maldives",
        "ja": "モルディブ"
    },
    "MW": {
        "en": "Malawi",
        "ja": "マラウイ"
    },
    "MX": {
        "en": "Mexico",
        "ja": "メキシコ"
    },
    "MY": {
        "en": "Malaysia",
        "ja": "マレーシア"
    },
    "MZ": {
        "en": "Mozambique",
        "ja": "モザンビーク"
    },
    "NA": {
        "en": "Namibia",
        "ja": "ナミビア"
    },
    "NC": {
        "en": "New Caledonia",
        "ja": "ニューカレドニア"
    },
    "NE": {
        "en": "Niger",
        "ja": "ニジェール"
    },
    "NF": {
        "en": "Norfolk Island",
        "ja": "ノーフォーク島"
    },
    "NG": {
        "en": "Nigeria",
        "ja": "ナイジェリア"
    },
    "NI": {
        "en": "Nicaragua",
        "ja": "ニカラグア"
    },
    "NL": {
        "en": "Netherlands",
        "ja": "オランダ"
    },
    "NO": {
        "en": "Norway",
        "ja": "ノルウェー"
    },
    "NP": {
        "en": "Nepal",
        "ja": "ネパール"
    },
    "NR": {
        "en": "Nauru",
        "ja": "ナウル"
    },
    "NU": {
        "en": "Niue",
        "ja": "ニウエ"
    },
    "NZ": {
        "en": "New Zealand",
        "ja": "ニュージーランド"
    },
    "OM": {
        "en": "Oman",
        "ja": "オマーン"
    },
    "PA": {
        "en": "Panama",
        "ja": "パナマ"
    },
    "PE": {
        "en": "Peru",
        "ja": "ペルー"
    },
    "PF": {
        "en": "French Polynesia",
        "ja": "フランス領ポリネシア"
    },
    "PG": {
        "en": "Papua New Guinea",
        "ja": "パプアニューギニア"
    },
    "PH": {
        "en": "Philippines",
        "ja": "フィリピン"
    },
    "PK": {
        "en": "Pakistan",
        "ja": "パキスタン"
    },
    "PL": {
        "en": "Poland",
        "ja": "ポーランド"
    },
    "PM": {
        "en": "St. Pierre & Miquelon",
        "ja": "サンピエール島・ミクロン島"
    },
    "PN": {
        "en": "Pitcairn Islands",
        "ja": "ピトケアン諸島"
    },
    "PS": {
        "en": "Palestinian Territories",
        "ja": "パレスチナ領土"
    },
    "PT": {
        "en": "Portugal",
        "ja": "ポルトガル"
    },
    "PY": {
        "en": "Paraguay",
        "ja": "パラグアイ"
    },
    "QA": {
        "en": "Qatar",
        "ja": "カタール"
    },
    "RE": {
        "en": "Réunion",
        "ja": "再会"
    },
    "RO": {
        "en": "Romania",
        "ja": "ルーマニア"
    },
    "RS": {
        "en": "Serbia",
        "ja": "セルビア"
    },
    "RU": {
        "en": "Russia",
        "ja": "ロシア"
    },
    "RW": {
        "en": "Rwanda",
        "ja": "ルワンダ"
    },
    "SA": {
        "en": "Saudi Arabia",
        "ja": "サウジアラビア"
    },
    "SB": {
        "en": "Solomon Islands",
        "ja": "ソロモン諸島"
    },
    "SC": {
        "en": "Seychelles",
        "ja": "セーシェル"
    },
    "SD": {
        "en": "Sudan",
        "ja": "スーダン"
    },
    "SE": {
        "en": "Sweden",
        "ja": "スウェーデン"
    },
    "SG": {
        "en": "Singapore",
        "ja": "シンガポール"
    },
    "SH": {
        "en": "St. Helena",
        "ja": "セントヘレナ"
    },
    "SI": {
        "en": "Slovenia",
        "ja": "スロベニア"
    },
    "SJ": {
        "en": "Svalbard & Jan Mayen",
        "ja": "スバールバル諸島とヤンマイエン島"
    },
    "SK": {
        "en": "Slovakia",
        "ja": "スロバキア"
    },
    "SL": {
        "en": "Sierra Leone",
        "ja": "シエラレオネ"
    },
    "SM": {
        "en": "San Marino",
        "ja": "サンマリノ"
    },
    "SN": {
        "en": "Senegal",
        "ja": "セネガル"
    },
    "SO": {
        "en": "Somalia",
        "ja": "ソマリア"
    },
    "SR": {
        "en": "Suriname",
        "ja": "スリナム"
    },
    "SS": {
        "en": "South Sudan",
        "ja": "南スーダン"
    },
    "ST": {
        "en": "São Tomé & Príncipe",
        "ja": "サントメ・プリンシペ"
    },
    "SV": {
        "en": "El Salvador",
        "ja": "エルサルバドル"
    },
    "SX": {
        "en": "Sint Maarten",
        "ja": "シント・マールテン島"
    },
    "SY": {
        "en": "Syria",
        "ja": "シリア"
    },
    "SZ": {
        "en": "Eswatini",
        "ja": "エスワティニ"
    },
    "TA": {
        "en": "Tristan da Cunha",
        "ja": "トリスタン・ダ・クーニャ"
    },
    "TC": {
        "en": "Turks & Caicos Islands",
        "ja": "タークス・カイコス諸島"
    },
    "TD": {
        "en": "Chad",
        "ja": "チャド"
    },
    "TF": {
        "en": "French Southern Territories",
        "ja": "フランス領南方領土"
    },
    "TG": {
        "en": "Togo",
        "ja": "持ち帰り"
    },
    "TH": {
        "en": "Thailand",
        "ja": "タイ"
    },
    "TJ": {
        "en": "Tajikistan",
        "ja": "タジキスタン"
    },
    "TK": {
        "en": "Tokelau",
        "ja": "トケラウ"
    },
    "TL": {
        "en": "Timor-Leste",
        "ja": "東ティモール"
    },
    "TM": {
        "en": "Turkmenistan",
        "ja": "トルクメニスタン"
    },
    "TN": {
        "en": "Tunisia",
        "ja": "チュニジア"
    },
    "TO": {
        "en": "Tonga",
        "ja": "トンガ"
    },
    "TR": {
        "en": "Türkiye",
        "ja": "トゥルキエ"
    },
    "TT": {
        "en": "Trinidad & Tobago",
        "ja": "トリニダード・トバゴ"
    },
    "TV": {
        "en": "Tuvalu",
        "ja": "ツバル"
    },
    "TW": {
        "en": "Taiwan",
        "ja": "台湾"
    },
    "TZ": {
        "en": "Tanzania",
        "ja": "タンザニア"
    },
    "UA": {
        "en": "Ukraine",
        "ja": "ウクライナ"
    },
    "UG": {
        "en": "Uganda",
        "ja": "ウガンダ"
    },
    "UM": {
        "en": "U.S. Outlying Islands",
        "ja": "米国の離島"
    },
    "US": {
        "en": "United States",
        "ja": "アメリカ"
    },
    "UY": {
        "en": "Uruguay",
        "ja": "ウルグアイ"
    },
    "UZ": {
        "en": "Uzbekistan",
        "ja": "ウズベキスタン"
    },
    "VA": {
        "en": "Vatican City",
        "ja": "バチカン市"
    },
    "VC": {
        "en": "St. Vincent & Grenadines",
        "ja": "セントビンセントおよびグレナディーン諸島"
    },
    "VE": {
        "en": "Venezuela",
        "ja": "ベネズエラ"
    },
    "VG": {
        "en": "British Virgin Islands",
        "ja": "イギリス領バージン諸島"
    },
    "VN": {
        "en": "Vietnam",
        "ja": "ベトナム"
    },
    "VU": {
        "en": "Vanuatu",
        "ja": "バヌアツ"
    },
    "WF": {
        "en": "Wallis & Futuna",
        "ja": "ウォリス＆フツナ"
    },
    "WS": {
        "en": "Samoa",
        "ja": "サモア"
    },
    "XK": {
        "en": "Kosovo",
        "ja": "コソボ"
    },
    "YE": {
        "en": "Yemen",
        "ja": "イエメン"
    },
    "YT": {
        "en": "Mayotte",
        "ja": "マヨット"
    },
    "ZA": {
        "en": "South Africa",
        "ja": "南アフリカ"
    },
    "ZM": {
        "en": "Zambia",
        "ja": "ザンビア"
    },
    "ZW": {
        "en": "Zimbabwe",
        "ja": "ジンバブエ"
    },
    "ZZ": {
        "en": "Unknown Region",
        "ja": "未知の領域"
    }}

EXISTING_SHOPIFY_COUNTRY_CODE = [
    'AC',
    'AD',
    'AE',
    'AF',
    'AG',
    'AI',
    'AL',
    'AM',
    'AO',
    'AR',
    'AT',
    'AU',
    'AW',
    'AX',
    'AZ',
    'BA',
    'BB',
    'BD',
    'BE',
    'BF',
    'BG',
    'BH',
    'BJ',
    'BL',
    'BM',
    'BN',
    'BO',
    'BQ',
    'BR',
    'BS',
    'BT',
    'BW',
    'BY',
    'BZ',
    'CA',
    'CC',
    'CD',
    'CF',
    'CG',
    'CH',
    'CI',
    'CK',
    'CL',
    'CM',
    'CN',
    'CO',
    'CR',
    'CV',
    'CW',
    'CX',
    'CY',
    'CZ',
    'DE',
    'DJ',
    'DK',
    'DM',
    'DO',
    'DZ',
    'EC',
    'EE',
    'EG',
    'ES',
    'ET',
    'FI',
    'FJ',
    'FK',
    'FO',
    'FR',
    'GA',
    'GB',
    'GD',
    'GE',
    'GF',
    'GG',
    'GH',
    'GI',
    'GL',
    'GN',
    'GP',
    'GQ',
    'GR',
    'GT',
    'GY',
    'HK',
    'HN',
    'HR',
    'HT',
    'HU',
    'ID',
    'IE',
    'IL',
    'IM',
    'IN',
    'IO',
    'IQ',
    'IS',
    'IT',
    'JE',
    'JM',
    'JO',
    'JP',
    'KE',
    'KG',
    'KH',
    'KM',
    'KN',
    'KR',
    'KW',
    'KY',
    'KZ',
    'LA',
    'LB',
    'LC',
    'LI',
    'LK',
    'LR',
    'LS',
    'LT',
    'LU',
    'LV',
    'LY',
    'MA',
    'MC',
    'MD',
    'ME',
    'MF',
    'MG',
    'MK',
    'ML',
    'MM',
    'MN',
    'MO',
    'MQ',
    'MR',
    'MS',
    'MT',
    'MU',
    'MV',
    'MW',
    'MX',
    'MY',
    'MZ',
    'NA',
    'NC',
    'NE',
    'NG',
    'NI',
    'NL',
    'NO',
    'Norway',
    'NP',
    'NR',
    'NZ',
    'OM',
    'PA',
    'PE',
    'PF',
    'PG',
    'PH',
    'PK',
    'PL',
    'PM',
    'PS',
    'PT',
    'PY',
    'QA',
    'RE',
    'RO',
    'RS',
    'RU',
    'RW',
    'SA',
    'SB',
    'SC',
    'SD',
    'SE',
    'SG',
    'SI',
    'SJ',
    'SK',
    'SL',
    'SM',
    'SN',
    'SO',
    'SR',
    'SV',
    'SX',
    'SZ',
    'TA',
    'TC',
    'TG',
    'TH',
    'TJ',
    'TL',
    'TM',
    'TN',
    'TO',
    'TR',
    'TT',
    'TW',
    'TZ',
    'UA',
    'UG',
    'UM',
    'US',
    'UY',
    'UZ',
    'VA',
    'VC',
    'VE',
    'VG',
    'VN',
    'VU',
    'WF',
    'WS',
    'XK',
    'YE',
    'YT',
    'ZA',
    'ZM',
    'ZW'
]

DG_PAYMENT_METHOD = {
    'en':
        [
            {
                'slug': '',
                'display': ''
            },
            {
                'slug': '00',
                'display': 'All payment types'
            },
            {
                'slug': '01',
                'display': 'Card'
            },
            {
                'slug': '02',
                'display': 'Convenience Store'
            },
            {
                'slug': '03',
                'display': 'Electronic Money'
            },
            {
                'slug': '04',
                'display': 'Banks (Pagey et al.)'
            },
            {
                'slug': '05',
                'display': 'Shopping Credit (Orico)'
            },
            {
                'slug': '06',
                'display': 'Bank Transfer (Resona)'
            },
        ],
    'ja':
        [
            {
                'slug': '',
                'display': ''
            },
            {
                'slug': '00',
                'display': 'すべての決済方法'
            },
            {
                'slug': '01',
                'display': 'クレジットカード'
            },
            {
                'slug': '02',
                'display': 'コンビニ'
            },
            {
                'slug': '03',
                'display': '電子マネー'
            },
            {
                'slug': '04',
                'display': '銀行(ペイジー 他)  '
            },
            {
                'slug': '05',
                'display': 'ショッピングクレジット(オリコ)'
            },
            {
                'slug': '06',
                'display': '銀行振込(りそな)'
            },
        ]
}
MONTHS_NAME = {
    'en': [
        {'slug': '', 'display': ''},  # Placeholder for index 0
        {'slug': '1', 'display': 'January'},
        {'slug': '2', 'display': 'February'},
        {'slug': '3', 'display': 'March'},
        {'slug': '4', 'display': 'April'},
        {'slug': '5', 'display': 'May'},
        {'slug': '6', 'display': 'June'},
        {'slug': '7', 'display': 'July'},
        {'slug': '8', 'display': 'August'},
        {'slug': '9', 'display': 'September'},
        {'slug': '10', 'display': 'October'},
        {'slug': '11', 'display': 'November'},
        {'slug': '12', 'display': 'December'}
    ],
    'ja': [
        {'slug': '', 'display': ''},  # Placeholder for index 0
        {'slug': '1', 'display': '一月'},
        {'slug': '2', 'display': '二月'},
        {'slug': '3', 'display': '三月'},
        {'slug': '4', 'display': '四月'},
        {'slug': '5', 'display': '五月'},
        {'slug': '6', 'display': '六月'},
        {'slug': '7', 'display': '七月'},
        {'slug': '8', 'display': '八月'},
        {'slug': '9', 'display': '九月'},
        {'slug': '10', 'display': '十月'},
        {'slug': '11', 'display': '十一月'},
        {'slug': '12', 'display': '十二月'}
    ]
}

KEYWORDS_YEAR_RANGE = [
    {'en': '1 Year', 'ja': '1年'},
    {'en': '2 Years', 'ja': '2年'},
    {'en': '3 Years', 'ja': '3年'},
    {'en': '4 Years', 'ja': '4年'},
]

COUNTRY_CODE = [
    {'country_code': 'ar', 'name': 'Argentina', 'code': '+54'},
    {'country_code': 'de', 'name': 'Austria', 'code': '+43'},
    {'country_code': 'fr', 'name': 'Belgium', 'code': '+32'},
    {'country_code': 'es', 'name': 'Bolivia', 'code': '+591'},
    {'country_code': 'pt', 'name': 'Brazil', 'code': '+55'},
    {'country_code': 'es', 'name': 'Chile', 'code': '+56'},
    {'country_code': 'zh-CN', 'name': 'China', 'code': '+86'},
    {'country_code': 'es', 'name': 'Colombia', 'code': '+57'},
    {'country_code': 'es', 'name': 'Costa Rica', 'code': '+506'},
    {'country_code': 'es', 'name': 'Cuba', 'code': '+53'},
    {'country_code': 'el', 'name': 'Cyprus', 'code': '+357'},
    {'country_code': 'es', 'name': 'Dominican Republic*', 'code': '+1809'},
    {'country_code': 'es', 'name': 'Ecuador', 'code': '+593'},
    {'country_code': 'ar', 'name': 'Egypt', 'code': '+20'},
    {'country_code': 'es', 'name': 'El Salvador', 'code': '+503'},
    {'country_code': 'et', 'name': 'Estonia', 'code': '+372'},
    {'country_code': 'fr', 'name': 'France', 'code': '+33'},
    {'country_code': 'de', 'name': 'Germany', 'code': '+49'},
    {'country_code': 'es', 'name': 'Guatemala', 'code': '+502'},
    {'country_code': 'es', 'name': 'Honduras', 'code': '+504'},
    {'country_code': 'en', 'name': 'India', 'code': '+91'},
    {'country_code': 'id', 'name': 'Indonesia', 'code': '+62'},
    {'country_code': 'it', 'name': 'Italy', 'code': '+39'},
    {'country_code': 'ja', 'name': 'Japan', 'code': '+81'},
    {'country_code': 'ar', 'name': 'Kuwait', 'code': '+965'},
    {'country_code': 'lt', 'name': 'Lithuania', 'code': '+370'},
    {'country_code': 'de', 'name': 'Luxembourg', 'code': '+352'},
    {'country_code': 'es', 'name': 'Mexico', 'code': '+52'},
    {'country_code': 'fr', 'name': 'Monaco', 'code': '+377'},
    {'country_code': 'es', 'name': 'Nicaragua', 'code': '+505'},
    {'country_code': 'ar', 'name': 'Oman', 'code': '+968'},
    {'country_code': 'es', 'name': 'Panama', 'code': '+507'},
    {'country_code': 'es', 'name': 'Paraguay', 'code': '+595'},
    {'country_code': 'es', 'name': 'Peru', 'code': '+51'},
    {'country_code': 'pl', 'name': 'Poland', 'code': '+48'},
    {'country_code': 'pt', 'name': 'Portugal', 'code': '+351'},
    {'country_code': 'ro', 'name': 'Romania', 'code': '+226'},
    {'country_code': 'it', 'name': 'San Marino', 'code': '+378'},
    {'country_code': 'ar', 'name': 'Saudi Arabia', 'code': '+966'},
    {'country_code': 'sk', 'name': 'Slovakia', 'code': '+421'},
    {'country_code': 'af', 'name': 'South Africa', 'code': '+27'},
    {'country_code': 'es', 'name': 'Spain', 'code': '+34'},
    {'country_code': 'de', 'name': 'Switzerland', 'code': '+41'},
    {'country_code': 'uk', 'name': 'Ukraine', 'code': '+380'},
    {'country_code': 'ar', 'name': 'United Arab Emirates', 'code': '+971'},
    {'country_code': 'en-GB', 'name': 'United Kingdom', 'code': '+44'},
    {'country_code': 'en-us', 'name': 'United States', 'code': '+1'},
    {'country_code': 'es', 'name': 'Uruguay', 'code': '+598'},
    {'country_code': 'it', 'name': 'Vatican City', 'code': '+379'},
    {'country_code': 'es', 'name': 'Venezuela', 'code': '+58'},
    {'country_code': 'vi', 'name': 'Vietnam', 'code': '+84'}
]


HOLIDAY_COUNTRY_CODE = [  # VACCANZA Support Only
    ("AL", "Albania"),
    ("DZ", "Algeria"),
    ("AS", "American Samoa"),
    ("AD", "Andorra"),
    ("AO", "Angola"),
    ("AR", "Argentina"),
    ("AM", "Armenia"),
    ("AW", "Aruba"),
    ("AU", "Australia"),
    ("AT", "Austria"),
    ("AZ", "Azerbaijan"),
    ("BS", "Bahamas"),
    ("BH", "Bahrain"),
    ("BD", "Bangladesh"),
    ("BB", "Barbados"),
    ("BY", "Belarus"),
    ("BE", "Belgium"),
    ("BZ", "Belize"),
    ("BO", "Bolivia"),
    ("BA", "Bosnia and Herzegovina"),
    ("BW", "Botswana"),
    ("BR", "Brazil"),
    ("BN", "Brunei"),
    ("BG", "Bulgaria"),
    ("BF", "Burkina Faso"),
    ("BI", "Burundi"),
    ("KH", "Cambodia"),
    ("CM", "Cameroon"),
    ("CA", "Canada"),
    ("TD", "Chad"),
    ("CL", "Chile"),
    ("CN", "China"),
    ("CO", "Colombia"),
    ("CR", "Costa Rica"),
    ("HR", "Croatia"),
    ("CU", "Cuba"),
    ("CW", "Curacao"),
    ("CY", "Cyprus"),
    ("CZ", "Czechia"),
    ("DK", "Denmark"),
    ("DJ", "Djibouti"),
    ("DO", "Dominican Republic"),
    ("EC", "Ecuador"),
    ("EG", "Egypt"),
    ("SV", "El Salvador"),
    ("EE", "Estonia"),
    ("SZ", "Eswatini"),
    ("ET", "Ethiopia"),
    ("FI", "Finland"),
    ("FR", "France"),
    ("GA", "Gabon"),
    ("GE", "Georgia"),
    ("DE", "Germany"),
    ("GH", "Ghana"),
    ("GR", "Greece"),
    ("GL", "Greenland"),
    ("GU", "Guam"),
    ("GT", "Guatemala"),
    ("HN", "Honduras"),
    ("HK", "Hong Kong"),
    ("HU", "Hungary"),
    ("IS", "Iceland"),
    ("IN", "India"),
    ("ID", "Indonesia"),
    ("IR", "Iran"),
    ("IE", "Ireland"),
    ("IM", "Isle of Man"),
    ("IL", "Israel"),
    ("IT", "Italy"),
    ("JM", "Jamaica"),
    ("JP", "Japan"),
    ("JE", "Jersey"),
    ("JO", "Jordan"),
    ("KZ", "Kazakhstan"),
    ("KE", "Kenya"),
    ("KW", "Kuwait"),
    ("KG", "Kyrgyzstan"),
    ("LA", "Laos"),
    ("LV", "Latvia"),
    ("LS", "Lesotho"),
    ("LI", "Liechtenstein"),
    ("LT", "Lithuania"),
    ("LU", "Luxembourg"),
    ("MG", "Madagascar"),
    ("MW", "Malawi"),
    ("MY", "Malaysia"),
    ("MV", "Maldives"),
    ("MT", "Malta"),
    ("MH", "Marshall Islands"),
    ("MX", "Mexico"),
    ("MD", "Moldova"),
    ("MC", "Monaco"),
    ("ME", "Montenegro"),
    ("MA", "Morocco"),
    ("MZ", "Mozambique"),
    ("NA", "Namibia"),
    ("NL", "Netherlands"),
    ("NZ", "New Zealand"),
    ("NI", "Nicaragua"),
    ("NG", "Nigeria"),
    ("MP", "Northern Mariana Islands"),
    ("MK", "North Macedonia"),
    ("NO", "Norway"),
    ("PK", "Pakistan"),
    ("PW", "Palau"),
    ("PA", "Panama"),
    ("PG", "Papua New Guinea"),
    ("PY", "Paraguay"),
    ("PE", "Peru"),
    ("PH", "Philippines"),
    ("PL", "Poland"),
    ("PT", "Portugal"),
    ("PR", "Puerto Rico"),
    ("RO", "Romania"),
    ("RU", "Russia"),
    ("SM", "San Marino"),
    ("SA", "Saudi Arabia"),
    ("RS", "Serbia"),
    ("SC", "Seychelles"),
    ("SG", "Singapore"),
    ("SK", "Slovakia"),
    ("SI", "Slovenia"),
    ("ZA", "South Africa"),
    ("KR", "South Korea"),
    ("ES", "Spain"),
    ("SE", "Sweden"),
    ("CH", "Switzerland"),
    ("TW", "Taiwan"),
    ("TZ", "Tanzania"),
    ("TH", "Thailand"),
    ("TL", "Timor Leste"),
    ("TO", "Tonga"),
    ("TN", "Tunisia"),
    ("TR", "Turkey"),
    ("UA", "Ukraine"),
    ("AE", "United Arab Emirates"),
    ("GB", "United Kingdom"),
    ("UM", "United States Minor Outlying Islands"),
    ("US", "United States of America"),
    ("VI", "United States Virgin Islands"),
    ("UY", "Uruguay"),
    ("UZ", "Uzbekistan"),
    ("VU", "Vanuatu"),
    ("VA", "Vatican City"),
    ("VE", "Venezuela"),
    ("VN", "Vietnam"),
    ("ZM", "Zambia"),
    ("ZW", "Zimbabwe")
]

ITEM_USAGE_CATEGORY = 'commerce_items'
ORDER_USAGE_CATEGORY = 'commerce_orders'
SUBSCRIPTION_USAGE_CATEGORY = 'commerce_subscription'
CONTACT_USAGE_CATEGORY = 'contacts'
COMPANY_USAGE_CATEGORY = 'company'
INVENTORY_USAGE_CATEGORY = 'commerce_inventory'
INVENTORY_TRANSACTION_USAGE_CATEGORY = 'commerce_inventory_transaction'
WAREHOUSE_USAGE_CATEGORY = 'commerce_inventory_warehouse'
ESTIMATE_USAGE_CATEGORY = 'estimates'
DELIVERY_SLIP_USAGE_CATEGORY = 'delivery_slips'
INVOICE_USAGE_CATEGORY = 'invoices'
RECEIPT_USAGE_CATEGORY = 'receipts'
PURCHASE_ORDER_USAGE_CATEGORY = 'purchaseorder'
BILL_USAGE_CATEGORY = 'bill'
SLIP_USAGE_CATEGORY = 'slips'
EXPENSE_USAGE_CATEGORY = 'expense'
USER_USAGE_CATEGORY = 'users'
JOURNAL_USAGE_CATEGORY = 'journal'
USAGE_CATEGORIES = [
    (ITEM_USAGE_CATEGORY, 'Items'),
    (ORDER_USAGE_CATEGORY, 'Orders'),
    (SUBSCRIPTION_USAGE_CATEGORY, 'Subscriptions'),
    (CONTACT_USAGE_CATEGORY, 'Contacts'),
    (COMPANY_USAGE_CATEGORY, 'Companies'),
    (INVENTORY_USAGE_CATEGORY, 'Inventory'),
    (INVENTORY_TRANSACTION_USAGE_CATEGORY, 'Inventory Transactions'),
    (WAREHOUSE_USAGE_CATEGORY, 'Warehouses'),
    (ESTIMATE_USAGE_CATEGORY, 'Estimates'),
    (DELIVERY_SLIP_USAGE_CATEGORY, 'Delivery Slips'),
    (INVOICE_USAGE_CATEGORY, 'Invoices'),
    (RECEIPT_USAGE_CATEGORY, 'Receipts'),
    (PURCHASE_ORDER_USAGE_CATEGORY, 'Purchase Orders'),
    (BILL_USAGE_CATEGORY, 'Bill Entries'),
    (EXPENSE_USAGE_CATEGORY, 'Expense Entries'),
    (USER_USAGE_CATEGORY, 'Users'),
    (SLIP_USAGE_CATEGORY, 'Slips'),
    (JOURNAL_USAGE_CATEGORY, 'JournalEntry')
]

USAGE_CATEGORIES_TITLE = {
    ITEM_USAGE_CATEGORY: {
        'en': 'Item Records',
        'ja': '商品レコード',
    },
    ORDER_USAGE_CATEGORY: {
        'en': 'Order Records',
        'ja': '受注レコード',
    },
    SUBSCRIPTION_USAGE_CATEGORY: {
        'en': 'Subscription Records',
        'ja': 'サブスクリプションレコード',
    },
    CONTACT_USAGE_CATEGORY: {
        'en': 'Contact Records',
        'ja': '連絡先レコード',
    },
    COMPANY_USAGE_CATEGORY: {
        'en': 'Company Records',
        'ja': '企業レコード',
    },
    INVENTORY_USAGE_CATEGORY: {
        'en': 'Inventory Records',
        'ja': '在庫レコード',
    },
    INVENTORY_TRANSACTION_USAGE_CATEGORY: {
        'en': 'Inventory Transaction Records',
        'ja': '入出庫レコード',
    },
    WAREHOUSE_USAGE_CATEGORY: {
        'en': 'Location Records',
        'ja': 'ロケーションレコード',
    },
    ESTIMATE_USAGE_CATEGORY: {
        'en': 'Estimate Records',
        'ja': '見積レコード',
    },
    DELIVERY_SLIP_USAGE_CATEGORY: {
        'en': 'Delivery Note Records',
        'ja': '納品レコード',
    },
    INVOICE_USAGE_CATEGORY: {
        'en': 'Invoice Records',
        'ja': '売上請求レコード',
    },
    RECEIPT_USAGE_CATEGORY: {
        'en': 'Payment Records',
        'ja': '入金レコード',
    },
    SLIP_USAGE_CATEGORY: {
        'en': 'Slip Records',
        'ja': '伝票レコード',
    },
    PURCHASE_ORDER_USAGE_CATEGORY: {
        'en': 'Purchase Order Records',
        'ja': '発注レコード',
    },
    BILL_USAGE_CATEGORY: {
        'en': 'Bill Records',
        'ja': '支払請求レコード',
    },
    EXPENSE_USAGE_CATEGORY: {
        'en': 'Expense Records',
        'ja': '経費レコード',
    },
    USER_USAGE_CATEGORY: {
        'en': 'Total Users',
        'ja': 'ユーザー数',
    },
    JOURNAL_USAGE_CATEGORY: {
        'en': 'Journal Entry Records',
        'ja': '仕訳レコード',
    },
}

STARTER_PRICING_TIER = 'free'
STANDARD_PRICING_TIER = 'standard'
PRICING_TIER_OPTIONS = [
    (STARTER_PRICING_TIER, 'Starter Tier'),
    (STANDARD_PRICING_TIER, 'Standard Tier'),
]

PRICING_TIER_TITLE = {
    STARTER_PRICING_TIER: {
        'en': 'Starter Plan',
        'ja': 'スタータープラン',
    },
    STANDARD_PRICING_TIER: {
        'en': 'Standard Plan',
        'ja': 'スタンダードプラン'
    }
}

MONTHLY = 'monthly'
ANNUAL = 'annual'
PAYMENT_FREQUENCY_OPTIONS = [
    (MONTHLY, 'Monthly'),
    (ANNUAL, 'Annual'),
]

ENTRIES = 'entries'
RECORDS = 'records'
PROCESSES = 'processes'
PRICING_TYPES = [
    (ENTRIES, 'Entries'),
    (RECORDS, 'Records'),
    (PROCESSES, 'Processes'),
]

HOLIDAY_COUNTRY_CODES_COLUMNS_DISPLAY = {
    'AL': {'en': 'Albania', 'ja': 'アルバニア'},
    'DZ': {'en': 'Algeria', 'ja': 'アルジェリア'},
    'AS': {'en': 'American Samoa', 'ja': 'アメリカのサモア'},
    'AD': {'en': 'Andorra', 'ja': 'アンドラ'},
    'AO': {'en': 'Angola', 'ja': 'アンゴラ'},
    'AR': {'en': 'Argentina', 'ja': 'アルゼンチン'},
    'AM': {'en': 'Armenia', 'ja': 'アルメニア'},
    'AW': {'en': 'Aruba', 'ja': 'アルバ'},
    'AU': {'en': 'Australia', 'ja': 'オーストラリア'},
    'AT': {'en': 'Austria', 'ja': 'オーストリア'},
    'AZ': {'en': 'Azerbaijan', 'ja': 'アゼルバイジャン'},
    'BS': {'en': 'Bahamas', 'ja': 'バハマ'},
    'BH': {'en': 'Bahrain', 'ja': 'バーレーン'},
    'BD': {'en': 'Bangladesh', 'ja': 'バングラデシュ'},
    'BB': {'en': 'Barbados', 'ja': 'バルバドス'},
    'BY': {'en': 'Belarus', 'ja': 'ベラルーシ'},
    'BE': {'en': 'Belgium', 'ja': 'ベルギー'},
    'BZ': {'en': 'Belize', 'ja': 'ベリーズ'},
    'BO': {'en': 'Bolivia', 'ja': 'ボリビア'},
    'BA': {'en': 'Bosnia and Herzegovina', 'ja': 'ボスニア・ヘルツェゴビナ'},
    'BW': {'en': 'Botswana', 'ja': 'ボツワナ'},
    'BR': {'en': 'Brazil', 'ja': 'ブラジル'},
    'BN': {'en': 'Brunei', 'ja': 'ブルネイ'},
    'BG': {'en': 'Bulgaria', 'ja': 'ブルガリア'},
    'BF': {'en': 'Burkina Faso', 'ja': 'ブルキナファソ'},
    'BI': {'en': 'Burundi', 'ja': 'ブルンジ'},
    'KH': {'en': 'Cambodia', 'ja': 'カンボジア'},
    'CM': {'en': 'Cameroon', 'ja': 'カメルーン'},
    'CA': {'en': 'Canada', 'ja': 'カナダ'},
    'TD': {'en': 'Chad', 'ja': 'チャド'},
    'CL': {'en': 'Chile', 'ja': 'チリ'},
    'CN': {'en': 'China', 'ja': '中国'},
    'CO': {'en': 'Colombia', 'ja': 'コロンビア'},
    'CR': {'en': 'Costa Rica', 'ja': 'コスタリカ'},
    'HR': {'en': 'Croatia', 'ja': 'クロアチア'},
    'CU': {'en': 'Cuba', 'ja': 'キューバ'},
    'CW': {'en': 'Curacao', 'ja': 'キュラソー'},
    'CY': {'en': 'Cyprus', 'ja': 'キプロス'},
    'CZ': {'en': 'Czechia', 'ja': 'チェコ'},
    'DK': {'en': 'Denmark', 'ja': 'デンマーク'},
    'DJ': {'en': 'Djibouti', 'ja': 'djibouti'},
    'DO': {'en': 'Dominican Republic', 'ja': 'ドミニカ共和国'},
    'EC': {'en': 'Ecuador', 'ja': 'エクアドル'},
    'EG': {'en': 'Egypt', 'ja': 'エジプト'},
    'SV': {'en': 'El Salvador', 'ja': '救世主'},
    'EE': {'en': 'Estonia', 'ja': 'エストニア'},
    'SZ': {'en': 'Eswatini', 'ja': 'タトゥーで'},
    'ET': {'en': 'Ethiopia', 'ja': 'エチオピア'},
    'FI': {'en': 'Finland', 'ja': 'フィンランド'},
    'FR': {'en': 'France', 'ja': 'フランス'},
    'GA': {'en': 'Gabon', 'ja': 'ガボン'},
    'GE': {'en': 'Georgia', 'ja': 'ジョージア'},
    'DE': {'en': 'Germany', 'ja': 'ドイツ'},
    'GH': {'en': 'Ghana', 'ja': 'ガーナ'},
    'GR': {'en': 'Greece', 'ja': 'ギリシャ'},
    'GL': {'en': 'Greenland', 'ja': 'グリーンランド'},
    'GU': {'en': 'Guam', 'ja': 'グアム'},
    'GT': {'en': 'Guatemala', 'ja': 'グアテマラ'},
    'HN': {'en': 'Honduras', 'ja': 'ホンジュラス'},
    'HK': {'en': 'Hong Kong', 'ja': '香港'},
    'HU': {'en': 'Hungary', 'ja': 'ハンガリー'},
    'IS': {'en': 'Iceland', 'ja': 'アイスランド'},
    'IN': {'en': 'India', 'ja': 'インド'},
    'ID': {'en': 'Indonesia', 'ja': 'インドネシア'},
    'IR': {'en': 'Iran', 'ja': 'イラン'},
    'IE': {'en': 'Ireland', 'ja': 'アイルランド'},
    'IM': {'en': 'Isle of Man', 'ja': 'マン島'},
    'IL': {'en': 'Israel', 'ja': 'イスラエル'},
    'IT': {'en': 'Italy', 'ja': 'イタリア'},
    'JM': {'en': 'Jamaica', 'ja': 'ジャマイカ'},
    'JP': {'en': 'Japan', 'ja': '日本'},
    'JE': {'en': 'Jersey', 'ja': 'ジャージー'},
    'JO': {'en': 'Jordan', 'ja': 'ヨルダン'},
    'KZ': {'en': 'Kazakhstan', 'ja': 'カザフスタン'},
    'KE': {'en': 'Kenya', 'ja': 'ケニア'},
    'KW': {'en': 'Kuwait', 'ja': 'クウェート'},
    'KG': {'en': 'Kyrgyzstan', 'ja': 'キルギスタン'},
    'LA': {'en': 'Laos', 'ja': 'ラオス'},
    'LV': {'en': 'Latvia', 'ja': 'ラトビア'},
    'LS': {'en': 'Lesotho', 'ja': 'レソト'},
    'LI': {'en': 'Liechtenstein', 'ja': 'リヒテンシュタイン'},
    'LT': {'en': 'Lithuania', 'ja': 'リトアニア'},
    'LU': {'en': 'Luxembourg', 'ja': 'ルクセンブルク'},
    'MG': {'en': 'Madagascar', 'ja': 'マダガスカル'},
    'MW': {'en': 'Malawi', 'ja': 'マラウイ'},
    'MY': {'en': 'Malaysia', 'ja': 'マレーシア'},
    'MV': {'en': 'Maldives', 'ja': 'モルディブ'},
    'MT': {'en': 'Malta', 'ja': 'マルタ'},
    'MH': {'en': 'Marshall Islands', 'ja': 'マーシャル諸島'},
    'MX': {'en': 'Mexico', 'ja': 'メキシコ'},
    'MD': {'en': 'Moldova', 'ja': 'モルドバ'},
    'MC': {'en': 'Monaco', 'ja': 'モナコ'},
    'ME': {'en': 'Montenegro', 'ja': 'モンテネグロ'},
    'MA': {'en': 'Morocco', 'ja': 'モロッコ'},
    'MZ': {'en': 'Mozambique', 'ja': 'モザンビーク'},
    'NA': {'en': 'Namibia', 'ja': 'ナミビア'},
    'NL': {'en': 'Netherlands', 'ja': 'オランダ'},
    'NZ': {'en': 'New Zealand', 'ja': 'ニュージーランド'},
    'NI': {'en': 'Nicaragua', 'ja': 'ニカラグア'},
    'NG': {'en': 'Nigeria', 'ja': 'ナイジェリア'},
    'MP': {'en': 'Northern Mariana Islands', 'ja': '北マリアナ諸島'},
    'MK': {'en': 'North Macedonia', 'ja': '北マケドニア'},
    'NO': {'en': 'Norway', 'ja': 'ノルウェー'},
    'PK': {'en': 'Pakistan', 'ja': 'パキスタン'},
    'PW': {'en': 'Palau', 'ja': 'パラオ'},
    'PA': {'en': 'Panama', 'ja': 'パナマ'},
    'PG': {'en': 'Papua New Guinea', 'ja': 'パプアニューギニア'},
    'PY': {'en': 'Paraguay', 'ja': 'パラグアイ'},
    'PE': {'en': 'Peru', 'ja': 'ペルー'},
    'PH': {'en': 'Philippines', 'ja': 'フィリピン'},
    'PL': {'en': 'Poland', 'ja': 'ポーランド'},
    'PT': {'en': 'Portugal', 'ja': 'ポルトガル'},
    'PR': {'en': 'Puerto Rico', 'ja': 'プエルトリコ'},
    'RO': {'en': 'Romania', 'ja': 'ルーマニア'},
    'RU': {'en': 'Russia', 'ja': 'ロシア'},
    'SM': {'en': 'San Marino', 'ja': 'サンマリノ'},
    'SA': {'en': 'Saudi Arabia', 'ja': 'サウジアラビア'},
    'RS': {'en': 'Serbia', 'ja': 'セルビア'},
    'SC': {'en': 'Seychelles', 'ja': 'セイシェル'},
    'SG': {'en': 'Singapore', 'ja': 'シンガポール'},
    'SK': {'en': 'Slovakia', 'ja': 'スロバキア'},
    'SI': {'en': 'Slovenia', 'ja': 'スロベニア'},
    'ZA': {'en': 'South Africa', 'ja': '南アフリカ'},
    'KR': {'en': 'South Korea', 'ja': '韓国'},
    'ES': {'en': 'Spain', 'ja': 'スペイン'},
    'SE': {'en': 'Sweden', 'ja': 'スウェーデン'},
    'CH': {'en': 'Switzerland', 'ja': 'スイス'},
    'TW': {'en': 'Taiwan', 'ja': '台湾'},
    'TZ': {'en': 'Tanzania', 'ja': 'タンザニア'},
    'TH': {'en': 'Thailand', 'ja': 'タイ'},
    'TL': {'en': 'Timor Leste', 'ja': 'ティモールリード'},
    'TO': {'en': 'Tonga', 'ja': 'トンガ'},
    'TN': {'en': 'Tunisia', 'ja': 'チュニジア'},
    'TR': {'en': 'Turkey', 'ja': '七面鳥'},
    'UA': {'en': 'Ukraine', 'ja': 'ウクライナ'},
    'AE': {'en': 'United Arab Emirates', 'ja': 'アラブ首長国連邦'},
    'GB': {'en': 'United Kingdom', 'ja': 'イギリス'},
    'UM': {'en': 'United States Minor Outlying Islands', 'ja': 'アメリカ合衆国のマイナーアウト島'},
    'US': {'en': 'United States of America', 'ja': 'アメリカ合衆国'},
    'VI': {'en': 'United States Virgin Islands', 'ja': '米国バージン諸島'},
    'UY': {'en': 'Uruguay', 'ja': 'ウルグアイ'},
    'UZ': {'en': 'Uzbekistan', 'ja': 'ウズベキスタン'},
    'VU': {'en': 'Vanuatu', 'ja': 'バヌアツ'},
    'VA': {'en': 'Vatican City', 'ja': 'バチカン市'},
    'VE': {'en': 'Venezuela', 'ja': 'ベネズエラ'},
    'VN': {'en': 'Vietnam', 'ja': 'ベトナム'},
    'ZM': {'en': 'Zambia', 'ja': 'ザンビア'},
    'ZW': {'en': 'Zimbabwe', 'ja': 'ジンバブエ'}
}


NEXT_ENGINE_PAYMENT_METHOD_LIST = {
    'en': [
        {'slug': '0', 'display': '0: Credit card'},
        {'slug': '1', 'display': '1: Cash on delivery'},
        {'slug': '15', 'display': '15: Prepayment by bank transfer'},
        {'slug': '16', 'display': '16: Bank transfer payment later'},
        {'slug': '20', 'display': '20: Postal transfer'},
        {'slug': '21', 'display': '21: Registered cash'},
        {'slug': '51', 'display': '51: au PAY/au Easy Payment'},
        {'slug': '30', 'display': '30: Consolidated au payments'},
        {'slug': '31', 'display': '31: Mobile Suica'},
        {'slug': '32', 'display': '32: Convenience store payment'},
        {'slug': '33', 'display': '33: Pay-easy payment'},
        {'slug': '34', 'display': '34: NP Postpaid'},
        {'slug': '35', 'display': '35: Lawson prepaid'},
        {'slug': '36', 'display': '36: Seven-Eleven prepaid'},
        {'slug': '40', 'display': '40: Rakuten Bank Payment'},
        {'slug': '41', 'display': '41: S! Consolidated payment'},
        {'slug': '42', 'display': '42: Docomo mobile payment'},
        {'slug': '43', 'display': '43: iD payment'},
        {'slug': '44', 'display': '44: Yahoo! Easy Payment'},
        {'slug': '45', 'display': '45: WebMoney Payment'},
        {'slug': '46', 'display': '46: Rakuten Bank'},
        {'slug': '47', 'display': '47: Edy payment'},
        {'slug': '48', 'display': '48: Amazon Payments'},
        {'slug': '49', 'display': '49: Japan Net Bank'},
        {'slug': '50', 'display': '50: Pay Later.com'},
        {'slug': '52', 'display': '52: Paygent Payment'},
        {'slug': '53', 'display': '53: Credit card (pg)'},
        {'slug': '54', 'display': '54: Convenience store payment (pg)'},
        {'slug': '55', 'display': '55: Bank online payment (pg)'},
        {'slug': '56', 'display': '56: ATM payments (pg)'},
        {'slug': '57', 'display': '57: Mobile carrier payment (pg)'},
        {'slug': '58', 'display': '58: Paperless payments'},
        {'slug': '59', 'display': '59: Qoo10'},
        {'slug': '60', 'display': '60: PayPal'},
        {'slug': '61', 'display': '61: @Payment'},
        {'slug': '63', 'display': '63: SoftBank One-Stop Payment'},
        {'slug': '64', 'display': '64: Atodine'},
        {'slug': '65', 'display': '65: Yahoo! Money/Deposit Payment'},
        {'slug': '66', 'display': '66: Kuroneko deferred payment'},
        {'slug': '67', 'display': '67: Bill payment'},
        {'slug': '68', 'display': '68: Apple Pay'},
        {'slug': '71', 'display': '71: Rakuten Pay Later'},
        {'slug': '72', 'display': '72: PayPay balance, etc.'},
        {'slug': '73', 'display': '73: Alipay'},
        {'slug': '74', 'display': '74: Relaxed deferred payment'},
        {'slug': '75', 'display': '75: Slow payment'},
        {'slug': '76', 'display': '76: PayPay Credit'},
        {'slug': '80', 'display': '80: Advance payment'},
        {'slug': '85', 'display': '85: Full payment of points'},
        {'slug': '90', 'display': '90: Samples and rentals'},
        {'slug': '91', 'display': '91: International Transaction Settlement'},
        {'slug': '95', 'display': '95: Credit'},
        {'slug': '96', 'display': '96: Other'},
        {'slug': '99', 'display': '99: Paid'}
    ],
    'ja':
    [
        {'slug': '0', 'display': '0:クレジットカード'},
        {'slug': '1', 'display': '1:代金引換'},
        {'slug': '15', 'display': '15:銀行振込前払い'},
        {'slug': '16', 'display': '16:銀行振込後払い'},
        {'slug': '20', 'display': '20:郵便振替'},
        {'slug': '21', 'display': '21:現金書留'},
        {'slug': '51', 'display': '51:au PAY/auかんたん決済'},
        {'slug': '30', 'display': '30:まとめてau支払い'},
        {'slug': '31', 'display': '31:モバイルSuica'},
        {'slug': '32', 'display': '32:コンビニ決済'},
        {'slug': '33', 'display': '33:ペイジー決済'},
        {'slug': '34', 'display': '34:NP後払い'},
        {'slug': '35', 'display': '35:ローソン前払'},
        {'slug': '36', 'display': '36:セブンイレブン前払'},
        {'slug': '40', 'display': '40:楽天バンク決済'},
        {'slug': '41', 'display': '41:S!まとめて支払い'},
        {'slug': '42', 'display': '42:ドコモケータイ払い'},
        {'slug': '43', 'display': '43:iD決済'},
        {'slug': '44', 'display': '44:Yahoo!かんたん決済'},
        {'slug': '45', 'display': '45:ウェブマネー決済'},
        {'slug': '46', 'display': '46:楽天銀行'},
        {'slug': '47', 'display': '47:Edy決済'},
        {'slug': '48', 'display': '48:Amazonペイメント'},
        {'slug': '49', 'display': '49:ジャパンネット銀行'},
        {'slug': '50', 'display': '50:後払い.com'},
        {'slug': '52', 'display': '52:ペイジェント決済'},
        {'slug': '53', 'display': '53:クレジットカード(pg)'},
        {'slug': '54', 'display': '54:コンビニ決済(pg)'},
        {'slug': '55', 'display': '55:銀行ネット決済(pg)'},
        {'slug': '56', 'display': '56:ATM決済(pg)'},
        {'slug': '57', 'display': '57:携帯キャリア決済(pg)'},
        {'slug': '58', 'display': '58:ペーパーレス決済'},
        {'slug': '59', 'display': '59:Qoo10'},
        {'slug': '60', 'display': '60:PayPal'},
        {'slug': '61', 'display': '61:＠払い'},
        {'slug': '63', 'display': '63:ソフトバンクまとめて支払い'},
        {'slug': '64', 'display': '64:アトディーネ'},
        {'slug': '65', 'display': '65:Yahoo!マネー／預金払い'},
        {'slug': '66', 'display': '66:クロネコ代金後払い'},
        {'slug': '67', 'display': '67:請求書払い'},
        {'slug': '68', 'display': '68:Apple Pay'},
        {'slug': '71', 'display': '71:楽天後払い'},
        {'slug': '72', 'display': '72:PayPay残高等'},
        {'slug': '73', 'display': '73:Alipay'},
        {'slug': '74', 'display': '74:ゆったり後払い'},
        {'slug': '75', 'display': '75:ゆっくり払い'},
        {'slug': '76', 'display': '76:PayPayクレジット'},
        {'slug': '80', 'display': '80:前払決済'},
        {'slug': '85', 'display': '85:ポイント全額支払い'},
        {'slug': '90', 'display': '90:サンプル・貸し出し'},
        {'slug': '91', 'display': '91:国際取引決済'},
        {'slug': '95', 'display': '95:掛売'},
        {'slug': '96', 'display': '96:その他'},
        {'slug': '99', 'display': '99:支払済'}
    ]
}


NEXT_ENGINE_SHIPPING_METHOD_LIST = {
    'en': [
        {'slug': '10', 'display': '10: Sagawa Express (e-Hiden PRO)'},
        {'slug': '12', 'display': '12: Sagawa Express (e-Hiden)'},
        {'slug': '13', 'display': '13: Sagawa Express (e-Hiden 2)'},
        {'slug': '17', 'display': '17: Sagawa Express (e-Hiden 3)'},
        {'slug': '11', 'display': '11: Sagawa Mail (e-Hiden PRO)'},
        {'slug': '14', 'display': '14: Sagawa Mail (e-Hiden)'},
        {'slug': '15', 'display': '15: Sagawa Mail (e-Hiden 2)'},
        {'slug': '18', 'display': '18: Sagawa Mail (e-Hiden 3)'},
        {'slug': '16',
            'display': '16: Rakuten convenience store pickup (Sagawa Express)'},
        {'slug': '20', 'display': '20: Yamato (cash on delivery) B2v6'},
        {'slug': '21', 'display': '21: Yamato (Collect) B2v6'},
        {'slug': '22', 'display': '22: Yamato (DM mail) B2v6'},
        {'slug': '23', 'display': '23: Yamato (Express Mail) B2v6'},
        {'slug': '24', 'display': '24: Yamato (cash on delivery) B2v5'},
        {'slug': '25', 'display': '25: Yamato (Collect) B2v5'},
        {'slug': '26', 'display': '26: Yamato (DM mail) B2v5'},
        {'slug': '27', 'display': '27: Yamato (Express Mail) B2v5'},
        {'slug': '28', 'display': '28: Yamato (Nekoposu)'},
        {'slug': '30', 'display': '30: Yu-Pack'},
        {'slug': '31', 'display': '31: Letter Pack 500'},
        {'slug': '32', 'display': '32: Letter Pack 350'},
        {'slug': '33', 'display': '33: Yu-Mail'},
        {'slug': '34', 'display': '34: Post Packet'},
        {'slug': '35', 'display': '35: Yu-Packet'},
        {'slug': '36',
            'display': '36: Rakuten convenience store pickup (Yu-Pack)'},
        {'slug': '37', 'display': '37: Rakuten Post Office Pickup'},
        {'slug': '40', 'display': '40: Non-standard mail (cash on delivery)'},
        {'slug': '41', 'display': '41: Non-standard mail'},
        {'slug': '42', 'display': '42: Japan Post (delivery outside home)'},
        {'slug': '50', 'display': '50: Nippon Express e-issue'},
        {'slug': '55', 'display': '55: Fukuyama Transportation Istar2'},
        {'slug': '60', 'display': '60: Seino Transportation Kangaroo m2'},
        {'slug': '61', 'display': '61: Rakuten EXPRESS (Courier service)'},
        {'slug': '62', 'display': '62: Rakuten EXPRESS (mail order)'},
        {'slug': '63', 'display': '63: Rakuten EXPRESS (Trackable Mail)'},
        {'slug': '70', 'display': '70: Air Mail'},
        {'slug': '71', 'display': '71: Eco-friendly'},
        {'slug': '72', 'display': '72: Densuke'},
        {'slug': '80', 'display': '80: EMS'},
        {'slug': '81', 'display': '81: Online Shipping Tools (EMS)'},
        {'slug': '95', 'display': '95: In-store pickup'},
        {'slug': '96', 'display': '96: Direct from manufacturer'},
        {'slug': '98', 'display': '98: Rakuten International Shipping'},
        {'slug': '99', 'display': '99: International shipping'}
    ],
    'ja':
    [
        {'slug': '10', 'display': '10:佐川急便(e飛伝PRO)'},
        {'slug': '12', 'display': '12:佐川急便(e飛伝)'},
        {'slug': '13', 'display': '13:佐川急便(e飛伝2)'},
        {'slug': '17', 'display': '17:佐川急便(e飛伝3)'},
        {'slug': '11', 'display': '11:佐川メール便(e飛伝PRO)'},
        {'slug': '14', 'display': '14:佐川メール便(e飛伝)'},
        {'slug': '15', 'display': '15:佐川メール便(e飛伝2)'},
        {'slug': '18', 'display': '18:佐川メール便(e飛伝3)'},
        {'slug': '16', 'display': '16:楽天コンビニ受取(佐川急便)'},
        {'slug': '20', 'display': '20:ヤマト(発払い)B2v6'},
        {'slug': '21', 'display': '21:ヤマト(コレクト)B2v6'},
        {'slug': '22', 'display': '22:ヤマト(DM便)B2v6'},
        {'slug': '23', 'display': '23:ヤマト(メール速達)B2v6'},
        {'slug': '24', 'display': '24:ヤマト(発払い)B2v5'},
        {'slug': '25', 'display': '25:ヤマト(コレクト)B2v5'},
        {'slug': '26', 'display': '26:ヤマト(DM便)B2v5'},
        {'slug': '27', 'display': '27:ヤマト(メール速達)B2v5'},
        {'slug': '28', 'display': '28:ヤマト(ネコポス)'},
        {'slug': '30', 'display': '30:ゆうパック'},
        {'slug': '31', 'display': '31:レターパック500'},
        {'slug': '32', 'display': '32:レターパック350'},
        {'slug': '33', 'display': '33:ゆうメール'},
        {'slug': '34', 'display': '34:ポスパケット'},
        {'slug': '35', 'display': '35:ゆうパケット'},
        {'slug': '36', 'display': '36:楽天コンビニ受取(ゆうパック)'},
        {'slug': '37', 'display': '37:楽天郵便局受取'},
        {'slug': '40', 'display': '40:定形外郵便(代引)'},
        {'slug': '41', 'display': '41:定形外郵便'},
        {'slug': '42', 'display': '42:日本郵便(自宅外配送)'},
        {'slug': '50', 'display': '50:日本通運e発行'},
        {'slug': '55', 'display': '55:福山通運istar2'},
        {'slug': '60', 'display': '60:西濃運輸カンガルm2'},
        {'slug': '61', 'display': '61:楽天EXPRESS(宅配便)'},
        {'slug': '62', 'display': '62:楽天EXPRESS(メール便)'},
        {'slug': '63', 'display': '63:楽天EXPRESS(追跡可能メール便)'},
        {'slug': '70', 'display': '70:Air Mail'},
        {'slug': '71', 'display': '71:エコ配'},
        {'slug': '72', 'display': '72:伝助'},
        {'slug': '80', 'display': '80:EMS'},
        {'slug': '81', 'display': '81:オンラインシッピングツール（EMS）'},
        {'slug': '95', 'display': '95:店頭渡し'},
        {'slug': '96', 'display': '96:メーカー直送'},
        {'slug': '98', 'display': '98:楽天国際配送'},
        {'slug': '99', 'display': '99:海外発送'}
    ]
}


VIEW_TARGET = [
    ('task', 'Task'),
    ('workflow', 'Workflow'),
    ('commerce_items', 'Items'),
    ('commerce_orders', 'Orders'),
    ('commerce_subscription', 'Subscriptions'),
    ('commerce_inventory', 'Inventory'),
    ('commerce_deals', 'Cases'),
    ('contacts', 'Contacts'),
    ('companies', 'Companies'),
    ('purchaseorder', 'Purchase Orders'),
]
APP_TARGET_DISPLAY = {
    "task": {
        "en": "Task",
        "ja": "タスク"
    },
    "workflows": {
        "en": "Workflow",
        "ja": "ワークフロー"
    },
    "commerce_items": {
        "en": "Item",
        "ja": "商品"
    },
    "commerce_orders": {
        "en": "Order",
        "ja": "受注"
    },
    "commerce_subscription": {
        "en": "Subscriptions",
        "ja": "サブスクリプション"
    },
    "customer_case": {
        "en": "Cases",
        "ja": "案件"
    },
    "commerce_inventory": {
        "en": "Inventory",
        "ja": "在庫"
    },
    "contacts": {
        "en": "Contact",
        "ja": "連絡先"
    },
    # TEMPORARY FIX BY ADDING SINGULAR FOR COMPANY
    "company": {
        "en": "Company",
        "ja": "企業"
    },
    "companies": {
        "en": "Company",
        "ja": "企業"
    },
    "invoices": {
        "en": "Invoice",
        "ja": "売上請求"
    },
    "estimates": {
        "en": "Estimate",
        "ja": "見積"
    },
    "deliveryslip": {
        "en": "Delivery Note",
        "ja": "納品"
    },
    "delivery_slips": {
        "en": "Delivery Note",
        "ja": "納品"
    },
    "slips": {
        "en": "Slip",
        "ja": "伝票"
    },
    "receipts": {
        "en": "Payment",
        "ja": "入金"
    },
    "purchaseorder": {
        "en": "Purchase Order",
        "ja": "発注"
    },
    "purchaseitem": {
        "en": "Purchase Item",
        "ja": "仕入品"
    },
    "billing": {
        "en": "Bill",
        "ja": "支払請求"
    },
    "bill": {
        "en": "Bill",
        "ja": "支払請求"
    },
    "expense": {
        "en": "Expense",
        "ja": "経費"
    },
    "expense": {
        "en": "Expense",
        "ja": "経費"
    },
    "commerce_inventory_warehouse": {
        "en": "Location",
        "ja": "ロケーション"
    },
    "commerce_inventory_transaction": {
        "en": "Inventory Transaction",
        "ja": "入出庫"
    },
    "journal": {
        "en": "Accounting Transaction",
        "ja": "仕訳"
    }

}


NEXT_ENGINE_HEADER_MAPPING_LIST = [
    {
        'ne_value': 'receive_order_purchaser_name',
        'value': 'purchase_name',
        'name': 'Customer Name',
        'name_ja': '購入者名',
        'skip': False,
        'default': 'Company - Name'
    },
    {
        'ne_value': 'receive_order_purchaser_kana',
        'value': 'purchase_name_kana',
        'name': 'Customer Name Kana',
        'name_ja': '購入者 - フリガナ',
        'skip': False,
        'default': 'Company - Name'
    },
    {
        'ne_value': 'receive_order_purchaser_tel',
        'value': 'purchase_phone_number',
        'name': 'Customer Phone Number',
        'name_ja': '購入者 - 電話番号',
        'skip': False,
        'default': 'Company - Phone Number'
    },
    {
        'ne_value': 'receive_order_purchaser_mail_address',
        'value': 'purchase_email_address',
        'name': 'Customer Email Address',
        'name_ja': '購入者 - メール',
        'skip': False,
        'default': 'Company - Email'
    },
    {
        'ne_value': 'receive_order_purchaser_address1',
        'value': 'purchase_address1',
        'name': 'Customer Address',
        'name_ja': '購入者 - 住所1',
        'skip': False,
        'default': 'Company - Name'
    },
    {
        'ne_value': 'receive_order_purchaser_address2',
        'value': 'purchase_address2',
        'name': 'Customer Address 2',
        'name_ja': '購入者 - 住所2',
        'skip': False,
        'default': 'Company - Name'
    },
    {
        'ne_value': 'receive_order_purchaser_zip_code',
        'value': 'purchase_zip_code',
        'name': 'Customer Postal Code',
        'name_ja': '購入者 - 郵便番号',
        'skip': False,
        'default': 'Company - Name'
    },
    {
        'ne_value': 'receive_order_consignee_name',
        'value': 'receiver_name',
        'name': 'Receiver Name',
        'name_ja': '送り先名',
        'skip': False,
        'default': 'Company - Name'
    },
    {
        'ne_value': 'receive_order_consignee_kana',
        'value': 'receiver_name_kana',
        'name': 'Receiver Name Kana',
        'name_ja': '送り先 - フリガナ',
        'skip': False,
        'default': 'Company - Name'
    },
    {
        'ne_value': 'receive_order_consignee_tel',
        'value': 'receiver_phone_number',
        'name': 'Receiver Phone Number',
        'name_ja': '送り先 - 電話番号',
        'skip': False,
        'default': 'phone_number'
    },
    {
        'ne_value': 'receive_order_consignee_address1',
        'value': 'receiver_address1',
        'name': 'Receiver Address',
        'name_ja': '送り先 - 住所1',
        'skip': False,
        'default': 'Company - Name'
    },
    {
        'ne_value': 'receive_order_consignee_address2',
        'value': 'receiver_address2',
        'name': 'Receiver Address 2',
        'name_ja': '送り先 - 住所2',
        'skip': False,
        'default': 'Company - Name'
    },
    {
        'ne_value': 'receive_order_consignee_zip_code',
        'value': 'receiver_zip_code',
        'name': 'Receiver Postal Code',
        'name_ja': '送り先 - 郵便番号',
        'skip': False,
        'default': 'Company - Name'
    },
    {
        'ne_value': 'receive_order_payment_method_id',
        'value': 'payment_method',
        'name': 'Payment Method',
        'name_ja': '支払方法',
        'skip': False,
        'default': 'Company - Name'
    },
    {
        'ne_value': 'receive_order_delivery_id',
        'value': 'shipping_method',
        'name': 'Shipping Method',
        'name_ja': '発送方法',
        'skip': False,
        'countable': True,
        'default': 'Company - Name'
    },
    {
        'ne_value': 'receive_order_delivery_fee_amount',
        'value': 'order_shipping_cost',
        'name': 'Shipping Fee',
        'name_ja': '発送料',
        'skip': False,
        'countable': True,
        'default': 'Company - Name'
    },
    {
        'ne_value': 'receive_order_order_status_id',
        'value': 'order_status',
        'name': 'Status',
        'name_ja': 'ステータス',
        'skip': False,
        'default': 'Company - Name'
    },
    {
        'ne_value': 'receive_order_shop_id',
        'value': 'shop_id',
        'name': 'Shop',
        'name_ja': '店舗',
        'skip': False,
        'default': 'Company - Name'
    },
    {
        'ne_value': 'receive_order_note',
        'value': 'notes',
        'name': 'Notes',
        'name_ja': '備考',
        'skip': False,
        'default': 'Notes'
    },
    {
        'ne_value': 'receive_order_row_goods_id',
        'value': 'line_item_id',
        'name': 'Line Item ID',
        'name_ja': '商品項目ID',
        'skip': False,
        'default': 'Platform ID'
    },
]

NEXT_ENGINE_EXTENDED_HEADER_MAPPING_LIST = NEXT_ENGINE_HEADER_MAPPING_LIST + [
    {
        'ne_value': 'receive_order_id',
        'value': 'order_id',
        'name': 'Order number',
        'name_ja': '受注番号',
        'skip': False,
        'countable': True,
        'default': 'Company - Name'
    },
    {
        'ne_value': 'receive_order_total_amount',
        'value': 'order_total_price',
        'name': 'Total Price [With Tax]',
        'name_ja': '商品計[税込]',
        'skip': False,
        'countable': True,
        'default': 'Company - Name'
    },
    {
        'ne_value': 'receive_order_goods_amount',
        'value': 'order_total_price_without_tax',
        'name': 'Total Price [Without Tax]',
        'name_ja': '合計価格[税なし]',
        'skip': False,
        'countable': True,
        'default': 'Company - Name'
    },
    {
        'ne_value': 'receive_order_tax_amount',
        'value': 'order_tax',
        'name': 'Tax Amount',
        'name_ja': '税金',
        'skip': False,
        'countable': True,
        'default': 'Company - Name'
    },
    {
        'ne_value': 'receive_order_creation_date',
        'value': 'order_created_date',
        'name': 'Order Date',
        'name_ja': '受注日',
        'skip': False,
        'countable': True,
        'default': 'Company - Name'
    }
]

# Still Observe for naming in japanese name
NEXTENGINE_ITEM_FIELD_DICT = {
    "order_receiver_charge": "receive_order_charge_amount",
    "order_other_amount": "receive_order_other_amount",
    "order_item_id": "receive_order_row_goods_id",
    "order_item_name": "receive_order_row_goods_name",
    "order_item_price": "receive_order_row_unit_price",
    "order_subtotal": "receive_order_row_sub_total_price",
    "number_of_item": "receive_order_row_quantity"
}
NEXTENGINE_PUSH_HEADER = ['店舗伝票番号',
                          '受注日',
                          '受注郵便番号',
                          '受注住所１',
                          '受注住所２',
                          '受注名',
                          '受注名カナ',
                          '受注電話番号',
                          '受注メールアドレス',
                          '発送郵便番号',
                          '発送先住所１',
                          '発送先住所２',
                          '発送先名',
                          '発送先カナ',
                          '発送電話番号',
                          '支払方法',
                          '発送方法',
                          '商品計',
                          '税金',
                          '発送料',
                          '手数料',
                          'ポイント',
                          'その他費用',
                          '合計金額',
                          'ギフトフラグ',
                          '時間帯指定',
                          '日付指定',
                          '作業者欄',
                          '備考',
                          '商品名',
                          '商品コード',
                          '商品価格',
                          '受注数量',
                          '商品オプション',
                          '出荷済フラグ',
                          '顧客区分',
                          '顧客コード',
                          '消費税率（%）',
                          'のし',
                          'ラッピング',
                          'メッセージ']

NEXTENGINE_PUSH_FIELD_NAME = ['purchase_zip_code',
                              'purchase_address1',
                              'purchase_address2',
                              'purchase_name',
                              'purchase_name_kana',
                              'purchase_phone_number',
                              'purchase_email_address',
                              'receiver_zip_code',
                              'receiver_address1',
                              'receiver_address2',
                              'receiver_name',
                              'receiver_name_kana',
                              'receiver_phone_number',
                              'payment_method',
                              'shipping_method'
                              ]

NEXT_ENGINE_API_OPERATOR = {
    "equal": {
        "operator": "=",
        "ne_operator": "-eq",
        "label_en": "Equal",
        "label_ja": "等しい"
    },
    "not_equal": {
        "operator": "!=",
        "ne_operator": "-neq",
        "label_en": "Not Equal",
        "label_ja": "等しくない"
    },
    "greater_than": {
        "operator": ">",
        "ne_operator": "-gt",
        "label_en": "Greater Than",
        "label_ja": "より大きい"
    },
    "greater_than_equal": {
        "operator": ">=",
        "ne_operator": "-gte",
        "label_en": "Greater Than Equal",
        "label_ja": "等しいより大きい"
    },
    "less_than": {
        "operator": "<",
        "ne_operator": "-lt",
        "label_en": "Less Than",
        "label_ja": "未満"
    },
    "less_than_equal": {
        "operator": "<=",
        "ne_operator": "-lte",
        "label_en": "Less Than Equal",
        "label_ja": "等しいより小さい"
    },
    "is_not_empty": {
        "operator": "IS NOT NULL",
        "ne_operator": "-neq null",
        "label_en": "Is Not Empty",
        "label_ja": "空でない"
    }
}

freee_mapping_header_list = [
    {
        'value': 'billing_date',
        'name': 'Billing Date',
        'name_ja': '請求日',
        'skip': False,
        'default': 'name'
    },
    {
        'value': 'issue_date',
        'name': 'Issue Date',
        'name_ja': '発行日',
        'skip': False,
        'default': 'name'
    },
    {
        'value': 'payment_date',
        'name': 'Payment Date',
        'name_ja': '支払日',
        'skip': False,
        'default': 'name'
    },
    {
        'value': 'subject',
        'name': 'Subject',
        'name_ja': '件名',
        'skip': False,
        'default': 'name'
    },
    {
        'value': 'invoice_note',
        'name': 'Note',
        'name_ja': '備考欄',
        'skip': False,
        'default': 'name'
    },
    {
        'value': 'partner_address_zipcode',
        'name': 'Customer Address Zipcode',
        'name_ja': '顧客住所郵便番号',
        'skip': False,
        'default': 'name'
    },
    {
        'value': 'partner_address_prefecture_code',
        'name': 'Customer Address Prefecture Code',
        'name_ja': '顧客住所都道府県コード',
        'skip': False,
        'default': 'name'
    },
    {
        'value': 'partner_address_street_name1',
        'name': 'Customer Street Name1',
        'name_ja': '顧客住所1',
        'skip': False,
        'default': 'name'
    },
    {
        'value': 'partner_address_street_name2',
        'name': 'Customer Street Name2',
        'name_ja': '顧客住所2',
        'skip': False,
        'default': 'name'
    },
    {
        'value': 'partner_contact_department',
        'name': 'Customer Contact Department',
        'name_ja': '顧客連絡先部署',
        'skip': False,
        'default': 'name'
    },
    {
        'value': 'partner_contact_name',
        'name': 'Partner Contact Name',
        'name_ja': '顧客連絡先名',
        'skip': False,
        'default': 'name'
    },
    {
        'value': 'partner_display_name',
        'name': 'Partner Display Name',
        'name_ja': '顧客表示名',
        'skip': False,
        'default': 'name'
    },
    {
        'value': 'partner_bank_account',
        'name': 'Customer Bank Account',
        'name_ja': '顧客銀行口座',
        'skip': False,
        'default': 'name'
    },
    {
        'value': 'company_contact_name',
        'name': 'Issuer Contact Name',
        'name_ja': '発行者連絡先名',
        'skip': False,
        'default': 'name'
    },
    {
        'value': 'company_name',
        'name': 'Issuer Company Name',
        'name_ja': '発行者会社名',
        'skip': False,
        'default': 'name'
    },
    {
        'value': 'company_description',
        'name': 'Issuer Company Description',
        'name_ja': '発行者会社説明',
        'skip': False,
        'default': 'name'
    },
    {
        'value': 'bank_account_to_transfer',
        'name': 'Issuer Bank Account',
        'name_ja': '発行者振込先銀行口座',
        'skip': False,
        'default': 'name'
    }
]

PANEL_TYPE_TITLE = {
    'chart': {
        'en': 'Line Chart',
        'ja': 'ラインチャート'
    },
    'bar': {
        'en': 'Bar Chart',
        'ja': '棒グラフ'  # TODO: need to check translation
    },
    'table': {
        'en': 'Table',
        'ja': 'テーブル'
    },
    'pivot_table': {
        'en': 'Pivot Table',
        'ja': 'ピボットテーブル'
    },

    'summary_table': {
        'en': 'Number',
        'ja': 'ナンバー'
    },
    'funnel_chart': {
        'en': 'Funnel Chart',
        'ja': 'ファネルチャート'
    },
    'cohort_chart': {
        'en': 'Cohort Chart',
        'ja': 'コホートチャート'
    },
    'sheet': {
        'en': 'Sheet',
        'ja': 'シート'
    },

}

PANEL_METRIC_TITLE = {
    'number_orders': {
        'en': 'Number Orders',
        'ja': '受注件数',
    },
    'revenue': {
        'en': 'Revenue',
        'ja': '売上',
    },
    'number_subscriptions': {
        'en': 'Number of Subscriptions',
        'ja': 'サブスクリプション件数',
    },
    'number_invoices': {
        'en': 'Number of Invoices',
        'ja': '売上請求件数',
    },
    'recurring_revenue': {  # for Subscription Object
        'en': 'Recurring Revenue',
        'ja': 'MRR 月間経常収益',
    },
    'recurring_revenue_invoice': {  # for Invoice Object
        'en': 'Billing Amount',
        'ja': '売上請求金額',
    },
    'recurring_revenue_annually': {
        'en': 'Annually Recurring Revenue',
        'ja': 'ARR 年間経常収益',
    },
    'recurring_revenue_annually_invoice': {  # for Invoice Object
        'en': 'Annual Billing Amount ',
        'ja': '年間経常収益',
    },
    'average_recurring_revenue': {
        'en': 'Average Recurring Revenue',
        'ja': '平均経常収益',
    },
    'average_recurring_revenue_annually': {
        'en': 'Average Recurring Revenue Annually',
        'ja': '年間平均経常収益',
    },
    'expansion_recurring_revenue': {
        'en': 'Expansion MRR',
        'ja': 'エクスパンション MRR',
    },
    'downgrade_recurring_revenue': {
        'en': 'Downgrade MRR',
        'ja': 'ダウングレード MRR'
    },
    'number_estimates': {
        'en': 'Number of Estimates',
        'ja': '見積数',
    },
    'revenue_estimates': {
        'en': 'Estimate Amount',
        'ja': '見積金額',
    },
    'number_contacts': {
        'en': 'Contacts',
        'ja': '連絡先',
    },
    'number_companies': {
        'en': 'Companies',
        'ja': '企業',
    },
    'number_cases': {
        'en': 'Cases',
        'ja': '案件',
    },
    'integration': {
        'en': 'Integration',
        'ja': '連携サービス',
    },
    'integration': {
        'en': 'Events',
        'ja': 'イベント',
    },
    'orders.delivery_status': {
        'en': 'Order Delivery Status',
        'ja': 'ステータス',
    },
    'delivery_status': {
        'en': 'Order Delivery Status',
        'ja': 'ステータス',
    },
    'sessions': {
        'en': 'Sessions',
        'ja': 'セッション',
    },
    'unique_sessions': {
        'en': 'Unique Sessions',
        'ja': 'ユニークなセッション',
    },
    'item_id': {
        'en': 'Item ID',
        'ja': '商品ID',
    },
    'item_id': {
        'en': 'Item ID',
        'ja': '商品ID',
    },
    'name': {
        'en': 'Name',
        'ja': '名前',
    },
    'orders': {
        'en': 'Orders',
        'ja': '受注',
    },
    'quantity': {
        'en': 'Quantity',
        'ja': '量',
    },
    'contact_id': {
        'en': 'Contact ID',
        'ja': '連絡先ID',
    },
    'email': {
        'en': 'Email',
        'ja': 'Eメール',
    },
    'item_quantity': {
        'en': 'Item Quantity',
        'ja': '商品の数量',
    },
    'order_quantity': {
        'en': 'Order Quantity',
        'ja': '受注件数',
    },
    'order_quantity': {
        'en': 'Order Quantity',
        'ja': '受注件数',
    },
    'contact__name': {
        'en': 'Customer',
        'ja': '顧客',
    },
    'order_id': {
        'en': 'Order ID',
        'ja': '受注ID',
    },
    'company__name': {
        'en': 'Company',
        'ja': '会社',
    },
    'platform': {
        'en': 'Platform',
        'ja': 'プラットホーム',
    },
    'number_item': {
        'en': 'Number Items',
        'ja': '商品数',
    },
    'currency': {
        'en': 'Currency',
        'ja': '通貨',
    },
    'item_price_order': {
        'en': 'Item Price Order',
        'ja': '商品価格',
    },
    'total_price': {
        'en': 'Total Price',
        'ja': '合計金額',
    },
    'tax': {
        'en': 'Tax',
        'ja': '税',
    },
    'order_type': {
        'en': 'Items',
        'ja': '商品',
    },
    'status': {
        'en': 'Status',
        'ja': 'ステータス',
    },
    'notes': {
        'en': 'Notes',
        'ja': '備考欄',
    },
    'created_at': {
        'en': 'Created at',
        'ja': '作成日',
    },
    'ltv': {
        'en': 'LTV',
        'ja': 'LTV',
    },
    'id': {
        'en': 'ID',
        'ja': 'ID',
    },
    'budget': {
        'en': 'Budget',
        'ja': '予算',
    },
    'amount_spent': {
        'en': 'Amount',
        'ja': '使用金額',
    },
    'signup': {
        'en': 'Signups',
        'ja': 'サインアップ',
    },
    'roas': {
        'en': 'ROAS',
        'ja': 'ROAS',
    },
    'date': {
        'en': 'Date',
        'ja': '日付',
    },
    'user': {
        'en': 'User',
        'ja': 'ユーザー',
    },
    'custom': {
        'en': 'Custom Chart',
        'ja': 'カスタム'
    },
    'items': {
        'en': 'Items',
        'ja': '商品'
    },
    'inventory': {
        'en': 'Inventory',
        'ja': '在庫'
    },
    'customers': {
        'en': 'Customer',
        'ja': '顧客',
    },
    'applicant': {
        'en': 'Applicant',
        'ja': '応募者'
    },
    'sql': {
        'en': 'SQL',
        'ja': 'SQL'
    },
    'retention_rate': {
        'en': 'Retention Rate',
        'ja': 'リテンション率',
    }
}

TABLE_PANEL_COLUMNS = {
    'items': [
        {"value": "item_id", "display": "Item ID",   "display_ja": "商品ID"},
        {"value": "name",    "display": "Item Name", "display_ja": "商品名"},
        {"value": "orders",  "display": "Orders",    "display_ja": "受注数"},
        {"value": "quantity", "display": "Quantity",  "display_ja": "受注件数"},
    ],
    'contacts': [
        {"value": "contact_id",     "display": "Contact ID",     "display_ja": "連絡先ID"},
        {"value": "name",           "display": "Contact Name",   "display_ja": "連絡先"},
        {"value": "email",          "display": "Email",          "display_ja": "Eメール"},
        {"value": "item_quantity",  "display": "Item Quantity",  "display_ja": "商品の数量"},
        {"value": "order_quantity", "display": "Order Quantity", "display_ja": "受注件数"},
    ],
    'orders': [
        {"value": "contact__name",      "display": "Customer",
            "display_ja": "顧客"},
        {"value": "order_id",           "display": "Order ID",
            "display_ja": "受注ID"},
        {"value": "company__name",      "display": "Company",
            "display_ja": "会社"},
        {"value": "platform",           "display": "Platform",
            "display_ja": "プラットホーム"},
        {"value": "number_item",        "display": "Number Items",
            "display_ja": "商品数"},
        {"value": "currency",           "display": "Currency",
            "display_ja": "通貨"},
        {"value": "item_price_order",
            "display": "Total Price Without Tax",    "display_ja": "税抜合計金額"},
        {"value": "total_price",        "display": "Total Price",
            "display_ja": "合計金額"},
        {"value": "tax",                "display": "Tax",
            "display_ja": "税"},
        {"value": "order_type",         "display": "Items",
            "display_ja": "商品"},
        {"value": "delivery_status",    "display": "Status",
            "display_ja": "ステータス"},
        {"value": "status",             "display": "Usage Status",
            "display_ja": "利用ステータス"},
        {"value": "notes",              "display": "Notes",
            "display_ja": "ノート"},
        {"value": "order_at",           "display": "Order At",
            "display_ja": "受注日時"},
        {"value": "created_at",         "display": "Created at",
            "display_ja": "作成日時"},
    ],
    'customers': [
        {"value": "name",            "display": "Name",
            "display_ja": "名前"},
        {"value": "email",           "display": "Email",
            "display_ja": "メールアドレス"},
        {"value": "ltv",             "display": "LTV",
            "display_ja": "LTV"},
        {"value": "last_order_date",
            "display": "Last order date",   "display_ja": "最終受注日"},
    ],
    'purchase_orders': [
        {"value": "supplier_id",                   "display": "Supplier ID",
            "display_ja": "仕入先ID"},
        {"value": "supplier_name",
            "display": "Supplier Name",               "display_ja": "仕入先名"},
        {"value": "supplier_type",                 "display": "Supplier Type",
            "display_ja": "仕入先タイプ"},
        {"value": "purchase_orders",
            "display": "Total Purchase Orders",       "display_ja": "合計受注数"},
        {"value": "purchase_orders_total_amount",
            "display": "Total Purchase Orders Amount", "display_ja": "合計注文金額"},
    ],
    'invoices': [
        {"value": "id_inv",                         "display": "Invoice ID",
            "display_ja": "請求書ID"},  # TODO: do not sure about the translation
        {"value": "customer__name",                 "display": "Customer Name",
            "display_ja": "顧客名"},  # TODO: do not sure about the translation
        {"value": "total_price_without_tax",
            "display": "Total Price Without Tax",    "display_ja": "税抜合計金額"},
        {"value": "total_price",
            "display": "Total Price",                "display_ja": "合計金額"},
    ],
    'estimates': [
        {"value": "id_est",                         "display": "Estimate ID",
            "display_ja": "見積書ID"},  # TODO: do not sure about the translation
        {"value": "customer__name",                 "display": "Customer Name",
            "display_ja": "顧客名"},  # TODO: do not sure about the translation
        {"value": "total_price_without_tax",
            "display": "Total Price Without Tax",    "display_ja": "税抜合計金額"},
        {"value": "total_price",
            "display": "Total Price",                "display_ja": "合計金額"},
    ],
    'inventory': [
        {"value": "warehouse__warehouse",
            "display": "Location",      "display_ja": "ロケーション"},
        {"value": "item__name",
            "display": "Items",         "display_ja": "商品"},
        {"value": "available",            "display": "Inventory - Available",
            "display_ja": "在庫量 -販売可能"},
    ],
    'application': [
        {"value": "status",               'display': 'Applicant Status',
            'display_ja': '応募者のステータス'},
        {"value": "current_company",
            'display': 'Current Company',          'display_ja': '応募者のステータス'}
    ]
}


DISPLAY_COLUMN_NEW_CREATED_LOG = {
    'item': {
        'en': 'New Item Record Created',
        'ja': '新しい商品レコードが作成されました',
    },
    'inventory': {
        'en': 'New Inventory Record Created',
        'ja': '新しい在庫レコードが作成されました',
    },
    'order': {
        'en': 'New Order Record Created',
        'ja': '新しい受注レコードが作成されました',
    },
    'subscription': {
        'en': 'New Subscription Record Created',
        'ja': '新しいサブスクリプションレコードが作成されました',
    },

    'contact': {
        'en': 'New Contact Record Created',
        'ja': '新しい連絡先レコードが作成されました',
    },
    'company': {
        'en': 'New Company Record Created',
        'ja': '新しい企業レコードが作成されました',
    },

    'invoice': {
        'en': 'New Invoice Record Created',
        'ja': '新しい売上請求レコードが作成されました',
    },

    'estimate': {
        'en': 'New Estimate Record Created',
        'ja': '新しい見積レコードが作成されました',
    },

    'deliveryslip': {
        'en': 'New Delivery Slip Created',
        'ja': '新しい納品レコードが作成されました',
    },

    'receipt': {
        'en': 'New Payment Record Created',
        'ja': '新しい入金レコードが作成されました',
    },

    'task': {
        'en': 'New Task Record Created',
        'ja': '新しいタスクレコードが作成されました',
    },

    'purchaseorders': {
        'en': 'New Purchase Order Record Created',
        'ja': '新しい発注レコードが作成されました',
    },

    'purchaseitems': {
        'en': 'New Purchase Item Record Created',
        'ja': '新しい仕入品レコードが作成されました',
    },

    'payment': {
        'en': 'New Expense Record Created',
        'ja': '新しい経費レコードが作成されました',
    },

    'expense': {
        'en': 'New Expense Record Created',
        'ja': '新しい経費レコードが作成されました',
    },

    'bill': {
        'en': 'New Bill Record Created',
        'ja': '新しい支払請求レコードが作成されました',
    },

    'deal': {
        'en': 'New Case Record Created',
        'ja': '新しい案件レコードが作成されました',
    },
}


CSV_DELIMITER_LIST_FIELD = ';'

SEARCHABLE_PROPERTY_TYPES = ['string', 'text', 'text-area', 'choice', 'phone']
UPDATABLE_PROPERTY_TYPES = ['text', 'date_time', 'text-area', 'choice']
EXCLUDED_PROPERTY_ID = ['currency', 'updated_at', 'created_at']
INCLUDED_PROPERTY_ID = ['memo', 'description', 'name', 'email','first_name', 'last_name']

TEMPLATE_FILE = {
    'spendpocket': {
        'en': 'https://docs.google.com/spreadsheets/d/1cr2AEWl2kjgiO6iegglRMDKYOjQQjNRJ_nAgrxpN-U0/edit',
        'ja': 'https://docs.google.com/spreadsheets/d/11lK3tOMDiw9lr-6zo4swbfM9fTIMc58LPYFuh_xir6M/edit',
    },
    'expense': {
        'en': 'https://docs.google.com/spreadsheets/d/1cr2AEWl2kjgiO6iegglRMDKYOjQQjNRJ_nAgrxpN-U0/edit',
        'ja': 'https://docs.google.com/spreadsheets/d/11lK3tOMDiw9lr-6zo4swbfM9fTIMc58LPYFuh_xir6M/edit',
    },
    'commerce_items': {
        'en': 'https://docs.google.com/spreadsheets/d/1_QPZcOWWvk3T8IevAi92Yt2wRMhr-bLCv3dtpHjbhuM/edit',
        'ja': 'https://docs.google.com/spreadsheets/d/1jIOjGfZ1gGPnFKeciV_Nq9q6KbfRz9rNcAJHVLEIogk/edit',
    },
    'commerce_orders': {
        'en': 'https://docs.google.com/spreadsheets/d/1Eo-E4QHjGouWdTWCjrL6lkp2y_vIynCsGa4rK4TIZ30/edit',
        'ja': 'https://docs.google.com/spreadsheets/d/1H-45s0r6RfChDVQ7-QAhcdHJqYo_BHcKD_jMFy_yD8A/edit',
    },
    'commerce_subscription': {
        'en': 'https://docs.google.com/spreadsheets/d/1G2fnkTqrCkfHSJIKp6hIPH8IUGGJPDQWO1nZQvowTw0/edit',
        'ja': 'https://docs.google.com/spreadsheets/d/1AESxskgITNxaS24f2SQwQXKDzi0XKDVn_8goNTLFxpw/edit',
    },

    'contacts': {
        'en': 'https://docs.google.com/spreadsheets/d/1evjDOtxdeNbRYaihgT8-me-8pwh14TZ8reGHTWnDqRI/edit#gid=0',
        'ja': 'https://docs.google.com/spreadsheets/d/1E9toQt48JSsBXxysBmxJmPz61TiJtvuRPyPfGMphCLQ/edit#gid=0',
    },
    'company': {
        'en': 'https://docs.google.com/spreadsheets/d/1Va8jCXmNWr9kL9fPxJnFNR7Mz-yYd6ujcuLJ3JjYYEo/edit',
        'ja': 'https://docs.google.com/spreadsheets/d/1MvKfTel6_M2Oz3fhhqLYp2y70bUiO3rxLpTO92T56SY/edit',
    },

    'invoices': {
        'en': 'https://docs.google.com/spreadsheets/d/15JP8et9OAY_qapuYI_Yr2DYTjG2ZtyKDzCiKisAMjww/edit?usp=sharing',
        'ja': 'https://docs.google.com/spreadsheets/d/1x1PRk3vt1tB7eub1fjWpz13SQLcEqcR6PnSF94JE4uE/edit?usp=sharing',
    },

    'estimates': {
        'en': 'https://docs.google.com/spreadsheets/d/1FL0tJ61b-fJAYH0ptWNL0hX3HqQ7nU0DpTigUo7irpU/',
        'ja': 'https://docs.google.com/spreadsheets/d/1w8QoghMsXW3BprzWgWvHNQetnBnzSvDPLYicgclkABM/edit',
    },

    'receipts': {
        'en': 'https://docs.google.com/spreadsheets/d/1qWJm8qHE-_lT2_0YeM0YjQ_uypdJL5WH9cJBspmesZA/',
        'ja': 'https://docs.google.com/spreadsheets/d/1LqrNW20N7mu5u_94v3bfqO12q0t1vR4j-SjrTP2zRXI/',
    },
    'customer_case': {
        'en': 'https://docs.google.com/spreadsheets/d/13C0f2ObeY1rpXEp9igStPqcRqNwgZ8k86ZcBycMnmKo/edit',
        'ja': 'https://docs.google.com/spreadsheets/d/119fSzTPJsEPEXJO9qRrwC8cp84dXsatQFyvLXTLRREA/edit',
    },
    'commerce_inventory': {
        'en': 'https://docs.google.com/spreadsheets/d/1I83WpBKyeffWTYaTxBQhoO6GqpOFlmPSN07cY41lGRM/',
        'ja': 'https://docs.google.com/spreadsheets/d/17Yc4ORgRjyNLXzc8pEChPIeRGuYvuvLY2kR1XzC0bjQ/',
    },
    'worker': {
        'en': 'https://docs.google.com/spreadsheets/d/1-Ltkb7hmBPt-IxBB6MgE1uHVD6IC6_uSmCgI2Ve9dQI/',
        'ja': 'https://docs.google.com/spreadsheets/d/1yOJNr0Hjdx9L1qzcqIfTYFz2evGAV4Dt1gN9OHHp6-o/',
    },
    'journal': {
        'en': 'https://docs.google.com/spreadsheets/d/1Kj4lsxqJYUguJSOFRDz-sMmQc_LofSVXmI-xsUHX2vM/',
        'ja': 'https://docs.google.com/spreadsheets/d/1UeUsWaBCqPf7x-NukolsIQAHUCpB4Z97QoyLwPOh-1Y/',
    },
    'customer_case': {
        'en': 'https://docs.google.com/spreadsheets/d/13C0f2ObeY1rpXEp9igStPqcRqNwgZ8k86ZcBycMnmKo/edit',
        'ja': 'https://docs.google.com/spreadsheets/d/119fSzTPJsEPEXJO9qRrwC8cp84dXsatQFyvLXTLRREA/edit',
    },
    'billing': {
        'en': 'https://docs.google.com/spreadsheets/d/1zkcg6xV27g2wEEjV81nzO4uOFRyATbEMXFGJDMB28nw/',
        'ja': 'https://docs.google.com/spreadsheets/d/1n-auuZtFQm4t_EK-q9v44IcKUDi1D3ig0FzmKv6ZPQ8/',
    },
    'bill': {
        'en': 'https://docs.google.com/spreadsheets/d/1zkcg6xV27g2wEEjV81nzO4uOFRyATbEMXFGJDMB28nw/',
        'ja': 'https://docs.google.com/spreadsheets/d/1n-auuZtFQm4t_EK-q9v44IcKUDi1D3ig0FzmKv6ZPQ8/',
    },
    'commerce_inventory_transaction': {
        'en': 'https://docs.google.com/spreadsheets/d/16XBW9F5I9UzHulSjw_V3Y_5ONaAhnxhmTa-L1yQxrx0/edit',
        'ja': 'https://docs.google.com/spreadsheets/d/1_kylkjyNlMfeKKcXnpkr0o0zRcp-3v6dYKD2PkhJBAU/edit',
    },
    'commerce_inventory_warehouse': {
        'en': 'https://docs.google.com/spreadsheets/d/1TJpm299RgSDqiSZ2smjzU8Ww5ZhzjhOQh5EMzuNJ4XY/edit',
        'ja': 'https://docs.google.com/spreadsheets/d/1w-KZnlX3qYzeBmn3qx7up0mRE7IlLh1CJcyQpbQZtlw/edit',
    },
    'delivery_slips': {
        'en': 'https://docs.google.com/spreadsheets/d/1FL0tJ61b-fJAYH0ptWNL0hX3HqQ7nU0DpTigUo7irpU/',
        'ja': 'https://docs.google.com/spreadsheets/d/1w8QoghMsXW3BprzWgWvHNQetnBnzSvDPLYicgclkABM/edit',
    },
    'slips': {
        'en': 'https://docs.google.com/spreadsheets/d/1FL0tJ61b-fJAYH0ptWNL0hX3HqQ7nU0DpTigUo7irpU/',
        'ja': 'https://docs.google.com/spreadsheets/d/1w8QoghMsXW3BprzWgWvHNQetnBnzSvDPLYicgclkABM/edit',
    },

    'purchaseorder': {
        'en': 'https://docs.google.com/spreadsheets/d/1UpH_0MY4N-D9yfJfolJ6-iND9nTaR4kbKiGoYgnDhPo/edit',
        'ja': 'https://docs.google.com/spreadsheets/d/1WESmnQzijDwvwtL_MKDk9LfifAwmlOjzN51IpQcGYiM/edit',
    }

}


TEMPLATE_EXCEL_FILE = {
    'spendpocket': {
        'en': '',
        'ja': '',
    },
    'expense': {
        'en': '',
        'ja': '',
    },
    'commerce_items': {
        'en': 'https://sankafile.nyc3.cdn.digitaloceanspaces.com/marketing/products/Items%20Import%20Template.xlsx',
        'ja': "https://sankafile.nyc3.cdn.digitaloceanspaces.com/marketing/products/%E5%95%86%E5%93%81%E3%82%A4%E3%83%B3%E3%83%9B%E3%82%9A%E3%83%BC%E3%83%88%E3%83%86%E3%83%B3%E3%83%95%E3%82%9A%E3%83%AC%E3%83%BC%E3%83%88.xlsx",
    },
    'commerce_orders': {
        'en': 'https://sankafile.nyc3.cdn.digitaloceanspaces.com/marketing/products/Orders%20Import%20Template.xlsx',
        'ja': 'https://sankafile.nyc3.cdn.digitaloceanspaces.com/marketing/products/%E6%B3%A8%E6%96%87%E3%82%A4%E3%83%B3%E3%83%9B%E3%82%9A%E3%83%BC%E3%83%88%E3%83%86%E3%83%B3%E3%83%95%E3%82%9A%E3%83%AC%E3%83%BC%E3%83%88.xlsx',
    },
    'commerce_subscription': {
        'en': 'https://sankafile.nyc3.cdn.digitaloceanspaces.com/marketing/products/Subscriptions%20CSV%20Import%20Template.xlsx',
        'ja': 'https://sankafile.nyc3.cdn.digitaloceanspaces.com/marketing/products/%E5%AE%9A%E6%9C%9F%E8%B3%BC%E5%85%A5%E3%82%A4%E3%83%B3%E3%83%9B%E3%82%9A%E3%83%BC%E3%83%88%E3%83%86%E3%83%B3%E3%83%95%E3%82%9A%E3%83%AC%E3%83%BC%E3%83%88.xlsx',
    },

    'contacts': {
        'en': 'https://sankafile.nyc3.cdn.digitaloceanspaces.com/marketing/products/Contacts%20Import%20Template.xlsx',
        'ja': 'https://sankafile.nyc3.cdn.digitaloceanspaces.com/marketing/products/%E9%80%A3%E7%B5%A1%E5%85%88%E3%82%A4%E3%83%B3%E3%83%9B%E3%82%9A%E3%83%BC%E3%83%88%E3%83%86%E3%83%B3%E3%83%95%E3%82%9A%E3%83%AC%E3%83%BC%E3%83%88.xlsx',
    },
    'company': {
        'en': 'https://sankafile.nyc3.cdn.digitaloceanspaces.com/marketing/products/Company%20Import%20Template.xlsx',
        'ja': 'https://sankafile.nyc3.cdn.digitaloceanspaces.com/marketing/products/%E4%BC%81%E6%A5%AD%E3%82%A4%E3%83%B3%E3%83%9B%E3%82%9A%E3%83%BC%E3%83%88%E3%83%86%E3%83%B3%E3%83%95%E3%82%9A%E3%83%AC%E3%83%BC%E3%83%88.xlsx',
    },

    'invoices': {
        'en': 'https://sankafile.nyc3.cdn.digitaloceanspaces.com/marketing/products/Invoice%20Import%20Template.xlsx',
        'ja': 'https://sankafile.nyc3.cdn.digitaloceanspaces.com/marketing/products/%E5%A3%B2%E4%B8%8A%E8%AB%8B%E6%B1%82%E6%9B%B8%E3%82%A4%E3%83%B3%E3%83%9B%E3%82%9A%E3%83%BC%E3%83%88%E3%83%86%E3%83%B3%E3%83%95%E3%82%9A%E3%83%AC%E3%83%BC%E3%83%88.xlsx',
    },

    'estimates': {
        'en': '',
        'ja': '',
    },

    'receipts': {
        'en': '',
        'ja': '',
    },
    'customer_case': {
        'en': '',
        'ja': '',
    },
    'commerce_inventory': {
        'en': 'https://sankafile.nyc3.cdn.digitaloceanspaces.com/marketing/products/Inventory%20Import%20Template.xlsx',
        'ja': 'https://sankafile.nyc3.cdn.digitaloceanspaces.com/marketing/products/%E5%9C%A8%E5%BA%AB%E3%82%A4%E3%83%B3%E3%83%9B%E3%82%9A%E3%83%BC%E3%83%88%E3%83%86%E3%83%B3%E3%83%95%E3%82%9A%E3%83%AC%E3%83%BC%E3%83%88.xlsx',
    },
    'worker': {
        'en': 'https://sankafile.nyc3.cdn.digitaloceanspaces.com/marketing/products/Employee%20CSV%20Import%20Template.xlsx',
        'ja': 'https://sankafile.nyc3.cdn.digitaloceanspaces.com/marketing/products/%E5%BE%93%E6%A5%AD%E5%93%A1CSV%E3%82%A4%E3%83%B3%E3%83%9B%E3%82%9A%E3%83%BC%E3%83%88%E3%83%86%E3%83%B3%E3%83%95%E3%82%9A%E3%83%AC%E3%83%BC%E3%83%88.xlsx',
    },
    'journal': {
        'en': '',
        'ja': '',
    },
    'customer_case': {
        'en': '',
        'ja': '',
    },
    'billing': {
        'en': '',
        'ja': '',
    },
    'bill': {
        'en': '',
        'ja': '',
    },
    'commerce_inventory_transaction': {
        'en': '',
        'ja': '',
    },

}

OBJECT_GROUP_TYPE = {
    'company': {
        'en': 'Companies',
        'ja': '企業',
    },
    'contacts': {
        'en': 'Contacts',
        'ja': '連絡先',
    },
    'customer_case': {
        'en': 'Cases',
        'ja': '案件',
    },
    'conversation': {
        'en': 'Messages',
        'ja': 'メッセージ',
    },
    'campaigns': {
        'en': 'Campaigns',
        'ja': 'キャンペーン',
    },

    'commerce_items': {
        'en': 'Items',
        'ja': '商品',
    },
    'commerce_inventory_warehouse': {
        'en': 'Locations',
        'ja': 'ロケーション',
    },
    'commerce_inventory': {
        'en': 'Inventory',
        'ja': '在庫',
    },
    'inventory': {  # CSV_UPLOAD STUFF
        'en': 'Inventory',
        'ja': '在庫',
    },
    'commerce_inventory_transaction': {
        'en': 'Inventory Transactions',
        'ja': '入出庫',
    },
    'estimates': {
        'en': 'Estimates',
        'ja': '見積',
    },
    'commerce_orders': {
        'en': 'Orders',
        'ja': '受注',
    },
    'commerce_subscription': {
        'en': 'Subscriptions',
        'ja': 'サブスクリプション',
    },
    'delivery_slips': {
        'en': 'Delivery Notes',
        'ja': '納品',
    },
    'invoices': {
        'en': 'Invoices',
        'ja': '売上請求',
    },
    'receipts': {
        'en': 'Payments',
        'ja': '入金',
    },
    'purchaseorder': {
        'en': 'Purchase Orders',
        'ja': '発注',
    },
    'billing': {
        'en': 'Bills',
        'ja': '支払請求',
    },
    'bill': {
        'en': 'Bills',
        'ja': '支払請求',
    },
    'spendpocket': {
        'en': 'Expenses',
        'ja': '経費',
    },
    'expense': {
        'en': 'Expenses',
        'ja': '経費',
    },
    'task': {
        'en': 'Tasks',
        'ja': 'タスク',
    },
    'workflow': {
        'en': 'Workflows',
        'ja': 'ワークフロー',
    },
    'panels': {
        'en': 'Reports',
        'ja': 'レポート',
    },
    'dashboards': {
        'en': 'Dashboards',
        'ja': 'ダッシュボード',
    },
    'forms': {
        'en': 'Forms',
        'ja': 'フォーム',
    },
    'worker': {
        'en': 'Employees',
        'ja': '従業員',
    },
    'user': {
        'en': 'User',
        'ja': 'ユーザー',
    },
    'slips': {
        'en': 'Slips',
        'ja': '伝票',
    },

    'journal': {
        'en': 'Journal Entries',
        'ja': '仕訳',
    },
    'session_event': {
        'en': 'Session Events',
        'ja': 'セッションイベント',
    },
    'contract': {
        'en': 'Contracts',
        'ja': '契約'
    },
    'calendar': {
        'en': 'Calendars',
        'ja': 'カレンダー'
    },
    'contact_lists': {
        'en': 'Lists',
        'ja': 'リスト',
    },

    'user_management': {
        'en': 'User Managements',
        'ja': 'ユーザー管理'
    },
    'timegenie': {
        'en': 'Attendances',
        'ja': '勤怠',
    },
    'worker_review': {
        'en': 'Workers Review',
        'ja': 'レビュー',
    },
    'absence': {
        'en': 'Workers Absence',
        'ja': '休暇',
    },
    'jobs': {
        'en': 'Jobs',
        'ja': '求人',
    },
    'jobs_applicant': {
        'en': 'Job Applicants',
        'ja': '応募者',
    },
}

REPORT_OBJECT_GROUP_TYPE = {
    **OBJECT_GROUP_TYPE,
    'case_line_item': {
        'en': 'Case Line Item',
        'ja': '商品項目'
    },
    'invoice': {
        'en': 'Invoice',
        'ja': '売上請求',
    },
    'subscription': {
        'en': 'Subscription',
        'ja': 'サブスクリプション',
    },
}

OBJECT_GROUP_TYPE_SINGULAR = {
    'commerce_items': {
        'en': 'Item',
        'ja': '商品',
    },
    'commerce_inventory': {
        'en': 'Inventory',
        'ja': '在庫',
    },
    'commerce_inventory_transaction': {
        'en': 'Inventory Transaction',
        'ja': '入出庫',
    },
    'commerce_inventory_warehouse': {
        'en': 'Location',
        'ja': 'ロケーション',
    },
    'commerce_orders': {
        'en': 'Order',
        'ja': '受注',
    },
    'commerce_subscription': {
        'en': 'Subscription',
        'ja': 'サブスクリプション',
    },
    'estimates': {
        'en': 'Estimate',
        'ja': '見積',
    },
    'delivery_slips': {
        'en': 'Delivery Note',
        'ja': '納品',
    },
    'invoices': {
        'en': 'Invoice',
        'ja': '売上請求',
    },
    'receipts': {
        'en': 'Payment',
        'ja': '入金',
    },
    'slips': {
        'en': 'Slip',
        'ja': '伝票',
    },
    'purchaseitem': {
        'en': 'Purchase Item',
        'ja': '仕入品',
    },
    'purchaseorder': {
        'en': 'Purchase Order',
        'ja': '発注',
    },
    'billing': {
        'en': 'Bill',
        'ja': '支払請求',
    },
    'bill': {
        'en': 'Bill',
        'ja': '支払請求',
    },
    'spendpocket': {
        'en': 'Expense',
        'ja': '経費',
    },
    'expense': {
        'en': 'Expense',
        'ja': '経費',
    },
    'customer_case': {
        'en': 'Case',
        'ja': '案件',
    },
    'contacts': {
        'en': 'Contact',
        'ja': '連絡先',
    },
    'company': {
        'en': 'Company',
        'ja': '企業',
    },
    'task': {
        'en': 'Project',
        'ja': 'プロジェクト',
    },
    'workflow': {
        'en': 'Workflow',
        'ja': 'ワークフロー',
    }

}

PANEL_METRIC_TYPE = {
    'new': {
        'en': 'New',
        'ja': '新規',
    },
    'cumulative': {
        'en': 'Cumulative',
        'ja': '累積',
    }
}

CONST_PANEL_METRIC_ROLE = {
    'column': {
        'en': 'Column',
        'ja': '列',  # TODO: not sure if the translation is true
    },
    'row': {
        'en': 'Row',
        'ja': '行',  # TODO: not sure if the translation is true
    },
    'values': {
        'en': 'Value',
        'ja': '値',  # TODO: not sure if the translation is true
    },
}

PANEL_FILTER = {
    'orders': ORDERS_COLUMNS_DISPLAY,
    'subscriptions': {
        'subscriptions_id': {
            'en': 'Subscription Id',
            'ja': 'サブスクリプションID',
        },
        'platform_display_name': {
            'en': 'Platform Name',
            'ja': 'プラットフォーム名',
        },
        'status': {
            'en': 'Status',
            'ja': 'ステータス',
        },
        'subscription_status': {
            'en': 'Status',
            'ja': 'ステータス',
        },
        'number_item': {
            'en': 'Number Item',
            'ja': '項目番号',
        },
        'start_date': {
            'en': 'Start Date',
            'ja': '開始日',
        },
        'end_date': {
            'en': 'End Date',
            'ja': '終了日',
        },
        'currency': {
            'en': 'Currency',
            'ja': '通貨',
        },
        'frequency': {
            'en': 'Frequency - Unit',
            'ja': '頻度 - 単位',
        },
        'frequency_time': {
            'en': 'Frequency - Time',
            'ja': '頻度 - 時間',
        },
        'prior_to_next': {
            'en': 'Prior To Next',
            'ja': '次回までの期間',
        },
        'prior_to_time': {
            'en': 'Prior To Time',
            'ja': '次回までの時間',
        },
        'total_price': {
            'en': 'Total Price',
            'ja': '合計金額',
        },
        'shipping_cost_tax_status': {
            'en': 'Shipping Cost Tax Status',
            'ja': '送料課税状況',
        },
        'tax': {
            'en': 'Tax',
            'ja': '税金',
        },
        'tax_applied_to': {
            'en': 'Tax Applied To',
            'ja': '税金が適用される対象',
        },
        'created_at': {
            'en': 'Created At',
            'ja': '作成日',
        },
    },
    'contacts': {
        'contact_id': {
            'en': 'Contact ID',
            'ja': '連絡先ID',
        },
        'name': {
            'en': 'First Name',
            'ja': '名前',
        },
        'last_name': {
            'en': 'Last Name',
            'ja': '苗字',
        },
        'email': {
            'en': 'Email',
            'ja': 'メールアドレス',
        },
        'company': {
            'en': 'Company',
            'ja': '企業',
        },
        'phone_number': {
            'en': 'Phone Number',
            'ja': '電話番号',
        },
        'status': {
            'en': 'Status',
            'ja': 'ステータス',
        },
        'created_at': {
            'en': 'Created At',
            'ja': '作成日',
        },
        'updated_at': {
            'en': 'Updated At',
            'ja': '更新日',
        },
    },
    'companies': {
        'company_id': {
            'en': 'Company Id',
            'ja': '企業ID',
        },
        'name': {
            'en': 'Name',
            'ja': '名前',
        },
        'address': {
            'en': 'Address',
            'ja': '住所',
        },
        'email': {
            'en': 'Email',
            'ja': 'メールアドレス',
        },
        'phone_number': {
            'en': 'Phone Number',
            'ja': '電話番号',
        },
        'created_at': {
            'en': 'Created At',
            'ja': '作成日',
        },
        'updated_at': {
            'en': 'Updated At',
            'ja': '更新日',
        },
        'status': {
            'en': 'Status',
            'ja': 'ステータス',
        },
    },
    'events': {
        'session': {
            'en': 'Session',
            'ja': 'セッション',
        },
        'is_dummy': {
            'en': 'Is Dummy',
            'ja': 'ダミーかどうか',
        },
        'created_at': {
            'en': 'Created At',
            'ja': '作成日',
        },
    },
    'inventory': {
        'inventory': {
            'en': "Inventory",
            'ja': "在庫",
        },
    },
    'deals': {
        'deal_id': {
            'en': 'Case Id',
            'ja': '案件ID',
        },
        'name': {
            'en': 'Name',
            'ja': '名前',
        },
        'status': {
            'en': 'Status',
            'ja': 'ステータス',
        },
        'created_at': {
            'en': 'Created At',
            'ja': '作成日',
        },
        'updated_at': {
            'en': 'Updated At',
            'ja': '更新日',
        }
    }
}

PERMISSION_SCOPES = ['all', 'team', 'user', 'none']
PERMISSION_TYPES = ['hide', 'read', 'edit', 'archive']

DEFAULT_PERMISSION = 'read'
RAKUTEN_EMAIL_HOST = 'sub.fw.rakuten.ne.jp'
RAKUTEN_EMAIL_PORT = 587
RAKUTEN_EMAIL_HOST_USER = "382866"
RAKUTEN_EMAIL_HOST_PASSWORD = "mLm53TLzLj"

APP_TARGET_SLUG = {
    'insights': 'insights',
    'shopturbo': 'commerce',
    'contacts': 'customer',
    'campaigns': 'campaign',
    'reports': 'dashboards',
    'taskflow': 'operations',
}

HUBSPOT_DEAL_STAGE_MAPPER = {
    '商談設定済み': 'appointmentscheduled',
    '購入見込みあり': 'qualifiedtobuy',
    '購入依頼あり': 'decisionmakerboughtin',
    '出荷依頼': 'contractsent',
    '出荷依頼済み': 'closedwon',
    '取引成立': '*********',
    '取引不成立': 'closedlost',
}

SVG_HEADER = """<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE svg
  PUBLIC '-//W3C//DTD SVG 1.1//EN'
  'http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd'>"""


FORMULA_RELATED_COLUMNS_DISPLAY = {
    "order": {
        "en": "Order",
        "ja": "受注"
    },
    "journal": {
        "en": "Account Receievalable",
        "ja": "売掛金"
    },
    "commerce_inventory_transaction": OBJECT_GROUP_TYPE["commerce_inventory_transaction"],
}


BILLINGS_PROCUREMENT_OBJECT_MAPPING = {
    'Estimate': 'estimates',
    'Invoice': 'invoices',
    'DeliverySlip': 'delivery_slips',
    'Slip': 'slips',
    'PurchaseOrders': 'purchaseorder',
    'PurchaseItems': 'purchaseorder',
}

SHEET_CONSTANT = {
    'stock_in': {
        'en': 'Stock In',
        'ja': '入庫'
    },
    'planned': {
        'en': 'Forecast',
        'ja': '予測'
    },
    'actual': {
        'en': 'Actual',
        'ja': '実績'
    },
    'stock_out': {
        'en': 'Stock Out',
        'ja': '出庫'
    },
    'shipping_forecast': {
        'en': 'Forecast',
        'ja': '出庫予測'
    },
    'special_forecast': {
        'en': 'Special Forecast',
        'ja': '特別予測'
    },
    'forecast_total': {
        'en': 'Forecast Total',
        'ja': '営業予測計'
    },
    'last_year_forecast_total': {
        'en': 'YoY',
        'ja': '昨年比予測'
    },
    'actual_revenue': {
        'en': 'Actual Revenue',
        'ja': '売上実績'
    },
    'expense': {
        'en': 'Expense',
        'ja': '経費出庫'
    },
    'total_shipment': {
        'en': 'Total',
        'ja': '出庫合計'
    },
    'inventory': {
        'en': 'inventory',
        'ja': '在庫'
    },
    'sales_forecast': {
        'en': 'Forecast',
        'ja': '営業予測'}
}

SHEET_ROW_OPTION = {
    'manual': {
        'en': 'Manual Entry',
        'ja': '手入力'
    },
    'formula': {
        'en': 'Formula',
        'ja': '数式'
    },
    'stock_in': {
        'en': 'Stock In',
        'ja': '入庫'
    },
    'stock_out': {
        'en': 'Stock Out',
        'ja': '出庫'
    },
    'revenue': {
        'en': 'Revenue',
        'ja': '売上'
    },
    'number_orders': {
        'en': 'Number Orders',
        'ja': '受注件数',
    },
    'invoice_revenue': {
        'en': 'Revenue',
        'ja': '売上'
    },
    'number_invoices': {
        'en': 'Number Invoices',
        'ja': '請求件数',
    },
    'cost': {
        'en': 'Cost',
        'ja': '原価'
    }
}

SHEET_PROPS_OPTION = {
    'blank_space': {
        'en': 'Space',
        'ja': 'スペース'
    },
    'column_header_bg': {
        'en': 'Column Header Background',
        'ja': '列ヘッダーの背景色'
    },
    'row_header_bg': {
        'en': 'Row Header Background',
        'ja': '行ヘッダーの背景色'
    },
    'merge_row_header': {
        'en': 'Merge Row Header',
        'ja': '行ヘッダーの結合'
    },
}

WIDGET_OPTION = {
    'default_apps': {
        'en': 'Content',
        'ja': 'コンテンツ',
    },
    'dashboard': {
        'en': 'Dashboard',
        'ja': 'ダッシュボード',
    }
}

HUBSPOT_PROPERTY_TYPE_MAP = {
    'IntegerField': 'number',
    'CharField': 'string',
    'ForeignKey': 'string',
    "BooleanField": 'bool',
    'FloatField': 'number',
    'TextField': 'string',
    'DateField': 'date',
    'DateTimeField': 'datetime',
    'UUIDField': 'string'
}

CUSTOM_FIELD_TYPE_MAP = {
    'text': 'string',
    'text-area': 'string',
    'choice': 'enumeration',
    'tag': 'string',
    'number': 'number',
    'date': 'date',
    'date_time': 'datetime',
    'user': 'string',
    'bill_objects': 'string',
    'invoice_objects': 'string',
    'contact': 'string',
    'company': 'string',
    'purchase_order': 'string',
    'subscription': 'string',
    'warehouse_objects': 'string',
    'phone': 'number',
}

EXCLUDE_SYNC_CHANNEL_NAME = [
    'Shopify Power Subscription', 'Hubspot Power Search']

BILLING_CYCLE_MAPPER = {**{str(i): {'en': str(i), 'ja': str(i)} for i in range(1, 32)},
                        'end': {'en': 'End of the month', 'ja': '月末'}}

WORKER_STATUS = [
    ('in_employment', 'In Employment'),
    ('resigned', 'Resigned'),
    ('terminated', 'Terminated'),
    ('retired', 'Retired'),
    ('on_leave', 'On Leave'),
    ('transferred', 'Transferred'),
    ('contract_ended', 'Contract Ended'),
]

DEFAULT_JOURNAL_CATEGORY = {
    # https://www.yayoi-kk.co.jp/kaikei/oyakudachi/shiwakedaizenshu/account/#anc-01
    "cash_deposit": {
        "en": "Cash Deposit",
        "ja": "現金預金",
        "choices": [
            {
                'en': 'Cash',
                'ja': '現金',
                'value': 'cash',
                'counter_category_income': 'cash',
                'counter_category_expense': 'cash'
            },
            {
                'en': 'Checking Account',
                'ja': '当座預金',
                'value': 'checking_account',
                'counter_category_income': 'checking_account',
                'counter_category_expense': 'checking_account'
            },
            {
                'en': 'Savings Account',
                'ja': '普通預金',
                'value': 'savings_account',
                'counter_category_income': 'savings_account',
                'counter_category_expense': 'savings_account'
            },
            {
                'en': 'Cash Over and Short',
                'ja': '現金過不足',
                'value': 'cash_over_and_short',
                'counter_category_income': 'cash_over_and_short',
                'counter_category_expense': 'cash_over_and_short'
            },
        ]
    },
    "accounts_receivable": {
        "en": "Accounts Receivable",
        "ja": "売上債権",
        "choices": [
            {
                "en": "Dishonored Notes",
                "ja": "不渡手形",
                "value": "dishonored_notes",
                "counter_category_income": "dishonored_notes",
                "counter_category_expense": "dishonored_notes"
            },
            {
                "en": "Notes Receivable",
                "ja": "受取手形",
                "value": "notes_receivable",
                "counter_category_income": "notes_receivable",
                "counter_category_expense": "notes_receivable"
            },
            {
                "en": "Accounts Receivable",
                "ja": "売掛金",
                "value": "accounts_receivable",
                "counter_category_income": "accounts_receivable",
                "counter_category_expense": "accounts_receivable"
            },
            {
                "en": "Allowance for Doubtful Accounts",
                "ja": "貸倒引当金",
                "value": "allowance_for_doubtful_accounts",
                "counter_category_income": "allowance_for_doubtful_accounts",
                "counter_category_expense": "allowance_for_doubtful_accounts"
            }
        ]
    },
    "inventories": {
        "en": "Inventories",
        "ja": "棚卸資産",
        "choices": [
            {
                "en": "Merchandise",
                "ja": "商品",
                "value": "merchandise",
                "counter_category_income": "merchandise",
                "counter_category_expense": "merchandise"
            },
            {
                "en": "Supplies",
                "ja": "貯蔵品",
                "value": "supplies",
                "counter_category_income": "supplies",
                "counter_category_expense": "supplies"
            }
        ]
    },
    "other_current_assets": {
        "en": "Other Current Assets",
        "ja": "その他の流動資産",
        "choices": [
            {
                "en": "Advance Payments",
                "ja": "前渡金",
                "value": "advance_payments",
                "counter_category_income": "advance_payments",
                "counter_category_expense": "advance_payments"
            },
            {
                "en": "Prepaid Expenses",
                "ja": "前払費用",
                "value": "prepaid_expenses",
                "counter_category_income": "prepaid_expenses",
                "counter_category_expense": "prepaid_expenses"
            },
            {
                "en": "Accrued Revenue",
                "ja": "未収収益",
                "value": "accrued_revenue",
                "counter_category_income": "accrued_revenue",
                "counter_category_expense": "accrued_revenue"
            },
            {
                "en": "Accounts Receivable - Other",
                "ja": "未収入金",
                "value": "accounts_receivable_other",
                "counter_category_income": "accounts_receivable_other",
                "counter_category_expense": "accounts_receivable_other"
            },
            {
                "en": "Deposits Paid",
                "ja": "預け金",
                "value": "deposits_paid",
                "counter_category_income": "deposits_paid",
                "counter_category_expense": "deposits_paid"
            },
            {
                "en": "Advances Paid",
                "ja": "立替金",
                "value": "advances_paid",
                "counter_category_income": "advances_paid",
                "counter_category_expense": "advances_paid"
            },
            {
                "en": "Suspense Payments",
                "ja": "仮払金",
                "value": "suspense_payments",
                "counter_category_income": "suspense_payments",
                "counter_category_expense": "suspense_payments"
            },
            {
                "en": "Suspense Consumption Tax",
                "ja": "仮払消費税等",
                "value": "suspense_consumption_tax",
                "counter_category_income": "suspense_consumption_tax",
                "counter_category_expense": "suspense_consumption_tax"
            }
        ]
    },
    "tangible_fixed_assets": {
        "en": "Tangible Fixed Assets",
        "ja": "有形固定資産",
        "choices": [
            {
                "en": "Buildings",
                "ja": "建物",
                "value": "buildings",
                "counter_category_income": "buildings",
                "counter_category_expense": "buildings"
            },
            {
                "en": "Building Attachments",
                "ja": "建物附属設備",
                "value": "building_attachments",
                "counter_category_income": "building_attachments",
                "counter_category_expense": "building_attachments"
            },
            {
                "en": "Machinery and Equipment",
                "ja": "機械装置",
                "value": "machinery_and_equipment",
                "counter_category_income": "machinery_and_equipment",
                "counter_category_expense": "machinery_and_equipment"
            },
            {
                "en": "Vehicles and Transportation Equipment",
                "ja": "車両運搬具",
                "value": "vehicles_and_transportation_equipment",
                "counter_category_income": "vehicles_and_transportation_equipment",
                "counter_category_expense": "vehicles_and_transportation_equipment"
            },
            {
                "en": "Tools, Furniture, and Fixtures",
                "ja": "工具器具備品",
                "value": "tools_furniture_and_fixtures",
                "counter_category_income": "tools_furniture_and_fixtures",
                "counter_category_expense": "tools_furniture_and_fixtures"
            },
            {
                "en": "Land",
                "ja": "土地",
                "value": "land",
                "counter_category_income": "land",
                "counter_category_expense": "land"
            },
            {
                "en": "Lump-sum Depreciable Assets",
                "ja": "一括償却資産",
                "value": "lump_sum_depreciable_assets",
                "counter_category_income": "lump_sum_depreciable_assets",
                "counter_category_expense": "lump_sum_depreciable_assets"
            },
            {
                "en": "Construction in Progress",
                "ja": "建設仮勘定",
                "value": "construction_in_progress",
                "counter_category_income": "construction_in_progress",
                "counter_category_expense": "construction_in_progress"
            },
            {
                "en": "Accumulated Depreciation",
                "ja": "減価償却累計額",
                "value": "accumulated_depreciation",
                "counter_category_income": "accumulated_depreciation",
                "counter_category_expense": "accumulated_depreciation"
            }
        ]
    },
    "intangible_fixed_assets": {
        "en": "Intangible Fixed Assets",
        "ja": "無形固定資産",
        "choices": [
            {
                "en": "Telephone Subscription Rights",
                "ja": "電話加入権",
                "value": "telephone_subscription_rights",
                "counter_category_income": "telephone_subscription_rights",
                "counter_category_expense": "telephone_subscription_rights"
            },
            {
                "en": "Industrial Property Rights",
                "ja": "工業所有権",
                "value": "industrial_property_rights",
                "counter_category_income": "industrial_property_rights",
                "counter_category_expense": "industrial_property_rights"
            },
            {
                "en": "Software",
                "ja": "ソフトウェア",
                "value": "software",
                "counter_category_income": "software",
                "counter_category_expense": "software"
            }
        ]
    },
    "investments_and_other_assets": {
        "en": "Investments and Other Assets",
        "ja": "投資その他の資産",
        "choices": [
            {
                "en": "Long-term Prepaid Expenses",
                "ja": "長期前払費用",
                "value": "long_term_prepaid_expenses",
                "counter_category_income": "long_term_prepaid_expenses",
                "counter_category_expense": "long_term_prepaid_expenses"
            },
            {
                "en": "Security Deposits",
                "ja": "敷金",
                "value": "security_deposits",
                "counter_category_income": "security_deposits",
                "counter_category_expense": "security_deposits"
            },
            {
                "en": "Guarantee Deposits Paid",
                "ja": "差入保証金",
                "value": "guarantee_deposits_paid",
                "counter_category_income": "guarantee_deposits_paid",
                "counter_category_expense": "guarantee_deposits_paid"
            },
            {
                "en": "Deposits",
                "ja": "預託金",
                "value": "deposits",
                "counter_category_income": "deposits",
                "counter_category_expense": "deposits"
            }
        ]
    },
    "deferred_assets": {
        "en": "Deferred Assets",
        "ja": "繰延資産",
        "choices": [
            {
                "en": "Incorporation Expenses",
                "ja": "創立費",
                "value": "incorporation_expenses",
                "counter_category_income": "incorporation_expenses",
                "counter_category_expense": "incorporation_expenses"
            },
            {
                "en": "Business Commencement Expenses",
                "ja": "開業費",
                "value": "business_commencement_expenses",
                "counter_category_income": "business_commencement_expenses",
                "counter_category_expense": "business_commencement_expenses"
            },
        ]
    },
    "accounts_payable": {
        "en": "Accounts Payable",
        "ja": "仕入債務",
        "choices": [
            {
                "en": "Accounts Payable",
                "ja": "買掛金",
                "value": "accounts_payable",
                "counter_category_income": "accounts_payable",
                "counter_category_expense": "accounts_payable"
            },
            {
                "en": "Notes Payable",
                "ja": "支払手形",
                "value": "notes_payable",
                "counter_category_income": "notes_payable",
                "counter_category_expense": "notes_payable"
            },
            {
                "en": "Discounted Notes Payable",
                "ja": "割引手形",
                "value": "discounted_notes_payable",
                "counter_category_income": "discounted_notes_payable",
                "counter_category_expense": "discounted_notes_payable"
            },
            {
                "en": "Endorsement Notes",
                "ja": "裏書手形",
                "value": "endorsement_notes",
                "counter_category_income": "endorsement_notes",
                "counter_category_expense": "endorsement_notes"
            },
        ]
    },
    "other_current_liabilities": {
        "en": "Other Current Liabilities",
        "ja": "その他の流動負債",
        "choices": [
            {
                "en": "Accounts Payable - Other",
                "ja": "未払金",
                "value": "accounts_payable_other",
                "counter_category_income": "accounts_payable_other",
                "counter_category_expense": "accounts_payable_other"
            },
            {
                "en": "Accrued Expenses",
                "ja": "未払費用",
                "value": "accrued_expenses",
                "counter_category_income": "accrued_expenses",
                "counter_category_expense": "accrued_expenses"
            },
            {
                "en": "Advances Received",
                "ja": "前受金",
                "value": "advances_received",
                "counter_category_income": "advances_received",
                "counter_category_expense": "advances_received"
            },
            {
                "en": "Deposits Received",
                "ja": "預り金",
                "value": "deposits_received",
                "counter_category_income": "deposits_received",
                "counter_category_expense": "deposits_received"
            },
            {
                "en": "Suspense Receipts",
                "ja": "仮受金",
                "value": "suspense_receipts",
                "counter_category_income": "suspense_receipts",
                "counter_category_expense": "suspense_receipts"
            },
            {
                "en": "Deposits Received",
                "ja": "預かり補償金",
                "value": "deposits_received",
                "counter_category_income": "deposits_received",
                "counter_category_expense": "deposits_received"
            },
            {
                "en": "Accrued Consumption Tax",
                "ja": "仮受消費税等",
                "value": "accrued_consumption_tax",
                "counter_category_income": "accrued_consumption_tax",
                "counter_category_expense": "accrued_consumption_tax"
            },
            {
                "en": "Non Paid Consumption Tax",
                "ja": "未払消費税等",
                "value": "non_paid_consumption_tax",
                "counter_category_income": "non_paid_consumption_tax",
                "counter_category_expense": "non_paid_consumption_tax"
            },
            {
                "en": "Non Paid Corporate Tax",
                "ja": "未払法人税等",
                "value": "non_paid_corporate_tax",
                "counter_category_income": "non_paid_corporate_tax",
                "counter_category_expense": "non_paid_corporate_tax"
            },
            {
                "en": "Short-term Borrowings",
                "ja": "短期借入金",
                "value": "short_term_borrowings",
                "counter_category_income": "short_term_borrowings",
                "counter_category_expense": "short_term_borrowings"
            },
        ]
    },
    "fixed_liabilities": {
        "en": "Fixed Liabilities",
        "ja": "固定負債",
        "choices": [
            {
                "en": "Long-term Borrowings",
                "ja": "長期借入金",
                "value": "long_term_borrowings",
                "counter_category_income": "long_term_borrowings",
                "counter_category_expense": "long_term_borrowings"
            },
        ]
    },
    "capital_stock": {
        "en": "Capital Stock",
        "ja": "資本金・元入金",
        "choices": [
            {
                "en": "Capital Stock",
                "ja": "資本金",
                "value": "capital_stock",
                "counter_category_income": "capital_stock",
                "counter_category_expense": "capital_stock"
            },
            {
                "en": "Capital",
                "ja": "資本金",
                "value": "capital",
                "counter_category_income": "capital",
                "counter_category_expense": "capital"
            },
            {
                "en": "Capital Reserve",
                "ja": "資本準備金",
                "value": "capital_reserve",
                "counter_category_income": "capital_reserve",
                "counter_category_expense": "capital_reserve"
            },
            {
                "en": "Profit Reserve",
                "ja": "利益準備金",
                "value": "profit_reserve",
                "counter_category_income": "profit_reserve",
                "counter_category_expense": "profit_reserve"
            }
        ]
    },
    "revenue": {
        "en": "Revenue",
        "ja": "売上高",
        "choices": [
            {
                "en": "Sales",
                "ja": "売上高",
                "value": "sales",
                "counter_category_income": "sales",
                "counter_category_expense": "sales"
            },
            {
                "en": "Sales Discount",
                "ja": "売上値引高",
                "value": "sales_discount",
                "counter_category_income": "sales_discount",
                "counter_category_expense": "sales_discount"
            },
            {
                "en": "Sales Return",
                "ja": "売上戻り高",
                "value": "sales_return",
                "counter_category_income": "sales_return",
                "counter_category_expense": "sales_return"
            },
            {
                "en": "Sales Rebate",
                "ja": "売上割戻し高",
                "value": "sales_rebate",
                "counter_category_income": "sales_rebate",
                "counter_category_expense": "sales_rebate"
            }
        ]
    },
    "cost_of_goods_sold": {
        "en": "Cost of Goods Sold",
        "ja": "商品・製品の売上原価",
        "choices": [
            {
                "en": "Beginning Inventory",
                "ja": "期首商品棚卸高",
                "value": "beginning_inventory",
                "counter_category_income": "beginning_inventory",
                "counter_category_expense": "beginning_inventory"
            },
            {
                "en": "Purchases",
                "ja": "仕入高",
                "value": "purchases",
                "counter_category_income": "purchases",
                "counter_category_expense": "purchases"
            },
            {
                "en": "Ending Inventory",
                "ja": "期末商品棚卸高",
                "value": "ending_inventory",
                "counter_category_income": "ending_inventory",
                "counter_category_expense": "ending_inventory"
            },
            {
                "en": "Purchase Discounts",
                "ja": "仕入値引高",
                "value": "purchase_discounts",
                "counter_category_income": "purchase_discounts",
                "counter_category_expense": "purchase_discounts"
            },
            {
                "en": "Purchase Returns",
                "ja": "仕入戻し高",
                "value": "purchase_returns",
                "counter_category_income": "purchase_returns",
                "counter_category_expense": "purchase_returns"
            },
            {
                "en": "Purchase Discounts",
                "ja": "仕入割戻し高",
                "value": "purchase_discounts",
                "counter_category_income": "purchase_discounts",
                "counter_category_expense": "purchase_discounts"
            },
            {
                "en": "Other Account Transfers",
                "ja": "他勘定振替高",
                "value": "other_account_transfers",
                "counter_category_income": "other_account_transfers",
                "counter_category_expense": "other_account_transfers"
            },
        ]
    },
    "personnel_expenses": {
        "en": "Personnel Expenses",
        "ja": "人件費",
        "choices": [
            {
                "en": "Salaries and Allowances",
                "ja": "給料手当",
                "value": "salaries_and_allowances",
                "counter_category_income": "salaries_and_allowances",
                "counter_category_expense": "salaries_and_allowances"
            },
            {
                "en": "Bonuses",
                "ja": "賞与",
                "value": "bonuses",
                "counter_category_income": "bonuses",
                "counter_category_expense": "bonuses"
            },
            {
                "en": "Executive Compensation",
                "ja": "役員報酬",
                "value": "executive_compensation",
                "counter_category_income": "executive_compensation",
                "counter_category_expense": "executive_compensation"
            },
            {
                "en": "Executive Bonuses",
                "ja": "役員賞与",
                "value": "executive_bonuses",
                "counter_category_income": "executive_bonuses",
                "counter_category_expense": "executive_bonuses"
            },
            {
                "en": "Welfare Expenses",
                "ja": "福利厚生費",
                "value": "welfare_expenses",
                "counter_category_income": "welfare_expenses",
                "counter_category_expense": "welfare_expenses"
            },
            {
                "en": "Statutory Welfare Expenses",
                "ja": "法定福利費",
                "value": "statutory_welfare_expenses",
                "counter_category_income": "statutory_welfare_expenses",
                "counter_category_expense": "statutory_welfare_expenses"
            },
            {
                "en": "Retirement Benefits",
                "ja": "退職金",
                "value": "retirement_benefits",
                "counter_category_income": "retirement_benefits",
                "counter_category_expense": "retirement_benefits"
            },
            {
                "en": "Miscellaneous Wages",
                "ja": "雑給",
                "value": "miscellaneous_wages",
                "counter_category_income": "miscellaneous_wages",
                "counter_category_expense": "miscellaneous_wages"
            },
            {
                "en": "Family Employee Salaries",
                "ja": "専従者給与",
                "value": "family_employee_salaries",
                "counter_category_income": "family_employee_salaries",
                "counter_category_expense": "family_employee_salaries"
            }
        ]
    },
    "selling_general_and_administrative_expenses": {
        "en": "Selling, General and Administrative Expenses",
        "ja": "販売費および一般管理費",
        "choices": [
            {
                "en": "Advertising Expenses",
                "ja": "広告宣伝費",
                "value": "advertising_expenses",
                "counter_category_income": "advertising_expenses",
                "counter_category_expense": "advertising_expenses"
            },
            {
                "en": "Sales Commission",
                "ja": "販売手数料",
                "value": "sales_commission",
                "counter_category_income": "sales_commission",
                "counter_category_expense": "sales_commission"
            },
            {
                "en": "Packing and Freight",
                "ja": "荷造運賃",
                "value": "packing_and_freight",
                "counter_category_income": "packing_and_freight",
                "counter_category_expense": "packing_and_freight"
            },
            {
                "en": "Travel Expenses",
                "ja": "旅費交通費",
                "value": "travel_expenses",
                "counter_category_income": "travel_expenses",
                "counter_category_expense": "travel_expenses"
            },
            {
                "en": "Communication Expenses",
                "ja": "通信費",
                "value": "communication_expenses",
                "counter_category_income": "communication_expenses",
                "counter_category_expense": "communication_expenses"
            },
            {
                "en": "Utilities",
                "ja": "水道光熱費",
                "value": "utilities",
                "counter_category_income": "utilities",
                "counter_category_expense": "utilities"
            },
            {
                "en": "Rent",
                "ja": "賃借料",
                "value": "rent",
                "counter_category_income": "rent",
                "counter_category_expense": "rent"
            },
            {
                "en": "Lease Payments",
                "ja": "リース料",
                "value": "lease_payments",
                "counter_category_income": "lease_payments",
                "counter_category_expense": "lease_payments"
            },
            {
                "en": "Depreciation",
                "ja": "減価償却費",
                "value": "depreciation",
                "counter_category_income": "depreciation",
                "counter_category_expense": "depreciation"
            },
            {
                "en": "Deferred Asset Amortization",
                "ja": "繰延資産償却",
                "value": "deferred_asset_amortization",
                "counter_category_income": "deferred_asset_amortization",
                "counter_category_expense": "deferred_asset_amortization"
            },
            {
                "en": "Taxes and Dues",
                "ja": "租税公課",
                "value": "taxes_and_dues",
                "counter_category_income": "taxes_and_dues",
                "counter_category_expense": "taxes_and_dues"
            },
            {
                "en": "Insurance Expenses",
                "ja": "保険料",
                "value": "insurance_expenses",
                "counter_category_income": "insurance_expenses",
                "counter_category_expense": "insurance_expenses"
            },
            {
                "en": "Supplies Expenses",
                "ja": "消耗品費",
                "value": "supplies_expenses",
                "counter_category_income": "supplies_expenses",
                "counter_category_expense": "supplies_expenses"
            },
            {
                "en": "Repair and Maintenance",
                "ja": "修繕費",
                "value": "repair_and_maintenance",
                "counter_category_income": "repair_and_maintenance",
                "counter_category_expense": "repair_and_maintenance"
            },
            {
                "en": "Entertainment Expenses",
                "ja": "交際費",
                "value": "entertainment_expenses",
                "counter_category_income": "entertainment_expenses",
                "counter_category_expense": "entertainment_expenses"
            },
            {
                "en": "Meeting Expenses",
                "ja": "会議費",
                "value": "meeting_expenses",
                "counter_category_income": "meeting_expenses",
                "counter_category_expense": "meeting_expenses"
            },
            {
                "en": "Vehicle Expenses",
                "ja": "車両費",
                "value": "vehicle_expenses",
                "counter_category_income": "vehicle_expenses",
                "counter_category_expense": "vehicle_expenses"
            },
            {
                "en": "Recruitment and Training Expenses",
                "ja": "採用教育費",
                "value": "recruitment_and_training_expenses",
                "counter_category_income": "recruitment_and_training_expenses",
                "counter_category_expense": "recruitment_and_training_expenses"
            },
            {
                "en": "Outsourcing Expenses",
                "ja": "外注費",
                "value": "outsourcing_expenses",
                "counter_category_income": "outsourcing_expenses",
                "counter_category_expense": "outsourcing_expenses"
            },
            {
                "en": "Provision for Doubtful Accounts",
                "ja": "貸倒引当金繰入",
                "value": "provision_for_doubtful_accounts",
                "counter_category_income": "provision_for_doubtful_accounts",
                "counter_category_expense": "provision_for_doubtful_accounts"
            },
            {
                "en": "Miscellaneous Expenses",
                "ja": "雑費",
                "value": "miscellaneous_expenses",
                "counter_category_income": "miscellaneous_expenses",
                "counter_category_expense": "miscellaneous_expenses"
            },
            {
                "en": "Professional Fees",
                "ja": "支払報酬",
                "value": "professional_fees",
                "counter_category_income": "professional_fees",
                "counter_category_expense": "professional_fees"
            },
            {
                "en": "Office Supplies",
                "ja": "事務用品費",
                "value": "office_supplies",
                "counter_category_income": "office_supplies",
                "counter_category_expense": "office_supplies"
            },
            {
                "en": "Sales Promotion Expenses",
                "ja": "販売促進費",
                "value": "sales_promotion_expenses",
                "counter_category_income": "sales_promotion_expenses",
                "counter_category_expense": "sales_promotion_expenses"
            },
            {
                "en": "Warranty Expenses",
                "ja": "製品保証費",
                "value": "warranty_expenses",
                "counter_category_income": "warranty_expenses",
                "counter_category_expense": "warranty_expenses"
            },
            {
                "en": "Bad Debt Expenses",
                "ja": "貸倒損失",
                "value": "bad_debt_expenses",
                "counter_category_income": "bad_debt_expenses",
                "counter_category_expense": "bad_debt_expenses"
            },
            {
                "en": "Provision for Doubtful Accounts",
                "ja": "貸倒引当金繰入額",
                "value": "provision_for_doubtful_accounts",
                "counter_category_income": "provision_for_doubtful_accounts",
                "counter_category_expense": "provision_for_doubtful_accounts"
            },
            {
                "en": "Payment Fees",
                "ja": "支払手数料",
                "value": "payment_fees",
                "counter_category_income": "payment_fees",
                "counter_category_expense": "payment_fees"
            },
            {
                "en": "Donations",
                "ja": "寄付金",
                "value": "donations",
                "counter_category_income": "donations",
                "counter_category_expense": "donations"
            },
            {
                "en": "Vehicle Expenses",
                "ja": "車両費",
                "value": "vehicle_expenses",
                "counter_category_income": "vehicle_expenses",
                "counter_category_expense": "vehicle_expenses"
            }
        ]
    },
    "other_income": {
        "en": "Other Income",
        "ja": "その他の収入・収益",
        "choices": [
            {
                "en": "Interest Received",
                "ja": "受取利息",
                "value": "interest_received",
                "counter_category_income": "interest_received",
                "counter_category_expense": "interest_received"
            },
            {
                "en": "Return of Provision Income",
                "ja": "貸倒引当金戻入額",
                "value": "return_of_provision_income",
                "counter_category_income": "return_of_provision_income",
                "counter_category_expense": "return_of_provision_income"
            },
            {
                "en": "Purchase Discounts",
                "ja": "仕入割引",
                "value": "purchase_discounts",
                "counter_category_income": "purchase_discounts",
                "counter_category_expense": "purchase_discounts"
            },
            {
                "en": "Housework Consumption",
                "ja": "家事消費等",
                "value": "housework_consumption",
                "counter_category_income": "housework_consumption",
                "counter_category_expense": "housework_consumption"
            },
            {
                "en": "Gains from Sale of Fixed Assets",
                "ja": "固定資産売却益",
                "value": "gains_from_sale_of_fixed_assets",
                "counter_category_income": "gains_from_sale_of_fixed_assets",
                "counter_category_expense": "gains_from_sale_of_fixed_assets"
            },
            {
                "en": "Miscellaneous Income",
                "ja": "雑収入",
                "value": "miscellaneous_income",
                "counter_category_income": "miscellaneous_income",
                "counter_category_expense": "miscellaneous_income"
            }
        ]
    },
    "other_expenses": {
        "en": "Other Expenses",
        "ja": "その他の支出・経費",
        "choices": [
            {
                "en": "Interest Expenses",
                "ja": "支払利息",
                "value": "interest_expenses",
                "counter_category_income": "interest_expenses",
                "counter_category_expense": "interest_expenses"
            },
            {
                "en": "Loss on Sale of Bills Receivable",
                "ja": "手形売却損",
                "value": "loss_on_sale_of_bills_receivable",
                "counter_category_income": "loss_on_sale_of_bills_receivable",
                "counter_category_expense": "loss_on_sale_of_bills_receivable"
            },
            {
                "en": "Sales Discounts",
                "ja": "売上割引",
                "value": "sales_discounts",
                "counter_category_income": "sales_discounts",
                "counter_category_expense": "sales_discounts"
            },
            {
                "en": "Loss on Sale of Fixed Assets",
                "ja": "固定資産売却損",
                "value": "loss_on_sale_of_fixed_assets",
                "counter_category_income": "loss_on_sale_of_fixed_assets",
                "counter_category_expense": "loss_on_sale_of_fixed_assets"
            },
            {
                "en": "Donations",
                "ja": "寄付金",
                "value": "donations",
                "counter_category_income": "donations",
                "counter_category_expense": "donations"
            },
            {
                "en": "Loss on Disposal of Fixed Assets",
                "ja": "固定資産除却損",
                "value": "loss_on_disposal_of_fixed_assets",
                "counter_category_income": "loss_on_disposal_of_fixed_assets",
                "counter_category_expense": "loss_on_disposal_of_fixed_assets"
            }
        ]
    },
    "corporate_tax": {
        "en": "Corporate Tax",
        "ja": "法人税等",
        "choices": [
            {
                "en": "Corporate Tax",
                "ja": "法人税等",
                "value": "corporate_tax",
                "counter_category_income": "corporate_tax",
                "counter_category_expense": "corporate_tax"
            }
        ]
    },
    "other_rental_income": {
        "en": "Borrowing and Lending",
        "ja": "事業主貸・事業主借",
        "choices": [
            {
                "en": "Business Owner Loan",
                "ja": "事業主貸",
                "value": "business_owner_loan",
                "counter_category_income": "business_owner_loan",
                "counter_category_expense": "business_owner_loan"
            },
            {
                "en": "Business Owner Borrowing",
                "ja": "事業主借",
                "value": "business_owner_borrowing",
                "counter_category_income": "business_owner_borrowing",
                "counter_category_expense": "business_owner_borrowing"
            }
        ]
    }

}


JOURNAL_CATEGORY_DISPLAY = [
    {
        'category': 'last',
        'choices': [
            {'label': '1', 'value': '2'}]
    },
    {
        'category': 'last',
        'choices': [
            {'label': '2', 'value': '4'}]
    },
    {
        'category': 'last',
        'choices': [
            {'label': '1', 'value': '2'}]
    },
    {
        'category': 'last',
        'choices': [
            {'label': '2', 'value': '4'}]
    }
]


JOURNAL_TAX_CATEGORY_DISPLAY = {
    "10": {
        "en": "Taxable Sales 10%",
        "ja": "課税販売 10%"
    },
    "8": {
        "en": "Taxable Sales 8%",
        "ja": "課税販売 8%"
    },
    "0": {
        "en": "Non Taxable",
        "ja": "非課税販売"
    }
}

JOURNAL_COUNTER_CATEGORY_DISPLAY = {
    "": {
        "en": "",
        "ja": ""
    }
}


FORM_COLUMNS_DISPLAY = {
    "title": {
        "en": "Form Title",
        "ja": "フォーム名"
    },
    "user": {
        "en": "Created by",
        "ja": "作成者"
    },
    "entries": {
        "en": "Entries",
        "ja": "	エントリー"
    },
    "created_at": {
        "en": "Created Date",
        "ja": "作成日時"
    },
    "updated_at": {
        "en": "Updated Date",
        "ja": "更新日時"
    },
    "visibility": {
        "en": "Status",
        "ja": "ステータス"
    },
    "owner": {
        "en": 'Owner',
        "ja": '所有者'
    }
}


DEFAULT_JOURNAL_CATEGORY_CORRESPONDING = {
    "[確]一時収入": [
        "未収入金",
        "未払金"
    ],
    "[確]保険金補填（医療費）": [
        "未収入金",
        "未払金"
    ],
    "[確]利子収入": [
        "未収入金",
        "未払金"
    ],
    "[確]小規模企業等掛金": [
        "未収入金",
        "未払金"
    ],
    "[確]支払医療費": [
        "未収入金",
        "未払金"
    ],
    "[確]社会保険料": [
        "未収入金",
        "未払金"
    ],
    "[確]給与収入": [
        "未収入金",
        "未払金"
    ],
    "[確]総合譲渡収入（短期）": [
        "未収入金",
        "未払金"
    ],
    "[確]総合譲渡収入（長期）": [
        "未収入金",
        "未払金"
    ],
    "[確]配当収入": [
        "未収入金",
        "未払金"
    ],
    "[確]雑収入（その他）": [
        "未収入金",
        "未払金"
    ],
    "[確]雑収入（公的年金等）": [
        "未収入金",
        "未払金"
    ],
    "software": [
        "未収入金",
        "未払金"
    ],
    "lease_payments": [
        "未収収益",
        "未払金"
    ],
    "lump_sum_depreciable_assets": [
        "未収入金",
        "未払金"
    ],
    "dishonored_notes": [
        "未収入金",
        "未払金"
    ],
    "office_supplies": [
        "未収収益",
        "未払金"
    ],
    "business_owner_borrowing": [
        "未収入金",
        "未払金"
    ],
    "business_owner_loan": [
        "未収入金",
        "未払金"
    ],
    "事業分量配当金": [
        "未収入金",
        "未払費用"
    ],
    "entertainment_expenses": [
        "未収収益",
        "未払金"
    ],
    "purchase_discounts": [
        "未収収益",
        "買掛金"
    ],
    "purchase_discounts": [
        "未収入金",
        "未払費用"
    ],
    "purchase_discounts": [
        "未収収益",
        "買掛金"
    ],
    "purchase_returns": [
        "未収収益",
        "買掛金"
    ],
    "purchases": [
        "未収収益",
        "買掛金"
    ],
    "仕掛品": [
        "未収入金",
        "未払金"
    ],
    "accrued_consumption_tax": [
        "未収入金",
        "未払金"
    ],
    "suspense_receipts": [
        "未収入金",
        "未払金"
    ],
    "suspense_consumption_tax": [
        "未収入金",
        "未払金"
    ],
    "suspense_payments": [
        "未収入金",
        "未払金"
    ],
    "meeting_expenses": [
        "未収収益",
        "未払金"
    ],
    "保証金・敷金": [
        "未収入金",
        "未払金"
    ],
    "insurance_expenses": [
        "未収収益",
        "未払金"
    ],
    "repair_and_maintenance": [
        "未収収益",
        "未払金"
    ],
    "借地権": [
        "未収入金",
        "未払金"
    ],
    "元入金": [
        "未収入金",
        "未払金"
    ],
    "出資金": [
        "未収入金",
        "未払金"
    ],
    "前受収益": [
        "未収入金",
        "未払金"
    ],
    "advances_received": [
        "未収入金",
        "未払金"
    ],
    "前年度繰越収益": [
        "売掛金",
        "未払費用"
    ],
    "前年度繰越費用": [
        "未収収益",
        "買掛金"
    ],
    "prepaid_expenses": [
        "未収入金",
        "未払金"
    ],
    "前払金": [
        "未収入金",
        "未払金"
    ],
    "副産物作業くず": [
        "未収入金",
        "未払金"
    ],
    "discounted_notes_payable": [
        "未収入金",
        "未払金"
    ],
    "割引料": [
        "未収収益",
        "未払金"
    ],
    "半製品": [
        "未収入金",
        "未払金"
    ],
    "原材料": [
        "未収入金",
        "未払金"
    ],
    "取材費": [
        "未収入金",
        "未払金"
    ],
    "interest_received": [
        "未収入金",
        "未払金"
    ],
    "notes_receivable": [
        "未収入金",
        "未払金"
    ],
    "受取配当金": [
        "未収入金",
        "未払金"
    ],
    "merchandise": [
        "未収入金",
        "未払金"
    ],
    "land": [
        "未収入金",
        "未払金"
    ],
    "地代家賃": [
        "未収収益",
        "未払金"
    ],
    "sales_discount": [
        "売掛金",
        "未払費用"
    ],
    "sales_discounts": [
        "未収収益",
        "未払金"
    ],
    "sales_return": [
        "売掛金",
        "未払費用"
    ],
    "sales": [
        "売掛金",
        "未払費用"
    ],
    "accounts_receivable": [
        "未収入金",
        "未払金"
    ],
    "outsourcing_expenses": [
        "未収収益",
        "未払金"
    ],
    "housework_consumption": [
        "売掛金",
        "未払費用"
    ],
    "family_employee_salaries": [
        "未収収益",
        "未払金"
    ],
    "tools_furniture_and_fixtures": [
        "未収入金",
        "未払金"
    ],
    "industrial_property_rights": [
        "未収入金",
        "未払金"
    ],
    "guarantee_deposits_paid": [
        "未収入金",
        "未払金"
    ],
    "advertising_expenses": [
        "未収収益",
        "未払金"
    ],
    "buildings": [
        "未収入金",
        "未払金"
    ],
    "construction_in_progress": [
        "未収入金",
        "未払金"
    ],
    "投資有価証券": [
        "未収入金",
        "未払金"
    ],
    "recruitment_and_training_expenses": [
        "未収収益",
        "未払金"
    ],
    "損益": [
        "未収入金",
        "未払金"
    ],
    "interest_expenses": [
        "未収収益",
        "未払金"
    ],
    "notes_payable": [
        "未収入金",
        "未払金"
    ],
    "payment_fees": [
        "未収収益",
        "未払金"
    ],
    "security_deposits": [
        "未収入金",
        "未払金"
    ],
    "新聞図書費": [
        "未収収益",
        "未払金"
    ],
    "施設利用権": [
        "未収入金",
        "未払金"
    ],
    "travel_expenses": [
        "未収収益",
        "未払金"
    ],
    "有価証券": [
        "未収入金",
        "未払金"
    ],
    "ending_inventory": [
        "未収収益",
        "買掛金"
    ],
    "期末製品棚卸高": [
        "未収収益",
        "買掛金"
    ],
    "beginning_inventory": [
        "売掛金",
        "買掛金"
    ],
    "期首製品棚卸高": [
        "未収収益",
        "買掛金"
    ],
    "accounts_receivable_other": [
        "未収入金",
        "未払金"
    ],
    "accrued_revenue": [
        "未収入金",
        "未払金"
    ]
}

CATEGORY_TRANSLATE_ENG = {
    '未収入金':     'Accounts receivable',
    '未収収益':     'Accrued Revenue',
    '売掛金':       'Accounts receivable',

    '未払金':       'Accounts payable',
    '未払費用':     'Accrued expenses',
    '買掛金':       'Accounts Payable',
}

PANEL_COLUMNS_DISPLAY = {
    'panel_id': {
        'en': 'ID',
        'ja': 'ID'
    },
    'name': {
        'en': 'Name',
        'ja': '名前'
    },
    'data_source_type': {
        'en': 'Data Source Type',
        'ja': 'データソースの種類'
    },
    'data_source': {
        'en': 'Data Source',
        'ja': 'データソース'
    },
    'sheet_rows': {
        'en': 'Sheet Rows',
        'ja': 'シート行'
    },
    'panel_type': {
        'en': 'Report Type',
        'ja': 'レポートタイプ'
    },
    'created_at': {
        'en': 'Created At',
        'ja': '作成日'
    },
    'updated_at': {
        'en': 'Updated At',
        'ja': '更新日'
    },
    'metrices': {
        'en': 'Metrics',
        'ja': '指標'
    },
    'breakdown': {
        'en': 'Breakdown',
        'ja': '内訳'
    },
    'web_url': {
        'en': 'Web URL',
        'ja': 'ウェブURL'
    },
    'ratio': {
        'en': 'Ratio',
        'ja': '比率'
    },
    'group_by': {
        'en': 'Group By',
        'ja': 'グループ化'
    },
    'start_time': {
        'en': 'Start Time',
        'ja': '開始時間'
    },
    'end_time': {
        'en': 'End Time',
        'ja': '終了時間'
    },
    'date_format': {
        'en': 'Date Format',
        'ja': '日付形式'
    },
    'blank_space_sheet': {
        'en': 'Blank Space Sheet',
        'ja': '空白スペースシート'
    },
    'hide_rows': {
        'en': 'Hide Rows',
        'ja': '行を隠す'
    },
    'hide_cols': {
        'en': 'Hide Columns',
        'ja': '列を隠す'
    },
    'is_deleted': {
        'en': 'Usage Status',
        'ja': '利用ステータス'
    },
    'created_by': {
        'en': 'Created By',
        'ja': '作成者'
    },
    'type_objects': {
        'en': 'Objects',
        'ja': 'オブジェクト'
    },
    "owner": {
        "en": 'Owner',
        "ja": '所有者'
    }
}

DASHBOARD_COLUMNS_DISPLAY = {
    'name': {
        'en': 'Name',
        'ja': '名前'
    },
    'created_at': {
        'en': 'Created At',
        'ja': '作成日'
    },
    'updated_at': {
        'en': 'Last Updated At',
        'ja': '最終更新日時'
    },
    'created_by': {
        'en': 'Created By',
        'ja': '作成者'
    },
    'updated_by': {
        'en': 'Last Updated By',
        'ja': '最終更新者'
    },
    'panels': {
        'en': 'Reports',
        'ja': 'レポート'
    },
    'panelreportpanel__panel__name': {
        'en': 'Reports - Name',
        'ja': 'レポート - 名前'
    },
    'panelreportpanel__panel__panel_type': {
        'en': 'Reports - Report Type',
        'ja': 'レポート - レポートタイプ'
    },
    "owner": {
        "en": 'Owner',
        "ja": '所有者'
    }
}

BREAKDOWN_PANEL_METRIC_TITLE = {
    'delivery_status': {
        'en': 'Status',
        'ja': 'ステータス',
    },
    'item_id': {
        'en': 'Item ID',
        'ja': '商品ID',
    },
    'status': {
        'en': 'Status',
        'ja': 'ステータス',
    },
    'warehouse_id': {
        'en': 'Location ID',
        'ja': 'ロケーションID',
    },
    'inventory_id': {
        'en': 'Inventory ID',
        'ja': '在庫ID'
    },
    'customers': {
        'en': 'Customer',
        'ja': '顧客',
    },
    'job_applicant_status': {
        'en': 'Applicant Status',
        'ja': '応募者のステータス'
    }
}

NUMBER_FORMAT = [
    ('number', 'Number'),
    ('%', '%'),

    # Convert 3 tuples from currency_model to 2 tuples (Follow django choice rule))
    *[(currency[0].lower(), currency[0].upper()) for currency in CURRENCY_MODEL]
]

PANEL_SOURCE_TITLE = {
    'funnel_chart': {
        'orders':           {'en': 'Orders',       'ja': '受注'},
        'cases':            {'en': 'Cases',        'ja': '案件'}
    },
    'chart': {
        'orders':           {'en': 'Orders',       'ja': '受注'},
        'subscriptions':    {'en': 'Subscriptions', 'ja': 'サブスクリプション'},
        'invoices':         {'en': 'Invoices',      'ja': '売上請求'},
        'estimates':        {'en': 'Estimates',     'ja': '見積'},
        'inventory':        {'en': 'Inventory',        'ja': '在庫'},
        'contacts':         {'en': 'Contacts',     'ja': '連絡先'},
        'deals':            {'en': 'Cases',        'ja': '案件'},
        'companies':        {'en': 'Companies',    'ja': '企業'},
        'events':           {'en': 'Session Events',  'ja': 'セッションイベント'},
    },
    'bar': {
        'orders':           {'en': 'Orders',        'ja': '受注'},
        'subscriptions':    {'en': 'Subscriptions', 'ja': 'サブスクリプション'},
        'invoices':         {'en': 'Invoices',      'ja': '売上請求'},
        'inventory':        {'en': 'Inventory',        'ja': '在庫'},
        'contacts':         {'en': 'Contacts',     'ja': '連絡先'},
        'companies':        {'en': 'Companies',    'ja': '企業'},
    },
    'stacked_chart': {
        'orders':           {'en': 'Orders',       'ja': '受注'},
        'subscriptions':    {'en': 'Subscriptions', 'ja': 'サブスクリプション'},
        # 'contacts':         {'en': 'Contacts',     'ja': '連絡先'},
        # 'deals':            {'en': 'Cases',        'ja': '案件'},
        # 'companies':        {'en': 'Companies',    'ja': '企業'},
        # 'events':           {'en': 'Session Events',  'ja': 'セッションイベント'},
    },
    'forecast': {
        'orders':           {'en': 'Orders',       'ja': '受注'},
        'subscriptions':    {'en': 'Subscriptions', 'ja': 'サブスクリプション'},
        'contacts':         {'en': 'Contacts',     'ja': '連絡先'},
        'deals':            {'en': 'Cases',        'ja': '案件'},
        'companies':        {'en': 'Companies',    'ja': '企業'},
        'events':           {'en': 'Session Events',  'ja': 'セッションイベント'},
    },

    'summary_table': {
        'orders':           {'en': 'Orders',       'ja': '受注'},
        'subscriptions':    {'en': 'Subscriptions', 'ja': 'サブスクリプション'},
        'contacts':         {'en': 'Contacts',     'ja': '連絡先'},
        'deals':            {'en': 'Cases',        'ja': '案件'},
        'companies':        {'en': 'Companies',    'ja': '企業'},
        'events':           {'en': 'Session Events',  'ja': 'セッションイベント'},
        'job':              {'en': 'Job',          'ja': '求人'},
        'sql':              {'en': 'SQL',           'ja': 'SQL'}
    },
    'table': {
        x: {'en': REPORT_OBJECT_GROUP_TYPE[x]['en'], 'ja': REPORT_OBJECT_GROUP_TYPE[x]['ja']} for x in TYPE_OBJECTS if x not in [TYPE_OBJECT_PANEL, TYPE_OBJECT_DASHBOARD]
    },
    'pivot_table': {
        x: {'en': REPORT_OBJECT_GROUP_TYPE[x]['en'], 'ja': REPORT_OBJECT_GROUP_TYPE[x]['ja']} for x in TYPE_OBJECTS + [TYPE_OBJECT_CASE_LINE_ITEM] if x not in [TYPE_OBJECT_PANEL, TYPE_OBJECT_DASHBOARD]
    },
    'sheet': {
        'inventory':    {'en': 'Inventory',    'ja': '在庫'},
        'order':        {'en': 'Order',        'ja': '受注'},
        'input':        {'en': 'Free Input',   'ja': '自由入力'},
    },
    'cohort_chart': {
        'orders':           {'en': 'Orders',        'ja': '受注'},
        'invoices':         {'en': 'Invoices',      'ja': '売上請求'},
    },
}

METRIC_DISPLAY = {
    'orders': {
        'en': 'Orders',
        'ja': '受注'
    },
    'items': {
        'en': 'Items',
        'ja': '商品'
    },
    'contacts': {
        'en': 'Contacts',
        'ja': '連絡先'
    },
    'deals': {
        'en': 'Cases',
        'ja': '案件'
    },
    'purchase_orders': {
        'en': 'Purchase Orders',
        'ja': '発注'
    },
    'invoices': {
        'en': 'Invoices',
        'ja': '売上請求'
    },
    'estimates': {
        'en': 'Estimates',
        'ja': '見積'
    },
    'events': {
        'en': 'Session Events',
        'ja': 'セッションイベント'
    },
}

CUSTOM_OBJECT_COLUMN_DISPLAY = {
    "row_id": {
        "en": "ID",
        "ja": "ID"
    },
    "usage_status": {
        "en": "Usage Status",
        "ja": "利用ステータス"
    },
    "created_at": {
        "en": "Created At",
        "ja": "作成日時"
    },
    "updated_at": {
        "en": "Updated At",
        "ja": "更新日時"
    },
    "platform_id": {
        "en": "Platform ID",
        "ja": "プラットフォームID"
    },
}

LINE_ITEM_DEFAULT = {
    'item.no': {
        'en': 'Item No',
        'ja': '商品番号',
    },
    'item.id': {
        'en': 'Item Id',
        'ja': '商品ID',
    },
    'item.name': {
        'en': 'Item Name',
        'ja': '商品名',
    },
    'item.description': {
        'en': 'Item Description',
        'ja': '商品説明',
    },
    'item.quantity': {
        'en': 'Quantity',
        'ja': '数量',
    },
    'item.unit_price': {
        'en': 'Unit Price',
        'ja': '単価',
    },
    'item.tax': {
        'en': 'Tax',
        'ja': '税率',
    },
    'item.total': {
        'en': 'Total',
        'ja': '合計',
    },
    'item.discount_rate': {
        'en': 'Discount Rate',
        'ja': '割引率',
    },
    'item.base_price': {
        'en': 'Item Sales Price',
        'ja': '商品販売価格',
    },
    'item.inventory': {
        'en': 'Inventory',
        'ja': '在庫',
    },
    'item_inventory_locations': {
        'en': 'Locations',
        'ja': 'ロケーション',
    },
    'item_inventory_amount': {
        'en': 'Inventory Amount',
        'ja': '在庫量',
    },
    'item.inventory_amount_location': {
        'en': 'Inventory Amount per Location',
        'ja': 'ロケーションごとの在庫数'
    }
}

ZENGIN_HEADER_SPEC = [1, 2, 1, 10, 40, 4, 4, 15, 3, 15, 1, 7, 17]  # 120
ZENGIN_MAIN_SPEC = [1, 4, 15, 3, 15, 4, 1,
                    7, 30, 10, 1, 10, 10, 1, 1, 7]  # 120
ZENGIN_FOOTER_1_SPEC = [1, 6, 12, 101]  # 120
ZENGIN_FOOTER_2_SPEC = [1, 119]  # 120
# 1  2  3  4  5  6  7  8  9 10 11 12 13 14 15 16   === # ===  0 as str, 1 as number
ZENGIN_SPEC_FORMAT_DATA = [1, 1, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0,
                           1, 1, 0, 1, 0, 0, 1, 1, 0, 1, 1, 1, 1, 0, 0, 0,
                           1, 1, 1, 0,
                           1, 0]

INVENTORY_STATUS_CHOICES = [
    ('available', 'Available'),
    ('committed', 'Committed'),
    ('unavailable', 'Unavailable')
]

INVENTORY_STATUS_DISPLAY = {
    'available': {
        'en': 'Available',
        'ja': '販売可能'
    },
    'committed': {
        'en': 'Committed',
        'ja': '確定済み'
    },
    'unavailable': {
        'en': 'Unavailable',
        'ja': '利用不可'
    },
}

WORKER_REVIEW_COLUMNS_DISPLAY = {
    "id_worker_review": {
        "en": "ID",
        "ja": "ID"
    },
    "usage_status": {
        "en": "Usage Status",
        "ja": "利用ステータス"
    },
    "review": {
        "en": "Review",
        "ja": "レビュー"
    },
    "score": {
        "en": "Score",
        "ja": "スコア"
    },
    "whats_going_well": {
        "en": "Whats Going Well",
        "ja": "何がうまくいっているのか」"
    },
    "areas_of_improvements": {
        "en": "Areas of Improvements",
        "ja": "改善の領域"
    },
    "status": {
        "en": "Status",
        "ja": "ステータス"
    }
}

ABSENCE_COLUMNS_DISPLAY = {
    "id_absence": {
        "en": "ID",
        "ja": "ID"
    },
    "start_date": {
        "en": "Start Date",
        "ja": "開始日"
    },
    "end_date": {
        "en": "End Date",
        "ja": "終了日"
    },
    "absence_type": {
        "en": "Leave Type",
        "ja": "休暇タイプ"
    },
    "note": {
        "en": "Note",
        "ja": "注記"
    },
    "status": {
        "en": "Status",
        "ja": "ステータス"
    },
    "usage_status": {
        "en": "Usage Status",
        "ja": "利用ステータス"
    },
    "requested_by": {
        "en": "Requester",
        "ja": "申請者"
    },
    'approved_by': {
        "en": "Approver",
        "ja": "承認者"
    }
}

TIMEGENIE_COLUMNS_DISPLAY = {
    "track_id": {
        "en": "Attendance ID",
        "ja": "勤怠ID"
    },
    "name": {
        "en": "Name",
        "ja": "名前"
    },
    "worker": {
        "en": "Employee",
        "ja": "従業員"
    },
    "user": {
        "en": "User",
        "ja": "ユーザー"
    },
    "submitted_by": {
        "en": "Submitter",
        "ja": "提出者"
    },
    "start_time": {
        "en": "Start Time",
        "ja": "開始時間"
    },
    "end_time": {
        "en": "End Time",
        "ja": "終了時間"
    },
    "status": {
        "en": "Track Status",
        "ja": "トラックステータス"
    },
    "usage_status": {
        "en": "Usage Status",
        "ja": "利用ステータス"
    },
    "duration": {
        "en": "Work Time",
        "ja": "勤務時間"
    },
    "hours": {
        "en": "Hours",
        "ja": "時間"
    },
    "minutes": {
        "en": "Minutes",
        "ja": "分"
    },
    "seconds": {
        "en": "Seconds",
        "ja": "秒"
    },
}

ABSENCE_TYPE = [
    ('sick', 'Sick Leave'),
    ('personal', 'Personal Leave'),
    ('pto', 'Paid Time Out'),
]
ABSENCE_STATUS = [
    ('draft', 'Draft'),
    ('requested', 'Requested'),
    ('approved', 'Approved'),
    ('declined', 'Declined'),
]
WORKER_STATUS = [
    ('in_employment', 'In Employment'),
    ('resigned', 'Resigned'),
    ('terminated', 'Terminated'),
    ('retired', 'Retired'),
    ('on_leave', 'On Leave'),
    ('transferred', 'Transferred'),
    ('contract_ended', 'Contract Ended'),
]


JOBS_COLUMNS_DISPLAY = {
    "job_id": {
        "en": "Job ID",
        "ja": "求人ID"
    },
    "title": {
        "en": "Title",
        "ja": "タイトル"
    },
    "status": {
        "en": "Status",
        "ja": "ステータス"
    },
    "job_type": {
        "en": "Type",
        "ja": "タイプ"
    },
    "preview": {
        "en": "Preview",
        "ja": "プレビュー"
    },
    "description": {
        "en": "Description",
        "ja": "説明"
    },
    "applications": {
        "en": "Applications",
        "ja": "応募者"
    },
    "interview": {
        "en": "Interviews",
        "ja": "面接"
    },
    "scorecard": {
        "en": "Scorecards",
        "ja": "スコアカード"
    },
    "location": {
        "en": "Work Location",
        "ja": "勤務地"
    },
    "usage_status": {
        "en": "Usage Status",
        "ja": "利用ステータス"
    },
    "created_at": {
        "en": "Created At",
        "ja": "作成日"
    }
}

JOBS_STATUS_DISPLAY = {
    "hc_approval": {
        "en": "Headcount Approval",
        "ja": "求人応募承認"
    },
    "job_requisition": {
        "en": "Job Requisition",
        "ja": "求人情報作成"
    },
    "candidate_sourcing": {
        "en": "Candidate Sourcing",
        "ja": "求職者募集"
    },
    "interview_scheduling": {
        "en": "Interview Scheduling",
        "ja": "面接調整"
    },
    "job_offer": {
        "en": "Job Offer",
        "ja": "内定"
    },
    "onboarding": {
        "en": "Onboarding",
        "ja": "入社手続き"
    },
    "job_closing": {
        "en": "Job Closing",
        "ja": "求人終了"
    },
}

JOBS_TYPE_DISPLAY = {
    "full_time": {
        "en": "Full Time Employee",
        "ja": "正社員"
    },
    "contractor": {
        "en": "Contractor",
        "ja": "契約社員"
    },
    "part_time": {
        "en": "Part Time",
        "ja": "パートタイム"
    },
    "intern": {
        "en": "Intern",
        "ja": "インターン"
    },
}

APPLICANT_DISPLAY_STATUS = {
    "listed": {
        "en": "Registered",
        "ja": "登録済み"
    },
    "contacted": {
        "en": "Contacted",
        "ja": "連絡済み"
    },
    "interview": {
        "en": "Interview",
        "ja": "面接"
    },
    "onboard": {
        "en": "Onboard",
        "ja": "入社手続き"
    },
    "all": {
        "en": "Total",
        "ja": "合計"
    }
}

APPLICANT_COLUMN_DISPLAY = {
    "name": {
        "en": "Name",
        "ja": "名前"
    },
    "email": {
        "en": "Email",
        "ja": "メール"
    },
    "status": {
        "en": "Status",
        "ja": "状態"
    },
    "current_company": {
        "en": "Current Company",
        "ja": "現在の会社"
    },
    "resume": {
        "en": "Resume",
        "ja": "再開する"
    },
    "phone_number": {
        "en": "Phone Number",
        "ja": "電話番号"
    },
    "url":{
        "en": "Url",
        "ja": "URL"
    },
    "created_at":{
        "en": "Applied at",
        "ja": "申請場所"
    }
}

JOBS_COLUMNS_DISPLAY = JOBS_COLUMNS_DISPLAY | JOBS_STATUS_DISPLAY | JOBS_TYPE_DISPLAY | APPLICANT_DISPLAY_STATUS



SYSTEM_SETTING_TYPE = {
    'basic': {
        'en': 'Workspace Settings',
        'ja': 'ワークスペース設定'
    },
    'user_management': {
        'en': 'User Manager',
        'ja': 'ユーザー管理'
    },
    'logs': {
        'en': "System Logs",
        'ja': "ワークスペースのログ"
    },
    'workspace_integrations': {
        'en': "Integrations",
        'ja': "連携サービス"
    },
    'workspace_billing': {
        'en': 'Plan & Billing',
        'ja': 'プラン & 請求管理'
    }
}