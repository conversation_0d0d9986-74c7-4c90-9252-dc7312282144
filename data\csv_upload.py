import ast
from io import String<PERSON>
import json
import traceback
import uuid

import chardet
from django.core.files.base import ContentFile
from django.core.files.uploadedfile import InMemoryUploadedFile, TemporaryUploadedFile
from django.core.management import call_command
from django.http.response import HttpResponse
from django.shortcuts import redirect, render
from django.utils import timezone
from django.views.decorators.http import require_POST
from django_hosts.resolvers import reverse
import pandas as pd

from data.constants.constant import TEMPLATE_FILE
from data.constants.properties_constant import (
    OBJECT_TYPE_TO_SLUG,
    TYPE_OBJECT_BILL,
    TYPE_OBJECT_CASE,
    TYPE_OBJECT_COMPANY,
    TYPE_OBJECT_CONTACT,
    TYPE_OBJECT_CUSTOM_OBJECT,
    TYPE_OBJECT_DELIVERY_NOTE,
    TYPE_OBJECT_ESTIMATE,
    TYPE_OBJECT_EXPENSE,
    TYPE_OBJECT_INVENTORY,
    TYPE_OBJECT_INVENTORY_TRANSACTION,
    TYPE_OBJECT_INVENTORY_WAREHOUSE,
    TYPE_OBJECT_INVOICE,
    TYPE_OBJECT_ITEM,
    TYPE_OBJECT_JOURNAL,
    TYPE_OBJECT_ORDER,
    TYPE_OBJECT_PURCHASE_ORDER,
    TYPE_OBJECT_RECEIPT,
    TYPE_OBJECT_SLIP,
    TYPE_OBJECT_SUBSCRIPTION,
)
from data.import_.validate_import import validate_inventory_import_keys
from data.models import (
    BackgroundJob,
    BillNameCustomField,
    CompanyNameCustomField,
    ContactsNameCustomField,
    CustomObject,
    CustomObjectPropertyName,
    DataTransfer,
    DealsNameCustomField,
    DeliverySlipNameCustomField,
    EstimateNameCustomField,
    ExpenseNameCustomField,
    ImportMappingFields,
    InventoryTransactionNameCustomField,
    InventoryWarehouseNameCustomField,
    InvoiceNameCustomField,
    JournalEntryNameCustomField,
    Module,
    Notification,
    PurchaseOrdersNameCustomField,
    ReceiptNameCustomField,
    ShopTurboInventoryNameCustomField,
    ShopTurboItemsNameCustomField,
    ShopTurboOrdersNameCustomField,
    ShopTurboSubscriptionsNameCustomField,
    SlipNameCustomField,
    TransferHistory,
)
from sanka.settings import AWS_LOCATION, AWS_STORAGE_BUCKET_NAME, LOCAL, S3_CLIENT
from utils.decorator import login_or_hubspot_required
from utils.discord import DiscordNotification
from utils.logic_app import trigger_bg_job
from utils.utility import get_workspace, is_valid_uuid

COMMERCE_COLUMNS_HEADER = [
    "start_date",
    "currency",
    "item",
    "item_price",
    "item_amount",
    "status",
    "tax_rate",
    "discount_rate",
    "discount_type",
    "discount_option",
    "tax_option",
    "tax_inclusive",
    "contact_id",
    "company_id",
    "owner",
]
OBJECT_CSV_CONFIG = {
    TYPE_OBJECT_COMPANY: {
        "columns": ["company_id", "name", "email", "url", "address", "phone_number"],
        "objnamecustomfields": CompanyNameCustomField,
    },
    "contact": {
        "columns": [
            "contact_id",
            "first_name",
            "last_name",
            "email",
            "phone_number",
            "image_url",
        ]
        + [f"associate#{TYPE_OBJECT_COMPANY}"],
        "objnamecustomfields": ContactsNameCustomField,
    },
    TYPE_OBJECT_CASE: {
        "columns": ["deal_id", "name", "contact_id", "company_id"],
        "objnamecustomfields": DealsNameCustomField,
    },
    "order": {
        "columns": [
            "order_id",
            "delivery_status",
            "currency",
            "price_of_items",
            "company_id",
            "contact_id",
            "item_id",
            "number_item",
            "total_price",
            "total_price_without_tax",
            "created_at",
            "order_at",
        ],
        "objnamecustomfields": ShopTurboOrdersNameCustomField,
    },
    "item": {
        "columns": [
            "item_id",
            "name",
            "description",
            "currency",
            "price_of_items",
            "tax_price_of_items",
            "purchase_price_currency",
            "purchase_price_of_items",
            "tax_purchase_price_of_items",
            "supplier",
        ],
        "objnamecustomfields": ShopTurboItemsNameCustomField,
    },
    TYPE_OBJECT_INVENTORY_WAREHOUSE: {
        "columns": [
            "id_iw",
            "warehouse",
            "floor",
            "zone",
            "aisle",
            "rack",
            "shelf",
            "bin",
        ],
        "objnamecustomfields": InventoryWarehouseNameCustomField,
    },
    TYPE_OBJECT_INVENTORY: {
        "columns": [
            "inventory_id",
            # "item_id",
            "inventory_status",
            "initial_value",
            "id_iw",
        ]+[
            f"associate#{TYPE_OBJECT_ITEM}",
        ],
        "objnamecustomfields": ShopTurboInventoryNameCustomField,
    },
    TYPE_OBJECT_INVENTORY_TRANSACTION: {
        "columns": [
            "inventory_id",
            "item_id",
            "transaction_type",
            "amount",
            "transaction_date",
        ],
        "objnamecustomfields": InventoryTransactionNameCustomField,
    },
    TYPE_OBJECT_BILL: {
        "columns": [
            "id_bill",
            "company_id",
            "contact_id",
            "title",
            "description",
            "currency",
            "amount",
            "due_date",
            "issued_date",
            "status",
        ],
        "objnamecustomfields": BillNameCustomField,
    },
    TYPE_OBJECT_SUBSCRIPTION: {
        "columns": [
            "subscriptions_id",
            "company_id",
            "contact_id",
            "start_durations",
            "end_durations",
            "currency",
            "price_of_items",
            "item_id",
            "number_item",
            "subscription_status",
            "frequency",
            "frequency_time",
            "created_at",
        ],
        "objnamecustomfields": ShopTurboSubscriptionsNameCustomField,
    },
    TYPE_OBJECT_ESTIMATE: {
        "columns": ["id_est"]
        + COMMERCE_COLUMNS_HEADER
        + [
            f"associate#{TYPE_OBJECT_ORDER}",
            f"associate#{TYPE_OBJECT_CASE}",
            f"associate#{TYPE_OBJECT_INVOICE}",
        ],
        "objnamecustomfields": EstimateNameCustomField,
    },
    TYPE_OBJECT_INVOICE: {
        "columns": ["id_inv"]
        + COMMERCE_COLUMNS_HEADER
        + ["due_date"]
        + [
            f"associate#{TYPE_OBJECT_ORDER}",
            f"associate#{TYPE_OBJECT_CASE}",
            f"associate#{TYPE_OBJECT_ESTIMATE}",
        ],
        "objnamecustomfields": InvoiceNameCustomField,
    },
    TYPE_OBJECT_DELIVERY_NOTE: {
        "columns": ["id_ds"] + COMMERCE_COLUMNS_HEADER,
        "objnamecustomfields": DeliverySlipNameCustomField,
    },
    TYPE_OBJECT_RECEIPT: {
        "columns": ["id_rcp"]
        + COMMERCE_COLUMNS_HEADER
        + ["billing_type", "manual_price"],
        "objnamecustomfields": ReceiptNameCustomField,
    },
    TYPE_OBJECT_SLIP: {
        "columns": ["id_slip"] + COMMERCE_COLUMNS_HEADER,
        "objnamecustomfields": SlipNameCustomField,
    },
    TYPE_OBJECT_CUSTOM_OBJECT: {
        "columns": ["row_id"],
        "objnamecustomfields": CustomObjectPropertyName,
    },
    TYPE_OBJECT_PURCHASE_ORDER: {
        "columns": [
            "id_po",
            "status",
            "currency",
            "item_id",
            "number_of_items",
            "price_of_items",
            "total_price",
            "total_price_without_tax",
            "date",
            "supplier_contact",
            "supplier_company",
        ],
        "objnamecustomfields": PurchaseOrdersNameCustomField,
    },
    TYPE_OBJECT_EXPENSE: {
        "columns": [
            "id_pm",
            "owner",
            "contact_id",
            "company_id",
            "status",
            "currency",
            "amount",
            "due_date",
            "reimburse_date",
        ],
        "objnamecustomfields": ExpenseNameCustomField,
    },
    TYPE_OBJECT_JOURNAL: {
        "columns": [
            "id_journal",
            "category",
            "amount",
            "currency",
            "settle_journal",
            "amount_credit",
            "amount_debit",
            "amount_with_tax",
            "tax_rate",
            "amount_after_settlement",
            "notes",
            "transaction_date",
            "usage_status",
            "owner",
            "contact_id",
            "company_id",
        ],
        "objnamecustomfields": JournalEntryNameCustomField,
    },
}

OBJECT_TYPE_TO_IMPORT_TYPE = {
    TYPE_OBJECT_ITEM: "import_item",
    TYPE_OBJECT_ORDER: "import_order",
    TYPE_OBJECT_SUBSCRIPTION: "import_subscription",
    TYPE_OBJECT_INVENTORY: "import_inventory",
    TYPE_OBJECT_CONTACT: "import_contact",
    TYPE_OBJECT_COMPANY: "import_company",
    TYPE_OBJECT_BILL: "import_bill",
    TYPE_OBJECT_CASE: "import_case",
    TYPE_OBJECT_INVENTORY_TRANSACTION: "import_inventory_transaction",
    TYPE_OBJECT_INVENTORY_WAREHOUSE: "import_inventory_warehouse",
    TYPE_OBJECT_ESTIMATE: "import_" + TYPE_OBJECT_ESTIMATE,
    TYPE_OBJECT_INVOICE: "import_" + TYPE_OBJECT_INVOICE,
    TYPE_OBJECT_DELIVERY_NOTE: "import_" + TYPE_OBJECT_DELIVERY_NOTE,
    TYPE_OBJECT_RECEIPT: "import_" + TYPE_OBJECT_RECEIPT,
    TYPE_OBJECT_SLIP: "import_" + TYPE_OBJECT_SLIP,
    TYPE_OBJECT_PURCHASE_ORDER: "import_purchase_order",
    TYPE_OBJECT_EXPENSE: "import_" + TYPE_OBJECT_EXPENSE,
    TYPE_OBJECT_JOURNAL: "import_" + TYPE_OBJECT_JOURNAL,
}


def master_csv_upload(request):
    """
    Handles the CSV upload workflow, including file selection, column mapping, and property assignment for importing data into various object types.

    Depending on the request method and section, this view renders different steps of the CSV import process:
    - For GET requests, it displays pages for file upload, key field selection, property mapping, and object relation mapping, dynamically building context based on the selected object type and custom fields.
    - For POST requests with section 'mapping-file', it processes the uploaded CSV file, detects encoding, reads the file into a DataFrame, cleans the data, and prepares mapping data for the next step in the import process.

    Returns the appropriate rendered template for each step or an HTTP response in case of errors.
    """
    workspace = get_workspace(request.user)
    if request.method == "GET":
        section = request.GET.get("section", None)
        if section == "csv_upload":
            import_data_type = request.GET.get("import_data_type", None)
            import_data_type = import_data_type.split(",")
            context = {
                "import_data_type": import_data_type,
                "page_group_type": request.GET.get("page_group_type", None),
                "TEMPLATE_FILE": TEMPLATE_FILE,
            }
            return render(
                request, "data/common/csv_upload/csv-upload-file.html", context
            )

        elif section == "choose-key-field":
            import_data_type = request.GET.get("import_data_type", None)
            how_to_import = request.GET.get("how-to-import", None)
            mapping_storage_id = request.GET.get("mapping_storage", None)

            columns = []
            custom_object = CustomObject.objects.filter(slug=import_data_type).first()
            if custom_object:
                columns = OBJECT_CSV_CONFIG[TYPE_OBJECT_CUSTOM_OBJECT]["columns"].copy()
                namecustomfields = (
                    OBJECT_CSV_CONFIG[TYPE_OBJECT_CUSTOM_OBJECT]["objnamecustomfields"]
                    .objects.filter(
                        workspace=workspace, custom_object=custom_object, type="text"
                    )
                    .values_list("id", flat=True)
                )
                for namecustomfield in namecustomfields:
                    columns.append(str(namecustomfield))
            else:
                columns = OBJECT_CSV_CONFIG[import_data_type]["columns"].copy()
                custome_field_ids = (
                    OBJECT_CSV_CONFIG[import_data_type]["objnamecustomfields"]
                    .objects.filter(workspace=workspace, type__in=["text", "formula"])
                    .values_list("id", flat=True)
                )
                for custome_field_id in custome_field_ids:
                    columns.append(str(custome_field_id))
            context = {
                "import_data_type": import_data_type,
                "columns": columns,
                "how_to_import": how_to_import,
            }
            if custom_object:
                context["custom_object"] = custom_object

            mapping_storage = ImportMappingFields.objects.filter(
                id=mapping_storage_id
            ).first()
            if mapping_storage:
                context["mapping_storage"] = mapping_storage

            return render(request, "data/common/csv_upload/csv-key-field.html", context)

        elif section == "sanka-field":
            import_as = request.GET.get("import-as", None)
            header = request.GET.get("header", None)
            mapping_storage_id = request.GET.get("mapping_storage", None)
            mapping_sanka_property_storage = request.GET.get(
                "mapping_sanka_property_storage", None
            )

            columns = []
            namecustomfields = []
            custom_object = CustomObject.objects.filter(slug=import_as).first()
            if custom_object:
                columns = OBJECT_CSV_CONFIG[TYPE_OBJECT_CUSTOM_OBJECT]["columns"].copy()
                namecustomfields = (
                    OBJECT_CSV_CONFIG[TYPE_OBJECT_CUSTOM_OBJECT]["objnamecustomfields"]
                    .objects.filter(workspace=workspace, custom_object=custom_object)
                    .exclude(type__in=["image", "user", "formula"])
                    .distinct("order", "id")
                    .order_by("order", "id")
                    .values("id", "type")
                )
            else:
                columns = OBJECT_CSV_CONFIG[import_as]["columns"].copy()
                custom_prop_model = OBJECT_CSV_CONFIG[import_as]["objnamecustomfields"]
                namecustomfields = (
                    custom_prop_model.objects.filter(workspace=workspace)
                    .exclude(type__in=["image", "user"])
                    .distinct("order", "id")
                    .order_by("order", "id")
                    .values("id", "type")
                )

            component_props = []
            for namecustomfield in namecustomfields:
                if namecustomfield["type"] == "components":
                    columns.append(str(namecustomfield["id"]))
                    columns.append(str(namecustomfield["id"]) + "|amount")
                    component_props.append(str(namecustomfield["id"]))
                    component_props.append(str(namecustomfield["id"]) + "|amount")
                elif namecustomfield["type"] == "hierarchy" and import_as == "company":
                    custom_object = custom_prop_model.objects.filter(
                        id=namecustomfield["id"]
                    ).first()
                    if custom_object:
                        fields_hierarchy = ast.literal_eval(custom_object.choice_value)
                        for f_ in fields_hierarchy:
                            columns.append(
                                str(namecustomfield["id"])
                                + "|hierarchy"
                                + f"|{f_['label']}"
                                + f"|{f_['value']}"
                            )
                else:
                    columns.append(str(namecustomfield["id"]))
            context = {
                "import_as": import_as,
                "header": header,
                "columns": columns,
                "mapping_sanka_property_storage": mapping_sanka_property_storage,
                "component_props": component_props,
                "mapping_storage_id": mapping_storage_id,
            }
            if custom_object:
                context["custom_object"] = custom_object
            return render(
                request,
                "data/common/csv_upload/csv-import-sanka-properties.html",
                context,
            )

        elif section == "sanka-property-object-relation":
            sanka_properties = request.GET.getlist("sanka-properties", [])
            import_data_type = request.GET.get("import_data_type", None)
            mapping_storage_id = request.GET.get("mapping_storage_id", None)
            page_group_type = request.GET.get("page_group_type", None)

            relation_prop = ""
            columns = []
            mapping_storage = None
            if mapping_storage_id:
                mapping_storage = ImportMappingFields.objects.filter(
                    id=mapping_storage_id
                ).first()
                print("== import_data_type: ",import_data_type)
                if import_data_type in [
                    TYPE_OBJECT_ORDER,
                    TYPE_OBJECT_INVENTORY,
                    TYPE_OBJECT_BILL,
                    TYPE_OBJECT_SUBSCRIPTION,
                    TYPE_OBJECT_PURCHASE_ORDER,
                    TYPE_OBJECT_CASE,
                    TYPE_OBJECT_EXPENSE,
                    TYPE_OBJECT_JOURNAL,
                ]:
                    custom_prop_model = None
                    if "item_id" in sanka_properties:
                        relation_prop = "item_id"
                        columns.append("item_id")
                        custom_prop_model = OBJECT_CSV_CONFIG["item"][
                            "objnamecustomfields"
                        ]
                    elif f"associate#{TYPE_OBJECT_ITEM}" in sanka_properties:
                        relation_prop = f"associate#{TYPE_OBJECT_ITEM}"
                        columns.append(f"associate#{TYPE_OBJECT_ITEM}")
                        custom_prop_model = OBJECT_CSV_CONFIG["item"][
                            "objnamecustomfields"
                        ]

                    elif "contact_id" in sanka_properties:
                        relation_prop = "contact_id"
                        columns.append("contact_id")
                        custom_prop_model = OBJECT_CSV_CONFIG["contact"][
                            "objnamecustomfields"
                        ]

                    elif "company_id" in sanka_properties:
                        relation_prop = "company_id"
                        columns.append("company_id")
                        custom_prop_model = OBJECT_CSV_CONFIG["company"][
                            "objnamecustomfields"
                        ]
                    else:
                        columns = []

                    if custom_prop_model and mapping_storage:
                        namecustomfields = (
                            custom_prop_model.objects.filter(
                                workspace=workspace, type__in=["text"]
                            )
                            .distinct("order", "id")
                            .order_by("order", "id")
                            .values_list("id", flat=True)
                        )
                        for namecustomfield in namecustomfields:
                            columns.append(str(namecustomfield))

                        print("== mapping_storage.input_pair: ",mapping_storage.input_pair)
                        if mapping_storage.input_pair:
                            sublist = []
                            for sublist in mapping_storage.input_pair.values():
                                if (
                                    "item_id" in sublist
                                    or f"associate#{TYPE_OBJECT_ITEM}" in sublist
                                    or "contact_id" in sublist
                                    or "company_id" in sublist
                                ):
                                    break
                            if sublist:
                                if sublist[-1] == "True":
                                    columns = []

            context = {
                "relation_prop": relation_prop,
                "columns": columns,
                "import_data_type": import_data_type,
                "mapping_storage": mapping_storage,
                "page_group_type": page_group_type,
            }
            return render(
                request,
                "data/common/csv_upload/csv-key-object-relation-field.html",
                context,
            )

        else:
            page_group_type = request.GET.get("page_group_type", None)
            custom_object_id = request.GET.get("custom_object_id", None)
            mapping_storage = ImportMappingFields.objects.filter(
                workspace=workspace, object_type=page_group_type
            ).first()
            if not mapping_storage:
                mapping_storage, _ = ImportMappingFields.objects.get_or_create(
                    workspace=workspace, object_type=page_group_type
                )

            context = {
                "mapping_storage": mapping_storage,
                "page_group_type": request.GET.get("page_group_type", None),
            }
            if custom_object_id:
                custom_object = CustomObject.objects.filter(id=custom_object_id).first()
                context["custom_object"] = custom_object
            return render(
                request, "data/common/csv_upload/csv-import-data.html", context
            )
    else:
        lang = request.LANGUAGE_CODE
        print(request.POST)
        section = request.POST.get("section", None)
        if section == "mapping-file":
            csv_ = request.FILES.get("csv_file", False)
            import_data_types = request.POST.get("import_data_type", None)
            page_group_type = request.POST.get("page_group_type")

            custom_object = CustomObject.objects.filter(slug=page_group_type).first()
            # try:
            if not csv_:
                Notification.objects.create(
                    workspace=get_workspace(request.user),
                    user=request.user,
                    message="Failed to retrieve the CSV file.",
                    type="error",
                )
                module_object_slug = OBJECT_TYPE_TO_SLUG[page_group_type]
                module = (
                    Module.objects.filter(
                        workspace=workspace, object_values__contains=page_group_type
                    )
                    .order_by("order", "created_at")
                    .first()
                )
                if module:
                    module_slug = module.slug
                    return redirect(
                        reverse(
                            "load_object_page",
                            host="app",
                            kwargs={
                                "module_slug": module_slug,
                                "object_slug": module_object_slug,
                            },
                        )
                    )
                return redirect(reverse("main", host="app"))
                # Validate file extension is .csv

            if not csv_.name.lower().endswith(".csv"):
                msg = f"We can not process your {csv_.name} file. Please upload a CSV file."
                if lang == "ja":
                    msg = f"{csv_.name} ファイルを処理できません。CSV ファイルを使用してください。"
                return HttpResponse(msg)

            with csv_.open() as f:
                # checking encoding
                read_data = f.read()
                decoded_content = None

                # Try to detect encoding
                try:
                    result = chardet.detect(read_data)
                    encoding = result["encoding"] if result else None
                except Exception as e:
                    print(f"Encoding detection failed: {e}")
                    encoding = None

                # Try decoding with detected encoding first
                if encoding:
                    try:
                        if encoding.lower() == "shift_jis":
                            decoded_content = read_data.decode("shift_jis")
                        else:
                            decoded_content = read_data.decode(encoding)
                    except UnicodeDecodeError as e:
                        print(
                            f"Decoding with detected encoding '{encoding}' failed: {e}"
                        )
                        decoded_content = None

                # Fallback to UTF-8 if detected encoding failed or wasn't detected
                if decoded_content is None:
                    try:
                        decoded_content = read_data.decode("utf-8")
                    except UnicodeDecodeError as e:
                        print(f"UTF-8 decoding failed: {e}")
                        # Final fallback to shift_jis with error handling
                        try:
                            decoded_content = read_data.decode(
                                "shift_jis", errors="ignore"
                            )
                        except Exception as e:
                            print(f"Shift_JIS fallback failed: {e}")
                            # Last resort: use latin-1 which can decode any byte sequence
                            decoded_content = read_data.decode(
                                "latin-1", errors="replace"
                            )

                # Create DataFrame from decoded content
                string_data = StringIO(decoded_content)
                df = pd.read_csv(string_data, sep=",", dtype=str)

                df = df.loc[:, ~df.columns.isna()]
                df = df.loc[:, df.columns.notna()]
                df = df.loc[:, ~df.columns.str.match(r"Unnamed: \d+")]
                df = df.dropna(axis=0, how="all")
                df = df.reset_index(drop=True)

            num_rows = len(df)
            header_list = df.columns.tolist()
            import_data_types = ast.literal_eval(import_data_types)
            mapping_data = {"header_list": header_list}
            for import_data_type in import_data_types:
                columns = []
                namecustomfield = []
                if custom_object:
                    custom_prop_model = OBJECT_CSV_CONFIG[TYPE_OBJECT_CUSTOM_OBJECT][
                        "objnamecustomfields"
                    ]
                    namecustomfields = (
                        custom_prop_model.objects.filter(
                            workspace=workspace, custom_object=custom_object
                        )
                        .exclude(type__in=["image", "user", "formula"])
                        .distinct("order", "id")
                        .order_by("order", "id")
                        .values_list("id", flat=True)
                    )
                    namecustomfield = [
                        str(namecustomfield) for namecustomfield in namecustomfields
                    ]
                else:
                    columns = OBJECT_CSV_CONFIG[import_data_type]["columns"].copy()
                    custom_prop_model = OBJECT_CSV_CONFIG[import_data_type][
                        "objnamecustomfields"
                    ]
                    namecustomfields = (
                        custom_prop_model.objects.filter(workspace=workspace)
                        .exclude(type__in=["image", "user"])
                        .distinct("order", "id")
                        .order_by("order", "id")
                        .values_list("id", flat=True)
                    )
                    namecustomfield = [
                        str(namecustomfield) for namecustomfield in namecustomfields
                    ]
                columns.extend(namecustomfield)

                mapping_data[import_data_type] = {"column": columns}

            text_custom_props = []
            if "item" in import_data_types:
                custom_prop_model = OBJECT_CSV_CONFIG[import_data_type][
                    "objnamecustomfields"
                ]
                text_custom_props = (
                    custom_prop_model.objects.filter(
                        workspace=workspace, type__in=["text"]
                    )
                    .distinct("order", "id")
                    .order_by("order", "id")
                    .values_list("id", flat=True)
                )
            elif "inventory" in import_data_types:
                custom_prop_model = OBJECT_CSV_CONFIG["item"]["objnamecustomfields"]
                text_custom_props = (
                    custom_prop_model.objects.filter(
                        workspace=workspace, type__in=["text"]
                    )
                    .distinct("order", "id")
                    .order_by("order", "id")
                    .values_list("id", flat=True)
                )

            mapping_storage = ImportMappingFields.objects.filter(
                workspace=workspace, object_type=page_group_type
            ).first()

            context = {
                "mapping_data": mapping_data,
                "import_data_types": import_data_types,
                "mapping_storage": mapping_storage,
                "num_rows": num_rows,
                "page_group_type": request.POST.get("page_group_type", None),
                "text_custom_props": text_custom_props,
                "module": request.POST.get("module", None),
            }
            if custom_object:
                context["custom_object"] = custom_object

            # if object_type == TYPE_OBJECT_JOURNAL:
            #     # Adding Partner category
            #     if 'partner' in base_columns:
            #         base_columns.remove('partner')
            #     if 'settle_choice' in base_columns:
            #         base_columns.remove('settle_choice')
            #     sanka_columns = base_columns + ["partner_company", "partner_contact",
            #                                     "settlement_date", "settlement_amount", "settlement_account"] + custom_fields
            return render(
                request, "data/common/csv_upload/csv-import-mapping.html", context
            )
            # except Exception as e:
            #     print("eh: ", e)
            #     if request.LANGUAGE_CODE == 'ja':
            #         return HttpResponse('CSV の読み込みに失敗しました。')
            #     else:
            #         return HttpResponse('Failed to load CSV.')
    return HttpResponse(200)


def variable_stored(request):
    workspace = get_workspace(request.user)
    page_group_type = request.POST.get("page_group_type", None)
    file_columns = request.POST.getlist("file-column", [])
    import_as = request.POST.getlist("import-as", [])
    sanka_properties = request.POST.getlist("sanka-properties", [])
    ignores = request.POST.getlist("ignore", [])

    how_to_import = request.POST.get("how-to-import", None)
    update_key_field = request.POST.get("key-field", None)

    property_object_relation_sanka = request.POST.getlist(
        "property-object-relation-sanka-prop", []
    )
    property_object_relation_key = request.POST.getlist(
        "property-object-relation-key", []
    )

    property_relation_key = dict(
        zip(property_object_relation_sanka, property_object_relation_key)
    )

    mapping_storage, _ = ImportMappingFields.objects.get_or_create(
        workspace=workspace, object_type=page_group_type
    )
    mapping_storage.input_pair = {
        file_col: [imprt_as, sanka_prop, ignore]
        for sanka_prop, imprt_as, file_col, ignore in zip(
            sanka_properties, import_as, file_columns, ignores
        )
    }

    mapping_storage.entry_method = [how_to_import]
    mapping_storage.key_field = [update_key_field]
    mapping_storage.property_object_relation_key_field = property_relation_key

    mapping_storage.save()

    #  Create a response with success status
    response = HttpResponse(status=200)

    # Add the toast notification trigger header
    response["HX-Trigger-Toast"] = "BE-STATUS"

    return response


@login_or_hubspot_required
@require_POST
def csv_upload_submit(request):
    """
    Handles the final submission of a CSV file for import, saving mapping configurations and triggering data import.

    Validates the presence of required parameters and the uploaded CSV file, saves import mapping and file to storage, and creates a transfer history record. Depending on the number of rows, either triggers an asynchronous background job for large imports or processes the import synchronously. Notifies the user of success or errors and redirects to the appropriate page.
    """
    module_slug = None
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE
    csv_ = request.FILES.get("csv_file", False)
    # module_slug = request.POST.get('module',None)
    page_group_type = request.POST.get("page_group_type", None)
    custom_object_id = request.POST.get("custom_object_id", None)
    module_object_slug = None
    module_slug = None
    print(request.POST)
    if not custom_object_id and not page_group_type:
        if lang == "ja":
            return HttpResponse(status=400)
        else:
            return HttpResponse(status=400)

    custom_object = None
    if custom_object_id:
        custom_object = CustomObject.objects.filter(id=custom_object_id).first()
        module_object_slug = custom_object.slug
        module = (
            Module.objects.filter(
                workspace=workspace, object_values__contains=str(custom_object_id)
            )
            .order_by("order", "created_at")
            .first()
        )
        if module:
            module_slug = module.slug

    elif page_group_type:
        module_object_slug = OBJECT_TYPE_TO_SLUG[page_group_type]
        module = (
            Module.objects.filter(
                workspace=workspace, object_values__contains=page_group_type
            )
            .order_by("order", "created_at")
            .first()
        )
        if module:
            module_slug = module.slug

    if custom_object:
        history_type = f"import_{custom_object.slug}"
    elif page_group_type is not None:
        history_type = OBJECT_TYPE_TO_IMPORT_TYPE[page_group_type]
    else:
        history_type = "import_unknown"  # or some other default value

    import_data_types_str = request.POST.get("import_data_type", "")
    file_columns = request.POST.getlist("file-column", [])
    import_as = request.POST.getlist("import-as", [])
    sanka_properties = request.POST.getlist("sanka-properties", [])
    ignores = request.POST.getlist("ignore", [])
    how_to_import = request.POST.getlist("how-to-import", [])
    key_field = request.POST.getlist("key-field", [])
    custom_key_field = request.POST.get("component-key")
    property_object_relation_key_field = request.POST.getlist(
        "property-object-relation-key"
    )
    property_object_relation_sanka_prop = request.POST.getlist(
        "property-object-relation-sanka-prop"
    )
    print(request.POST)

    history = TransferHistory.objects.create(
        workspace=workspace,
        user=request.user,
        status="running",
        type=history_type,
        start_date=timezone.now(),
        how_to_import=how_to_import[0] if len(how_to_import) > 0 else None,
    )

    DataTransfer.objects.create(
        workspace=workspace, user=request.user, import_export_type="import"
    )

    is_valid = True
    err_msg = "Failed to import."
    if lang == "ja":
        err_msg = "CSVの読み込みに失敗しました。"
    df = None
    csv_copy = None
    if not csv_:
        is_valid = False
        err_msg = "Failed to import: Failed to retrieve the CSV file."
        if lang == "ja":
            err_msg = "CSVの読み込みに失敗しました。"
    else:
        encodings = ["utf-8", "shift_jis", "latin-1"]

        with csv_.open() as f:
            content = f.read()
            f.seek(0)  # Reset file pointer to start
            for encoding in encodings:
                try:
                    decoded_content = content.decode(encoding)
                    string_data = StringIO(decoded_content)
                    df = pd.read_csv(string_data, sep=",", encoding=encoding, dtype=str)
                    if isinstance(csv_, InMemoryUploadedFile):
                        # For InMemoryUploadedFile, use getvalue() method
                        csv_copy = ContentFile(csv_.file.getvalue())
                        csv_mapping_file = ContentFile(csv_.file.getvalue())
                    elif isinstance(csv_, TemporaryUploadedFile):
                        # For TemporaryUploadedFile, read the file content
                        file_content = content
                        csv_copy = ContentFile(file_content)
                        csv_mapping_file = ContentFile(file_content)
                    else:
                        # Fallback for other file types - try to read content
                        try:
                            file_content = content
                            csv_copy = ContentFile(file_content)
                            csv_mapping_file = ContentFile(file_content)
                        except (AttributeError, IOError):
                            # If all else fails, use the original file (this may still cause issues with S3)
                            csv_copy = csv_
                            csv_mapping_file = csv_

                    break  # Successfully read with this encoding
                except (UnicodeDecodeError, pd.errors.EmptyDataError):
                    continue
            else:
                # If we get here, all encodings failed
                is_valid = False
                err_msg = (
                    "インポートに失敗しました: CSV ファイルを読み取ることができませんでした。"
                    if lang == "ja"
                    else "Failed to import: Could not read CSV file."
                )
            f.seek(0)

        if df is not None and df.empty:
            is_valid = False
            err_msg = "Failed to import: CSV file is empty."
            if lang == "ja":
                err_msg = "CSVの読み込みに失敗しました。"

    if page_group_type == TYPE_OBJECT_INVENTORY and is_valid:
        is_valid, err_msg = validate_inventory_import_keys(
            lang, how_to_import[0], import_as, sanka_properties, ignores, key_field[0]
        )

    if not is_valid:
        history.status = "canceled"
        history.error_message = err_msg
        history.save()
        Notification.objects.create(
            workspace=get_workspace(request.user),
            user=request.user,
            message=err_msg,
            type="error",
        )
        if module_slug and module_object_slug:
            return redirect(
                reverse(
                    "load_object_page",
                    host="app",
                    kwargs={
                        "module_slug": module_slug,
                        "object_slug": module_object_slug,
                    },
                )
            )
        return redirect(reverse("main", host="app"))

    import_data_types_list = (
        import_data_types_str.split(",") if import_data_types_str else []
    )

    num_types = len(import_data_types_list)

    processed_how_to_import = []
    if how_to_import:  # Check if the list is not empty
        if num_types > 0:
            if len(how_to_import) == 1 and num_types > 1:
                # Broadcast single value
                processed_how_to_import = [how_to_import[0]] * num_types
            elif len(how_to_import) == num_types:
                processed_how_to_import = how_to_import  # Lengths match
            else:
                # Mismatch that isn't a 1-to-N broadcast. This indicates a potential client-side data issue.
                # For now, pass the original list, which might still lead to errors downstream if not handled,
                # but the primary fix is for the 1-to-N case.
                processed_how_to_import = how_to_import
        else:  # No import_data_types, so how_to_import should ideally be empty or is irrelevant
            # Or keep as original `how_to_import` if that makes more sense for num_types = 0
            processed_how_to_import = []
    # If how_to_import was empty, processed_how_to_import remains empty

    processed_key_field = []
    if key_field:  # Check if the list is not empty
        if num_types > 0:
            if len(key_field) == 1 and num_types > 1:
                processed_key_field = [
                    key_field[0]
                ] * num_types  # Broadcast single value
            elif len(key_field) == num_types:
                processed_key_field = key_field  # Lengths match
            else:
                processed_key_field = key_field  # Mismatch, pass original
        else:  # No import_data_types
            processed_key_field = []  # Or keep as original `key_field`
    # If key_field was empty, processed_key_field remains empty

    if custom_key_field and (
        custom_key_field != "item_id" and not is_valid_uuid(custom_key_field)
    ):
        if lang == "ja":
            Notification.objects.create(
                workspace=workspace,
                user=request.user,
                message="無効または不正な入力です。",
                type="error",
            )
        else:
            Notification.objects.create(
                workspace=workspace,
                user=request.user,
                message="Invalid input or input was malformed.",
                type="error",
            )
        return redirect(
            reverse(
                "load_object_page",
                host="app",
                kwargs={"module_slug": module_slug, "object_slug": module_object_slug},
            )
        )

    num_rows = request.POST.get("num-rows", 0)
    # save mapping
    mapping_storage, _ = ImportMappingFields.objects.get_or_create(
        workspace=workspace, object_type=page_group_type
    )
    mapping_storage.import_data_type = import_data_types_list
    mapping_storage.entry_method = processed_how_to_import
    mapping_storage.key_field = processed_key_field
    mapping_storage.custom_key_field = custom_key_field
    mapping_storage.property_object_relation_key_field = {
        sanka_prop: relation_key
        for sanka_prop, relation_key in zip(
            property_object_relation_sanka_prop, property_object_relation_key_field
        )
    }

    # mapping_storage.file_column = file_columns
    mapping_storage.input_pair = {
        file_col: [imprt_as, sanka_prop, ignore]
        for sanka_prop, imprt_as, file_col, ignore in zip(
            sanka_properties, import_as, file_columns, ignores
        )
    }
    file_ = mapping_storage.file
    mapping_storage.file = None

    try:
        if file_ and "nyc3.digitaloceanspaces.com" in file_.url and not LOCAL:
            file_name = file_.url.split("/")[-1]
            file_name = f"csv-entry-file/{file_name}"
            sc_file = file_
            S3_CLIENT.delete_object(
                Bucket=AWS_STORAGE_BUCKET_NAME, Key=f"{AWS_LOCATION}/{sc_file.file}"
            )
    except Exception as e:
        traceback.print_exc()
        print("Error at delete file: ", e)

    original_file_name = csv_.name
    csv_mapping_file.name = str(uuid.uuid4()) + "." + csv_.name.split(".")[-1]

    mapping_storage.file = csv_mapping_file
    mapping_storage.save()

    df = df.loc[:, ~df.columns.isna()]
    df = df.loc[:, df.columns.notna()]
    df = df.loc[:, ~df.columns.str.match(r"Unnamed: \d+")]
    df = df.dropna(axis=0, how="all")
    df = df.fillna("")

    # Replace NaN with None in object (string) columns
    df[df.select_dtypes(include="object").columns] = df.select_dtypes(
        include="object"
    ).where(df.notna(), None)

    df = df.reset_index(drop=True)

    # count total rows without column row
    history.total_number = num_rows = len(df)
    csv_copy.name = str(uuid.uuid4()) + "." + csv_.name.split(".")[-1]
    history.name = original_file_name
    history.file = csv_copy
    history.input_pair = mapping_storage.input_pair
    history.save()

    if num_rows:
        num_rows = int(num_rows)
    if num_rows != 0:
        if num_rows > 100:  # run bg jobs
            params = {
                "imf_id": str(mapping_storage.id),
                "user_id": str(request.user.id),
                "history_id": str(history.id),
                "workspace_id": str(workspace.id),
            }
            payload = {
                "function": "bulk_objects_entry",
                "args": [
                    "--{}={}".format(key.replace("-", "_"), value)
                    for key, value in params.items()
                ],
                "workspace_id": str(workspace.id),
                "username": request.user.username,
                "job_id": str(uuid.uuid4()),
            }
            params_dumps = json.dumps(payload)
            status = trigger_bg_job(params_dumps, transfer_history=history)

            if status.status_code > 202:
                dn = DiscordNotification()
                dn.send_message(
                    "[Trigger next action] - Send queue to run-action failed."
                )
                BackgroundJob.objects.create(
                    data_type="log",
                    name="Failed to add new queue to start bulk entry actions",
                    status="error",
                    data=payload,
                    workspace_id=payload["workspace_id"],
                    username=payload["username"],
                    job_id=payload["job_id"],
                )

                if lang == "ja":
                    Notification.objects.create(
                        workspace=workspace,
                        user=request.user,
                        message="アップロードのバックグラウンド ジョブの実行に失敗しました。",
                        type="error",
                    )
                else:
                    Notification.objects.create(
                        workspace=workspace,
                        user=request.user,
                        message="Failed to run upload background job.",
                        type="error",
                    )

            else:
                if lang == "ja":
                    Notification.objects.create(
                        workspace=workspace,
                        user=request.user,
                        message="CSVファイルは非同期で処理されます。数分お待ちください。",
                        type="success",
                    )
                else:
                    Notification.objects.create(
                        workspace=workspace,
                        user=request.user,
                        message="Your CSV file will be processed asynchronously. Please wait for couple minutes",
                        type="success",
                    )

        else:
            # Run the command
            call_command(
                "bulk_objects_entry",
                imf_id=str(mapping_storage.id),
                user_id=str(request.user.id),
                history_id=str(history.id),
                workspace_id=str(workspace.id),
            )

            if lang == "ja":
                Notification.objects.create(
                    workspace=workspace,
                    user=request.user,
                    message="CSVファイルのアップロードが完了しました",
                    type="success",
                )
            else:
                Notification.objects.create(
                    workspace=workspace,
                    user=request.user,
                    message="Upload CSV File Complete",
                    type="success",
                )

    if module_slug:
        return redirect(
            reverse(
                "load_object_page",
                host="app",
                kwargs={"module_slug": module_slug, "object_slug": module_object_slug},
            )
        )
    return redirect(reverse("main", host="app"))
