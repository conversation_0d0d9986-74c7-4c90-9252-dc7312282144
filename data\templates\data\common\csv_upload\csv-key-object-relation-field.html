{% load i18n %}
{% load hosts %}
{% load custom_tags %}
{% get_current_language as LANGUAGE_CODE %}

{% if columns %}

{% with property_object_relation_key_field=mapping_storage.property_object_relation_key_field|string_list_to_list %}
<div class="fv-rowd-flex flex-column relation-{{relation_prop}} mb-3">    
    <label class="{% include 'data/utility/form-label.html' %}">
        <span class="required">
            {% if relation_prop == 'item_id' or relation_prop == "associate#{{TYPE_OBJECT_ITEM}}" %}
                {% if LANGUAGE_CODE == 'ja'%}
                    商品プロパティを選択
                {% else %}
                    Choose Item Property
                {% endif %}
            {% elif relation_prop == 'contact_id' %}
                {% if LANGUAGE_CODE == 'ja'%}
                    連絡先プロパティを選択
                {% else %}
                    Choose Contact Property
                {% endif %}
            {% elif relation_prop == 'company_id' %}
                {% if LANGUAGE_CODE == 'ja'%}
                    企業プロパティを選択
                {% else %}
                   Choose Company Property
                {% endif %}
            {% endif %}

        </span>
    </label>

    <input hidden name="property-object-relation-sanka-prop" value="{{relation_prop}}">

    <select name="property-object-relation-key" class="bg-white form-select form-select-solid border h-40px select2-this" 
        {% if LANGUAGE_CODE == 'ja' %}
        data-placeholder="キープロパティを選択"
        {% else %}
        data-placeholder="Select Key Property"
        {% endif %}
        data-allow-clear="true"
        onchange="customSubmit()"
        >
        {% if relation_prop == 'item_id' or relation_prop == associate#{TYPE_OBJECT_ITEM}" %}
            {% for column in columns %}
                <option value="{{column}}" {% if property_object_relation_key_field.item_id == column or property_object_relation_key_field.associate#{{TYPE_OBJECT_ITEM}} == column %}selected{% endif %}>
                    {% if not column|is_uuid %}
                        {{column|display_column_items:request}}
                    {% else %}
                        {% with custom_column=column|search_custom_field_object_items:request %}
                            {{custom_column.name}}
                        {% endwith %} 
                    {% endif %}
                </option>  
            {% endfor %}
        {% elif relation_prop == 'contact_id' %}
            {% for column in columns %}
                <option value="{{column}}" {% if property_object_relation_key_field.contact_id == column %}selected{% endif %}>
                    {% if not column|is_uuid %}
                        {{column|display_column_contacts:request}}
                    {% else %}
                        {% with custom_column=column|search_custom_field_object_contacts:request %}
                            {{custom_column.name}}
                        {% endwith %} 
                    {% endif %}
                </option>  
            {% endfor %}
        {% elif relation_prop == 'company_id' %}
            {% for column in columns %}
                <option value="{{column}}" {% if property_object_relation_key_field.company_id == column %}selected{% endif %}>
                    {% if not column|is_uuid %}
                        {{column|display_column_company:request}}
                    {% else %}
                        {% with custom_column=column|search_custom_field_object_company:request %}
                            {{custom_column.name}}
                        {% endwith %} 
                    {% endif %}
                </option>  
            {% endfor %}
        {% endif %}
                
    </select>
    
</div>	
<script>
    $(document).ready(function() {
        $('.select2-this').select2()

    });
</script>
{% endwith %}
{% endif %}
<script>
    $(document).ready(function() {
        checking_object_relation()

    });
</script>














