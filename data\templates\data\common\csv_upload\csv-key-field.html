{% load i18n %}
{% load hosts %}
{% load custom_tags %}
{% get_current_language as LANGUAGE_CODE %}



{% if 'update' in how_to_import %}
{% with key_field=mapping_storage.key_field|string_list_to_list %}
<div class="fv-rowd-flex flex-column">    
    <label class="{% include 'data/utility/form-label.html' %}">
        <span class="required">

            {% if "contact" in import_data_type %}

                {% if LANGUAGE_CODE == 'ja'%}
                    連絡先キープロパティを選択
                {% else %}
                    Choose Contact Key Property
                {% endif %}
            
            {% elif constant.TYPE_OBJECT_COMPANY in import_data_type %}

                {% if LANGUAGE_CODE == 'ja'%}
                    企業キープロパティを選択
                {% else %}
                    Choose Company Key Property 
                {% endif %}

            {% elif "order" in import_data_type %}

                {% if LANGUAGE_CODE == 'ja'%}
                    受注キープロパティを選択
                {% else %}
                    Choose Order Key Property
                {% endif %}

            {% elif "item" in import_data_type %}

                {% if LANGUAGE_CODE == 'ja'%}
                    商品キープロパティを選択
                {% else %}
                    Choose Item Key Property
                {% endif %}
            
            {% elif constant.TYPE_OBJECT_INVENTORY == import_data_type %}

                {% if LANGUAGE_CODE == 'ja'%}
                    在庫キープロパティを選択
                {% else %}
                    Choose Inventory Key Property
                {% endif %}

            {% elif constant.TYPE_OBJECT_INVENTORY_TRANSACTION == import_data_type %}

                {% if LANGUAGE_CODE == 'ja'%}
                    入出庫キープロパティを選択
                {% else %}
                    Choose Inventory Transaction Key Property
                {% endif %}

            {% elif constant.TYPE_OBJECT_INVENTORY_WAREHOUSE == import_data_type %}

                {% if LANGUAGE_CODE == 'ja'%}
                    ロケーションキープロパティを選択
                {% else %}
                    Choose Location Key Property
                {% endif %}

            {% elif constant.TYPE_OBJECT_CASE in import_data_type %}

                {% if LANGUAGE_CODE == 'ja'%}
                    案件レコードのキープロパティを選択
                {% else %}
                    Choose Case Record Key Property
                {% endif %}

            {% elif constant.TYPE_OBJECT_ESTIMATE in import_data_type %}

                {% if LANGUAGE_CODE == 'ja'%}
                    見積レコードのキープロパティを選択
                {% else %}
                    Choose Estimate Record Key Property
                {% endif %}
            
            {% elif constant.TYPE_OBJECT_INVOICE in import_data_type %}

                {% if LANGUAGE_CODE == 'ja'%}
                    売上請求レコードのキープロパティを選択
                {% else %}
                    Choose Invoice Record Key Property
                {% endif %}

            {% elif constant.TYPE_OBJECT_DELIVERY_NOTE in import_data_type %}

                {% if LANGUAGE_CODE == 'ja'%}
                    納品レコードのキープロパティを選択
                {% else %}
                    Choose Delivery Note Record Key Property
                {% endif %}

            {% elif constant.TYPE_OBJECT_RECEIPT in import_data_type %}

                {% if LANGUAGE_CODE == 'ja'%}
                    入金レコードのキープロパティを選択
                {% else %}
                    Choose Payment Record Key Property
                {% endif %}
            
            {% elif constant.TYPE_OBJECT_SUBSCRIPTION in import_data_type %}

                {% if LANGUAGE_CODE == 'ja'%}
                    サブスクリプションキープロパティを選択
                {% else %}
                    Choose Subscriptions Key Property
                {% endif %}
            
            {% elif "purchaseorder" in import_data_type %}

                {% if LANGUAGE_CODE == 'ja'%}
                    発注キープロパティを選択
                {% else %}
                    Choose Purchase Order Key Property
                {% endif %}

            {% elif constant.TYPE_OBJECT_EXPENSE == import_data_type %}

                {% if LANGUAGE_CODE == 'ja'%}
                    経費キープロパティを選択
                {% else %}
                    Choose Expense Key Property
                {% endif %}
            
            {% elif constant.TYPE_OBJECT_JOURNAL in import_data_type %}

                {% if LANGUAGE_CODE == 'ja'%}
                    ジャーナルエントリキープロパティを選択
                {% else %}
                    Choose Journal Entry Key Property
                {% endif %}
            
            {% elif custom_object %}
                {% if LANGUAGE_CODE == 'ja'%}
                    {{custom_object.slug|object_group_type:request}}プロパティを選択
                {% else %}
                    Choose {{custom_object.slug|object_group_type:request}} Key Property
                {% endif %}
            {% endif %}



        </span>
    </label>


    <select name="key-field" class="bg-white form-select form-select-solid border h-40px select2-this" 
        {% if LANGUAGE_CODE == 'ja'%}
        data-placeholder="キープロパティを選択"
        {% else %}
        data-placeholder="Select Key Property"
        {% endif %}
        data-allow-clear="true"
        onchange="customSubmit(),document.dispatchEvent(new Event('validateMappingProperties'))"
        >
  
        
        {% if "contact" in import_data_type %}
            {% for column in columns %}
                <option value='{{column}}' {% if key_field.0 == column %}selected{% endif %}>
                    {% with args=column|add:'|'|add:'contacts' %} 
                        {% with column_display=args|get_column_display:request %}
                            {{column_display.name}}
                        {% endwith %}
                    {% endwith %}
                </option>   
            {% endfor %} 

        {% elif "company" in import_data_type %}
            {% for column in columns %}
                <option value='{{column}}' {% if key_field.0 == column %}selected{% endif %}>
                    {% with args=column|add:'|'|add:'company' %} 
                        {% with column_display=args|get_column_display:request %}
                            {{column_display.name}}
                        {% endwith %}
                    {% endwith %}
                </option>   
            {% endfor %} 
            
        {% elif "purchaseorder" in import_data_type %}
            {% for column in columns %}
                <option value='{{column}}' {% if key_field.0 == column %}selected{% endif %}>
                    {% if column|startswith:'source_item__' %}
                        {% with custom_column=column|cut:'source_item__'|search_custom_field_object_items:request item_col_display=column|cut:'source_item__'|display_column_items:request %}
                            {% if custom_column %}
                                {{'source_item'|display_column_orders:request}} - {{custom_column.name}}
                            {% else %}
                                {{'source_item'|display_column_orders:request}} - {{item_col_display}}
                            {% endif %}
                        {% endwith %}
                    {% else %}
                        {% with args=column|add:'|'|add:import_data_type %} 
                            {% with column_display=args|get_column_display:request %}
                                {{column_display.name}}
                            {% endwith %}
                        {% endwith %}
                    {% endif %}
                </option>   
            {% endfor %} 
            

        {% elif "order" in import_data_type %}
            {% for column in columns %}
                <option value='{{column}}' {% if key_field.0 == column %}selected{% endif %}>
                    {% with args=column|add:'|'|add:'commerce_orders' %} 
                        {% with column_display=args|get_column_display:request %}
                            {{column_display.name}}
                        {% endwith %}
                    {% endwith %}
                </option>   
            {% endfor %} 


        {% elif "item" in import_data_type %}
            {% for column in columns %}
                <option value='{{column}}' {% if key_field.0 == column %}selected{% endif %}>
                    {% with args=column|add:'|'|add:'commerce_items' %} 
                        {% with column_display=args|get_column_display:request %}
                            {{column_display.name}}
                        {% endwith %}
                    {% endwith %}
                </option>   
            {% endfor %} 
        

        {% elif constant.TYPE_OBJECT_INVENTORY in import_data_type %}
            {% for column in columns %}
                <option value='{{column}}' {% if key_field.0 == column %}selected{% endif %}>
                    {% with args=column|add:'|'|add:'commerce_inventory' %} 
                        {% with column_display=args|get_column_display:request %}
                            {{column_display.name}}
                        {% endwith %}
                    {% endwith %}
                </option>   
            {% endfor %} 
        
        {% elif "commerce_inventory_transaction" in import_data_type %}
            {% for column in columns %}
                <option value='{{column}}' {% if key_field.0 == column %}selected{% endif %}>
                    {% with args=column|add:'|'|add:'commerce_inventory_transaction' %} 
                        {% with column_display=args|get_column_display:request %}
                            {{column_display.name}}
                        {% endwith %}
                    {% endwith %}
                </option>   
            {% endfor %} 
        
        {% elif "commerce_inventory_warehouse" in import_data_type %}
            {% for column in columns %}
                <option value='{{column}}' {% if key_field.0 == column %}selected{% endif %}>
                    {% with args=column|add:'|'|add:'commerce_inventory_warehouse' %} 
                        {% with column_display=args|get_column_display:request %}
                            {{column_display.name}}
                        {% endwith %}
                    {% endwith %}
                </option>   
            {% endfor %} 

        {% elif "production" in import_data_type %}
            {% for column in columns %}
                <option value='{{column}}' {% if key_field.0 == column %}selected{% endif %}>
                    {% with args=column|add:'|'|add:'production' %} 
                        {% with column_display=args|get_column_display:request %}
                            {{column_display.name}}
                        {% endwith %}
                    {% endwith %}
                </option>   
            {% endfor %} 
            
        {% elif "customer_case" in import_data_type %}
            {% for column in columns %}
                <option value='{{column}}' {% if key_field.0 == column %}selected{% endif %}>
                    {% with args=column|add:'|'|add:'customer_case' %} 
                        {% with column_display=args|get_column_display:request %}
                            {{column_display.name}}
                        {% endwith %}
                    {% endwith %}
                </option>   
            {% endfor %}
        
        {% elif custom_object %}
            {% for column in columns %}
                <option value="{{column}}" {% if key_field.0 == column %}selected{% endif %}>
                    {% search_custom_field_object_custom_object custom_object.id column request as custom_column %}
                    {% if custom_column.name %}
                        {% with custom_column=custom_column %}
                            {{custom_column.name}}
                        {% endwith %}
                    {% else %}
                        {% with default_column=column|display_column_custom_objects:request %}
                            {{default_column}}
                        {% endwith %}
                    {% endif %} 
                </option>
            {% endfor %}

        {% else %}
            {% for column in columns %}
            <option value='{{column}}' {% if key_field.0 == column %}selected{% endif %}>
                {% with args=column|add:'|'|add:import_data_type %} 
                    {% with column_display=args|get_column_display:request %}
                        {{column_display.name}}
                    {% endwith %}
                {% endwith %}
            </option>   
            {% endfor %}
        {% endif %}

    
    </select>
    
</div>	
<script>
    $(document).ready(function() {
        $('.select2-this').select2();
    });
    function showFilename(elm){
        if (elm.files.length === 0) return;

        path = elm.value.replace(/^.*[\\\/]/, '')
        elm.parentElement.parentElement.querySelector('.filename').innerHTML = path
        
        if (elm.id === 'csv-input') {
            htmx.trigger('#csv-input', 'csvFileChanged')
        }
    } 
</script>
{% endwith %}
{% else %}
<input hidden name="key-field"></input>
{% endif %}












