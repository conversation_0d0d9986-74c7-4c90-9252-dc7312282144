{% load i18n %}
{% load hosts %}
{% load custom_tags %}
{% get_current_language as LANGUAGE_CODE %}


<script>
    function choose_import_data_type(page_group_type){

        if (page_group_type == "commerce_items"){return "item"}
        else if (page_group_type == "contacts"){return "contact"}
        else if (page_group_type == "company"){return "company"}
        else if (page_group_type == "commerce_orders"){return "order"}
        else if (page_group_type == "commerce_inventory"){return "{{constant.TYPE_OBJECT_INVENTORY}}"}
        else if (page_group_type == "bill"){return "bill"}
        else if (page_group_type == "customer_case"){return "customer_case"}
        else if (page_group_type == "commerce_inventory_transaction"){return "commerce_inventory_transaction"}
        else if (page_group_type == "commerce_inventory_warehouse"){return "commerce_inventory_warehouse"}
        else if (page_group_type == "estimates"){return "estimates"}
        else if (page_group_type == "invoices"){return "invoices"}
        else if (page_group_type == "delivery_slips"){return "delivery_slips"}
        else if (page_group_type == "receipts"){return "receipts"}
        else if (page_group_type == "slips"){return "slips"}
        else if (page_group_type == "commerce_subscription"){return "commerce_subscription"}
        else if (page_group_type == "purchaseorder"){return "purchaseorder"}
        else if (page_group_type == "{{constant.TYPE_OBJECT_EXPENSE}}"){return "{{constant.TYPE_OBJECT_EXPENSE}}"}
        else if (page_group_type == "journal"){return "journal"}
        else if (page_group_type == '{{custom_object.slug}}'){return "{{ custom_object.slug }}"}
        
        return "item"
    }
</script>


<div class="fv-rowd-flex flex-column">
    
    {% comment %} <label class="{% include 'data/utility/form-label.html' %}">
        <span class="required">
            {% if LANGUAGE_CODE == 'ja'%}
            ファイルにはどのようなデータが含まれていますか?
            {% else %}
            What kind of data is in your file?
            {% endif %}
        </span>
    </label>
    
    <div class="fv-rowd-flex flex-column mb-8">
        <div class="mb-2">
            <input class="form-control" type="text" name="import_data_type"
                placeholder="{% if LANGUAGE_CODE == 'ja' %}インポートタイプを選択{% else %}Select Import Type{% endif %}"
                id="import_data_type"

                hx-target="#csvUploadFile"
                hx-get="{% host_url 'csv_upload' host 'app' %}"
                hx-trigger="change"
                hx-vals='{"section": "csv_upload"}'
            /> 

        </div>
    </div> {% endcomment %}

    
    <div class="d-none"
        hx-target="#csvUploadFile"
        hx-get="{% host_url 'csv_upload' host 'app' %}"
        hx-trigger="load"
        hx-vals='js:{"section": "csv_upload","import_data_type":choose_import_data_type("{{page_group_type}}")}'
    >
    </div>

    <input hidden name="import_data_type" data-group-type="{{ page_group_type }}" id="importDataType">
    <script>
        $(document).ready(function() {
            var input = document.getElementById('importDataType');
            var groupType = input.dataset.groupType;
            input.value = choose_import_data_type(groupType);
        });
    </script>
</div>		

<div id="csvUploadFile" class="mb-3"></div>

<div> 
    <a type='button' class="d-none cursor-pointer fs-8 create-property-table-mapping-wizard create-property-elm-order"
    
        hx-get="{% host_url 'new_property' host 'app' %}" 
        hx-target="#create-property-table-mapping-content"
        hx-vals='js:{"page_group_type":"commerce_orders","from-table-mapping":"True"}'
        hx-trigger='click'
    
    ></a>
</div>
<div> 
    <a type='button' class="d-none cursor-pointer fs-8 create-property-table-mapping-wizard create-property-elm-item"
    
        hx-get="{% host_url 'new_property' host 'app' %}" 
        hx-target="#create-property-table-mapping-content"
        hx-vals='js:{"page_group_type":"commerce_items","from-table-mapping":"True"}'
        hx-trigger='click'
    
    ></a>
</div>
<div> 
    <a type='button' class="d-none cursor-pointer fs-8 create-property-table-mapping-wizard create-property-elm-contact"
    
        hx-get="{% host_url 'new_property' host 'app' %}" 
        hx-target="#create-property-table-mapping-content"
        hx-vals='js:{"page_group_type":"contacts","from-table-mapping":"True"}'
        hx-trigger='click'
    
    ></a>
</div>



{% for page_object_type in "company,production,bill,customer_case,commerce_inventory,commerce_inventory_transaction,commerce_inventory_warehouse,estimates,invoices,delivery_slips,receipts,commerce_subscription,purchaseorder,expense,journal"|get_list:',' %}
<div> 
    <a type='button' class="d-none cursor-pointer fs-8 create-property-table-mapping-wizard create-property-elm-{{page_object_type}}"
    
        hx-get="{% host_url 'new_property' host 'app' %}" 
        hx-target="#create-property-table-mapping-content"
        hx-vals='js:{"page_group_type":"{{page_object_type}}","from-table-mapping":"True"}'
        hx-trigger='click'
    
    ></a>
</div>
{% endfor %}

<div> 
    <a type='button' class="d-none cursor-pointer fs-8 create-property-table-mapping-wizard create-property-elm-{{custom_object.slug}}"
    
        hx-get="{% host_url 'new_property' host 'app' %}" 
        hx-target="#create-property-table-mapping-content"
        hx-vals='js:{"page_group_type":"custom_object","from-table-mapping":"True", "custom_object_id": "{{custom_object.id}}"}'
        hx-trigger='click'
    
    ></a>
</div>


{% comment %} TRIGGER to Store Mapping {% endcomment %}
<div> 
    <a type='button' class="d-none cursor-pointer fs-8"
        id="customSubmit"
        hx-post="{% host_url 'variable_stored' host 'app' %}" 
        hx-target="#temp"
        hx-trigger="customSubmit" 
        hx-swap="innerHTML" 
    
    ></a>
</div>
<div id="temp" class="d-none"></div>


<!-- Toast container -->
<div class="toast-container position-fixed top-0 end-0 p-3">
    <div id="successToast" class="toast align-items-center text-white bg-success border-0" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="d-flex">
        <div class="toast-body" id="toastMessage">
            
            <h3>
                {% if LANGUAGE_CODE == 'ja'%}
                マッピングが正常に保存されました
                {% else %}
                Mapping saved successfully
                {% endif %}
            </h3>

        </div>
        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
    </div>
</div>


<script>
    $(document).ready(function() {
        $('.select2-this').select2();
        $('.select2-this').on('select2:select', function (e) {
            var selectElement = $(this).closest('select').get(0);
            selectElement && selectElement.dispatchEvent(new Event('htmx-change'));
        });

    });

    var toastElement = document.getElementById('successToast');
    var toast = new bootstrap.Toast(toastElement, {
    delay: 3000  // Auto-hide after 3 seconds
    });

    // Listen for HTMX events
    document.body.addEventListener('htmx:afterRequest', function(event) {
        if (event.detail.successful) {
          var toastMsg = event.detail.xhr.getResponseHeader('HX-Trigger-Toast');
          if (toastMsg) {
            toast.show();
          }
        }
    });



    {% comment %} var input_item_elem = document.querySelector("#import_data_type");
    var tagify = new Tagify(input_item_elem, {
        whitelist: [
        { "value": "{% if LANGUAGE_CODE == 'ja'%}連絡先{% else %}Contact{% endif %}", "id": "contact" },
        { "value": "{% if LANGUAGE_CODE == 'ja'%}会社{% else %}Company{% endif %}", "id": "company" },
        { "value": "{% if LANGUAGE_CODE == 'ja'%}注文{% else %}Order{% endif %}", "id": "order" },
        { "value": "{% if LANGUAGE_CODE == 'ja'%}商品{% else %}Item{% endif %}", "id": "item" },
        { "value": "{% if LANGUAGE_CODE == 'ja'%}在庫{% else %}Inventory{% endif %}", "id": "inventory" },
        { "value": "{% if LANGUAGE_CODE == 'ja'%}支払請求{% else %}Bill{% endif %}", "id": "billing" },
        { "value": "{% if LANGUAGE_CODE == 'ja'%}案件{% else %}Case{% endif %}", "id": "customer_case" },
        { "value": "{% if LANGUAGE_CODE == 'ja'%}入出庫{% else %}Inventory Transaction{% endif %}", "id": "commerce_inventory_transaction" },
        ],
        dropdown: {
            maxItems: 8,           // <- mixumum allowed rendered suggestions
            classname: "tagify__inline__suggestions", // <- custom classname for this dropdown, so it could be targeted
            enabled: 0,             // <- show suggestions on focus
            closeOnSelect: false    // <- do not hide the suggestions dropdown once an item has been selected
        },
        originalInputValueFormat: valuesArr => valuesArr.map(item => item.id).join(','), // Include item.id in the submitted values
    });

    {% for map_import_data_type in mapping_storage.import_data_type|string_list_to_list %}
        {% if 'contact' in map_import_data_type %}
        tagify.addTags([{ value: "{% if LANGUAGE_CODE == 'ja'%}連絡先{% else %}Contact{% endif %}", id: "contact" }]);
        {% endif %}
        {% if 'company' in map_import_data_type %}
        tagify.addTags([{ value: "{% if LANGUAGE_CODE == 'ja'%}会社{% else %}Company{% endif %}", id: "company" }]);
        {% endif %}
        {% if 'order' in map_import_data_type %}
        tagify.addTags([{ value: "{% if LANGUAGE_CODE == 'ja'%}注文{% else %}Order{% endif %}", id: "order" }]);
        {% endif %}
        {% if 'item' in map_import_data_type %}
        tagify.addTags([{ value: "{% if LANGUAGE_CODE == 'ja'%}商品{% else %}Item{% endif %}", id: "item" }]);
        {% endif %}
        {% if 'inventory' == map_import_data_type %}
        tagify.addTags([{ value: "{% if LANGUAGE_CODE == 'ja'%}在庫{% else %}Inventory{% endif %}", id: "inventory" }]);
        {% endif %}
        {% if 'bill' in map_import_data_type %}
        tagify.addTags([{ value: "{% if LANGUAGE_CODE == 'ja'%}支払請求{% else %}Bill{% endif %}", id: "billing" }]);
        {% endif %}
        {% if 'customer_case' in map_import_data_type %}
        tagify.addTags([{ value: "{% if LANGUAGE_CODE == 'ja'%}案件{% else %}Case{% endif %}", id: "customer_case" }]);
        {% endif %}
        {% if 'commerce_inventory_transaction' == map_import_data_type %}
        tagify.addTags([{ value: "{% if LANGUAGE_CODE == 'ja'%}入出庫{% else %}Inventory Transaction{% endif %}", id: "commerce_inventory_transaction" }]);
        {% endif %}
    {% endfor %} {% endcomment %}
    
    function getArrayElementsByName(name) {
        return Array.from(document.getElementsByName(name));
    }
    function customSubmit(){
        htmx.trigger(`#customSubmit`,'customSubmit')
    }

    function checking_object_relation(){
        setTimeout(function(){
            var sankaSelects = document.querySelectorAll('select[name="sanka-properties"]');
    
            if (!sankaSelects.length) {
                console.error('No sanka properties select elements found');
                return;
            }

            // Initialize arrays to track all selected values
            var allSelectedValues = [];
            
            // Gather all selected values from all selects
            sankaSelects.forEach(select => {
                var selectedValues = Array.from(select.selectedOptions).map(option => option.value);
                allSelectedValues = allSelectedValues.concat(selectedValues);
            });
            
  
            // Check for item_id across all selections
            if (!allSelectedValues.includes('item_id')) {
                const itemElements = document.querySelectorAll('.relation-item_id');
                itemElements.forEach(element => element.remove());
            }
            
            // Check for contact_id across all selections
            if (!allSelectedValues.includes('contact_id')) {
                const contactElements = document.querySelectorAll('.relation-contact_id');
                contactElements.forEach(element => element.remove());
            }
            if (!allSelectedValues.includes('company_id')) {
                const contactElements = document.querySelectorAll('.relation-company_id');
                contactElements.forEach(element => element.remove());
            }

            checkDuplicateRelations();

        }, 200)

        
    }

    function checkDuplicateRelations() {
        setTimeout(function() {
            // Check for duplicate relation-item_id
            const itemIdElements = document.querySelectorAll('.relation-item_id');
            if (itemIdElements.length > 1) {
                // Remove all but the first occurrence
                Array.from(itemIdElements).slice(1).forEach(element => element.remove());
                console.warn('Duplicate relation-item_id found and removed');
            }
    
            // Check for duplicate relation-contact_id
            const contactIdElements = document.querySelectorAll('.relation-contact_id');
            if (contactIdElements.length > 1) {
                Array.from(contactIdElements).slice(1).forEach(element => element.remove());
                console.warn('Duplicate relation-contact_id found and removed');
            }
    
            // Check for duplicate relation-company_id
            const companyIdElements = document.querySelectorAll('.relation-company_id');
            if (companyIdElements.length > 1) {
                Array.from(companyIdElements).slice(1).forEach(element => element.remove());
                console.warn('Duplicate relation-company_id found and removed');
            }
        }, 1200);
    }

   


    function objectRelation(elm){

        var elements = document.querySelectorAll('.select2-this-order');
        elements.forEach(element => {
            if (element.value == 'item_id' || element.value == 'contact_id' || element.value == 'company_id') {
                customSubmit()
                setTimeout(function(){
                    htmx.trigger(element, 'htmx-change');
                }, 500)
            }
        });

        var elements = document.querySelectorAll('.select2-this-inventory');
        elements.forEach(element => {
            if (element.value == 'item_id') {
                customSubmit()
                setTimeout(function(){
                    htmx.trigger(element, 'htmx-change');
                }, 500)
            }
        });

        var elements = document.querySelectorAll('.select2-this-billing');
        elements.forEach(element => {
            if (element.value == 'contact_id' || element.value == 'company_id') {
                customSubmit()
                setTimeout(function(){
                    htmx.trigger(element, 'htmx-change');
                }, 500)
            }
        });

        var elements = document.querySelectorAll('.select2-this-purchaseorder');
        elements.forEach(element => {
            if (element.value == 'item_id') {
                customSubmit()
                setTimeout(function(){
                    htmx.trigger(element, 'htmx-change');
                }, 500)
            }
        });
        
        var elements = document.querySelectorAll('.select2-this-expense');
        elements.forEach(element => {
            if (element.value == 'contact_id' || element.value == 'company_id') {
                customSubmit()
                setTimeout(function(){
                    htmx.trigger(element, 'htmx-change');
                }, 500)
            }
        });
    }

    


</script>







