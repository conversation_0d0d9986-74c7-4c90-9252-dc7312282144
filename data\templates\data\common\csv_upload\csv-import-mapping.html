{% load i18n %}
{% load hosts %}
{% load custom_tags %}
{% get_current_language as LANGUAGE_CODE %}

<style>
    .select2-footer {
        position: sticky;
        bottom: 0;
        left: 0;
        right: 0;
        background: white;
        border-top: 1px solid #e2e8f0;
        z-index: 100;
    }
    
    .select2-dropdown {
        padding-bottom: 0 !important;
    }
    
    /* If you want to add any hover effects or interactivity to the footer */
    .select2-footer:hover {
        background-color: #f8fafc;
    }
</style>

<input hidden name="num-rows" value="{{num_rows}}"></input>
<input hidden name="page_group_type" value="{{page_group_type}}"></input>

<div>
    <span class="fs-5 fw-bolder text-active-primary ms-0 mb-3 py-0 border-0">
        {% if LANGUAGE_CODE == 'ja' %}
        マッピングテーブル
        {% else %}
        Mapping Table
        {% endif %}
    </span>

    <table class="{% include "data/utility/table.html" %} px-5">
        <thead class="{% include "data/utility/table-header.html" %}">
            <tr>
                <th class="min-w-50px">
                    <span>
                        {% if LANGUAGE_CODE == 'ja' %}
                        ファイルヘッダー
                        {% else %}
                        File Header
                        {% endif %}
                    </span>
                </th>
                <th class="min-w-50px">
                    <span>
                        {% if LANGUAGE_CODE == 'ja' %}
                        Sanka オブジェクト
                        {% else %}
                        Sanka Object
                        {% endif %}
                    </span>
                </th>
                <th class="min-w-50px">
                    <span>
                        {% if LANGUAGE_CODE == 'ja' %}
                        Sanka プロパティ
                        {% else %}
                        Sanka Property
                        {% endif %}
                    </span>
                </th>
                <th class="min-w-50px">
                    <span>
                        {% if LANGUAGE_CODE == 'ja' %}
                        除外
                        {% else %}
                        Skip
                        {% endif %}
                    </span>
                </th>
            </tr>
        </thead>

        <tbody class="fs-6">
            {% for header in mapping_data.header_list %}

                {% generate_uuid as uuid %}
                
                <tr>
                    <td style="word-wrap: break-word; overflow-wrap: break-word; white-space: normal; max-width: 300px;">
                        <input name="file-column" type="hidden" value="{{header}}"/>
                        {{header}}
                    </td>

                    <td>
                        <div>
                            <select class="bg-white form-select form-select-solid border h-40px select2-this"
                                    data-control="select2" 
                                    {% if LANGUAGE_CODE == 'ja'%}
                                    data-placeholder="インポートのタイプを選択 "
                                    {% else %}
                                    data-placeholder="Select Type of Import" 
                                    {% endif %}
                                    data-allow-clear="false"
                                    name="import-as"
                                    onchange='showSankaProperties(this)'
                                    id="import##as##{{uuid}}"

                                    hx-target="#sanka-properties-{{uuid}}"
                                    hx-get="{% host_url 'csv_upload' host 'app' %}"
                                    hx-trigger="htmx-change"

                                    hx-vals='{"section": "sanka-field", "header":"{{uuid}}","mapping_storage":"{{mapping_storage.id}}"}'

                                    >
                                    {% with mapping_properties=mapping_storage.input_pair|get_item:header %}
                                        {% if "order" in mapping_data %}
                                        <option value='order' {% if mapping_properties.0 == 'order' %} selected {% endif %}>
                                            {% if LANGUAGE_CODE == 'ja'%}
                                            受注オブジェクト
                                            {% else %}
                                            Order Object
                                            {% endif %}
                                        </option>   
                                        {% endif %}

                                        {% if "item" in mapping_data %}
                                        <option value='item' {% if mapping_properties.0 == 'item' %} selected {% endif %}>
                                            {% if LANGUAGE_CODE == 'ja'%}
                                            商品オブジェクト
                                            {% else %}
                                            Item Object
                                            {% endif %}
                                        </option>  
                                        {% endif %}
                                        
                                        {% if "contact" in mapping_data %}
                                        <option value='contact' {% if mapping_properties.0 == 'contact' %} selected {% endif %}>
                                            {% if LANGUAGE_CODE == 'ja'%}
                                            連絡先オブジェクト
                                            {% else %}
                                            Contact Object
                                            {% endif %}
                                        </option>    
                                        {% endif %}

                                        {% if "company" in mapping_data %}
                                        <option value='company' {% if mapping_properties.0 == 'company' %} selected {% endif %}>
                                            {% if LANGUAGE_CODE == 'ja'%}
                                            企業オブジェクト
                                            {% else %}
                                            Company Object
                                            {% endif %}
                                        </option>  
                                        {% endif %}

                                        {% if constant.TYPE_OBJECT_INVENTORY in mapping_data %}
                                        <option value='{{constant.TYPE_OBJECT_INVENTORY}}' {% if mapping_properties.0 == constant.TYPE_OBJECT_INVENTORY %} selected {% endif %}>
                                            {% if LANGUAGE_CODE == 'ja'%}
                                            在庫オブジェクト
                                            {% else %}
                                            Inventory Object
                                            {% endif %}
                                        </option> 
                                        {% endif %} 

                                        {% if "billing" in mapping_data %}
                                        <option value='billing' {% if mapping_properties.0 == 'billing' %} selected {% endif %}>
                                            {% if LANGUAGE_CODE == 'ja'%}
                                            支払請求オブジェクト
                                            {% else %}
                                            Bill Object
                                            {% endif %}
                                        </option>
                                        {% endif %}

                                        {% if constant.TYPE_OBJECT_CASE in mapping_data %}
                                        <option value='{{constant.TYPE_OBJECT_CASE}}' {% if mapping_properties.0 == constant.TYPE_OBJECT_CASE %} selected {% endif %}>
                                            {% if LANGUAGE_CODE == 'ja'%}
                                            案件オブジェクト
                                            {% else %}
                                            Case Object
                                            {% endif %}
                                        </option>
                                        {% endif %}

                                        {% if constant.TYPE_OBJECT_INVENTORY_TRANSACTION in mapping_data %}
                                        <option value='{{constant.TYPE_OBJECT_INVENTORY_TRANSACTION}}' {% if mapping_properties.0 == constant.TYPE_OBJECT_INVENTORY_TRANSACTION %} selected {% endif %}>
                                            {% if LANGUAGE_CODE == 'ja'%}
                                            入出庫オブジェクト
                                            {% else %}
                                            Inventory Transaction Object
                                            {% endif %}
                                        </option>
                                        {% endif %}

                                        {% if constant.TYPE_OBJECT_INVENTORY_WAREHOUSE in mapping_data %}
                                        <option value='{{constant.TYPE_OBJECT_INVENTORY_WAREHOUSE}}' {% if mapping_properties.0 == constant.TYPE_OBJECT_INVENTORY_WAREHOUSE %} selected {% endif %}>
                                            {% if LANGUAGE_CODE == 'ja'%}
                                            ロケーションオブジェクト
                                            {% else %}
                                            Location Object
                                            {% endif %}
                                        </option>
                                        {% endif %}

                                        {% if constant.TYPE_OBJECT_ESTIMATE in mapping_data %}
                                        <option value='{{constant.TYPE_OBJECT_ESTIMATE}}' {% if mapping_properties.0 == constant.TYPE_OBJECT_ESTIMATE %} selected {% endif %}>
                                            {% if LANGUAGE_CODE == 'ja'%}
                                            見積オブジェクト
                                            {% else %}
                                            Estimate Object
                                            {% endif %}
                                        </option>
                                        {% endif %}

                                        {% if constant.TYPE_OBJECT_INVOICE in mapping_data %}
                                        <option value='{{constant.TYPE_OBJECT_INVOICE}}' {% if mapping_properties.0 == constant.TYPE_OBJECT_INVOICE %} selected {% endif %}>
                                            {% if LANGUAGE_CODE == 'ja'%}
                                            売上請求オブジェクト
                                            {% else %}
                                            Invoice Object
                                            {% endif %}
                                        </option>
                                        {% endif %}

                                        {% if constant.TYPE_OBJECT_DELIVERY_NOTE in mapping_data %}
                                        <option value='{{constant.TYPE_OBJECT_DELIVERY_NOTE}}' {% if mapping_properties.0 == constant.TYPE_OBJECT_DELIVERY_NOTE %} selected {% endif %}>
                                            {% if LANGUAGE_CODE == 'ja'%}
                                            納品オブジェクト
                                            {% else %}
                                            Delivery Note Object
                                            {% endif %}
                                        </option>
                                        {% endif %}

                                        {% if constant.TYPE_OBJECT_RECEIPT in mapping_data %}
                                        <option value='{{constant.TYPE_OBJECT_RECEIPT}}' {% if mapping_properties.0 == constant.TYPE_OBJECT_RECEIPT %} selected {% endif %}>
                                            {% if LANGUAGE_CODE == 'ja'%}
                                            入金オブジェクト
                                            {% else %}
                                            Payment Object
                                            {% endif %}
                                        </option>
                                        {% endif %}

                                        {% if constant.TYPE_OBJECT_SLIP in mapping_data %}
                                        <option value='{{constant.TYPE_OBJECT_SLIP}}' {% if mapping_properties.0 == constant.TYPE_OBJECT_SLIP %} selected {% endif %}>
                                            {% if LANGUAGE_CODE == 'ja'%}
                                            伝票オブジェクト
                                            {% else %}
                                            Slip Object
                                            {% endif %}
                                        </option>
                                        {% endif %}

                                        {% if constant.TYPE_OBJECT_SUBSCRIPTION in mapping_data %}
                                        <option value='{{constant.TYPE_OBJECT_SUBSCRIPTION}}' {% if mapping_properties.0 == constant.TYPE_OBJECT_SUBSCRIPTION %} selected {% endif %}>
                                            {% if LANGUAGE_CODE == 'ja'%}
                                            サブスクリプションオブジェクト
                                            {% else %}
                                            Subscriptions Object
                                            {% endif %}
                                        </option>
                                        {% endif %}

                                        {% if "purchaseorder" in mapping_data %}
                                        <option value='purchaseorder' {% if mapping_properties.0 == 'purchaseorder' %} selected {% endif %}>
                                            {% if LANGUAGE_CODE == 'ja'%}
                                            発注オブジェクト
                                            {% else %}
                                            Purchase Order Object
                                            {% endif %}
                                        </option>
                                        {% endif %}

                                        {% if constant.TYPE_OBJECT_EXPENSE in mapping_data %}
                                        <option value='{{constant.TYPE_OBJECT_EXPENSE}}' {% if mapping_properties.0 == constant.TYPE_OBJECT_EXPENSE %} selected {% endif %}>
                                            {% if LANGUAGE_CODE == 'ja'%}
                                            経費オブジェクト
                                            {% else %}
                                            Expense Object
                                            {% endif %}
                                        </option>
                                        {% endif %}

                                        {% if constant.TYPE_OBJECT_JOURNAL in mapping_data %}
                                        <option value='{{constant.TYPE_OBJECT_JOURNAL}}' {% if mapping_properties.0 == constant.TYPE_OBJECT_JOURNAL %} selected {% endif %}>
                                            {% if LANGUAGE_CODE == 'ja'%}
                                            ジャーナルエントリ
                                            {% else %}
                                            Journal Entry
                                            {% endif %}
                                        </option>
                                        {% endif %}


                                        {% if custom_object %}
                                        <option value='{{custom_object.slug}}' {% if mapping_properties.0 == 'custom_object' %} selected {% endif %}>
                                            {{page_group_type|object_group_type:request}}
                                        </option>
                                        {% endif %}
                                        

                                    {% endwith %}

                            </select>
                        </div>
                        <script>
                            $(document).ready(function() {
                                elm = document.getElementById("import##as##{{uuid}}")
                                showSankaProperties(elm)
                            });
                        </script>
                    </td>

                    <td>
                        {% with mapping_properties=mapping_storage.input_pair|get_item:header %}
                        <div id="sanka-properties-{{uuid}}"
                        
                            hx-target="#sanka-properties-{{uuid}}"
                            hx-get="{% host_url 'csv_upload' host 'app' %}"
                            hx-trigger="load"
                            hx-vals='js:{"section": "sanka-field", "header":"{{uuid}}", "import-as":document.getElementById("import##as##{{uuid}}").value, "mapping_sanka_property_storage":"{{mapping_properties.1}}","mapping_storage":"{{mapping_storage.id}}"}'
                        
                        ></div>   
                        {% endwith %}                  

                    </td>
              
                    <td class="border-bottom-0">
                        {% with mapping_properties=mapping_storage.input_pair|get_item:header %}
                        <input type="checkbox" class="checkbox-ignore form-check-input" 
                            onchange="handleCheckbox(this),customSubmit()" 
                            {% if mapping_properties.2 == "True" %} checked {% endif %}
                            >
                        <input type="hidden" name="ignore" {% if mapping_properties.2 == "True" %} value="True" {% else %} value="False" {% endif %} >
                        {% endwith %}
                    </td>
                </tr>
            {% endfor %} 
        </tbody> 
    </table>


    {% with entry_mode=mapping_storage.entry_method|string_list_to_list %}
    
    {% for import_data_type in import_data_types %}
    <div class="fv-row d-flex flex-column">    
        <label class="{% include 'data/utility/form-label.html' %}">
            <span class="required">
            
                {% if import_data_type == "contact" %}
                    {% if LANGUAGE_CODE == 'ja'%}連絡先レコードのインポート方法を選択{% else %}Choose How to Import Contact Records{% endif %}
                {% elif import_data_type == "company" %}
                    {% if LANGUAGE_CODE == 'ja'%}企業レコードのインポート方法を選択{% else %}Choose How to Import Company Records{% endif %}
                {% elif import_data_type == "order" %}
                    {% if LANGUAGE_CODE == 'ja'%}受注レコードのインポート方法を選択{% else %}Choose How to Import Order Records{% endif %}
                {% elif import_data_type == "item" %}
                    {% if LANGUAGE_CODE == 'ja'%}商品レコードのインポート方法を選択{% else %}Choose How to Import Item Records{% endif %}
                {% elif import_data_type == constant.TYPE_OBJECT_INVENTORY %}
                    {% if LANGUAGE_CODE == 'ja'%}在庫レコードのインポート方法を選択{% else %}Choose How to Import Inventory Records{% endif %}
                {% elif import_data_type == "billing" %}
                    {% if LANGUAGE_CODE == 'ja'%}支払請求レコードのインポート方法を選択{% else %}Choose How to Import Bill Records{% endif %}
                {% elif import_data_type == "customer_case" %}
                    {% if LANGUAGE_CODE == 'ja'%}案件レコードのインポート方法を選択{% else %}Choose How to Import Case Records{% endif %}
                {% elif import_data_type == "commerce_inventory_transaction" %}
                    {% if LANGUAGE_CODE == 'ja'%}入出庫レコードのインポート方法を選択{% else %}Choose How to Import Inventory Transaction Records{% endif %}
                {% elif import_data_type == "commerce_inventory_warehouse" %}
                    {% if LANGUAGE_CODE == 'ja'%}ロケーションレコードのインポート方法を選択{% else %}Choose How to Import Location Records{% endif %}
                {% elif import_data_type == "estimates" %}
                    {% if LANGUAGE_CODE == 'ja'%}見積レコードのインポート方法を選択{% else %}Choose How to Import Estimate Records{% endif %}
                {% elif import_data_type == "invoices" %}
                    {% if LANGUAGE_CODE == 'ja'%}売上請求レコードのインポート方法を選択{% else %}Choose How to Import Invoice Records{% endif %}
                {% elif import_data_type == "delivery_slips" %}
                    {% if LANGUAGE_CODE == 'ja'%}納品レコードのインポート方法を選択{% else %}Choose How to Import Delivery Note Records{% endif %}
                {% elif import_data_type == "receipts" %}
                    {% if LANGUAGE_CODE == 'ja'%}入金レコードのインポート方法を選択{% else %}Choose How to Import Payment Records{% endif %}
                {% elif import_data_type == "slips" %}
                    {% if LANGUAGE_CODE == 'ja'%}伝票レコードのインポート方法を選択する{% else %}Choose How to Import Slip Records{% endif %}
                {% elif import_data_type == "commerce_subscription" %}
                    {% if LANGUAGE_CODE == 'ja'%}サブスクリプションレコードのインポート方法を選択{% else %}Choose How to Import Subscription Records{% endif %}
                {% elif import_data_type == "purchaseorder" %}
                    {% if LANGUAGE_CODE == 'ja'%}発注レコードのインポート方法を選択{% else %}Choose How to Import Purchase Order Records{% endif %}
                {% elif import_data_type == "journal" %}
                    {% if LANGUAGE_CODE == 'ja'%}仕訳レコードのインポート方法を選択する{% else %}Choose How to Import Journal Entry Records{% endif %}
                {% else %}
                    {% if LANGUAGE_CODE == 'ja'%}{{page_group_type|object_group_type:request}}レコードのインポート方法を選択{% else %}Choose How to Import {{page_group_type|object_group_type:request}} Records{% endif %}
                {% endif %}
            
            </span>
        </label>

        <div>
            <select name="how-to-import" class="how-to-import bg-white form-select form-select-solid border h-40px select2-this" 
                {% if LANGUAGE_CODE == 'ja'%}
                data-placeholder="インポートデータの種類を選択"
                {% else %}
                data-placeholder="Select Import Data Type"
                {% endif %}
                data-allow-clear="true"

                hx-target="#choose-key-field-{{import_data_type}}"
                hx-get="{% host_url 'csv_upload' host 'app' %}"
                hx-trigger="load, htmx-change"
                hx-vals='js:{"section": "choose-key-field","import_data_type":"{{import_data_type}}",
                "import-as": getArrayElementsByName("import-as").map(input => input.value),
                "sanka-properties": getArrayElementsByName("sanka-properties").map(input => input.value),
                "mapping_storage":"{{mapping_storage.id}}" }'
                onchange="customSubmit()"
                >

                <option value="create" {% if entry_mode.0 == "create" %}selected{% endif %}>
                    {% if import_data_type == "contact" %}
                        {% if LANGUAGE_CODE == 'ja'%}連絡先レコードの作成{% else %}Create Contact Record{% endif %}
                    {% elif import_data_type == "company" %}
                        {% if LANGUAGE_CODE == 'ja'%}企業レコードの作成{% else %}Create Company Record{% endif %}
                    {% elif import_data_type == "order" %}
                        {% if LANGUAGE_CODE == 'ja'%}受注レコードの作成{% else %}Create Order Record{% endif %}
                    {% elif import_data_type == "item" %}
                        {% if LANGUAGE_CODE == 'ja'%}商品レコードの作成{% else %}Create Item Record{% endif %}
                    {% elif import_data_type == constant.TYPE_OBJECT_INVENTORY %}
                        {% if LANGUAGE_CODE == 'ja'%}在庫レコードの作成{% else %}Create Inventory Record{% endif %}
                    {% elif import_data_type == "billing" %}
                        {% if LANGUAGE_CODE == 'ja'%}支払請求レコードの作成{% else %}Create Bill Record{% endif %}
                    {% elif import_data_type == "customer_case" %}
                        {% if LANGUAGE_CODE == 'ja'%}案件を作成{% else %}Create Case Record{% endif %}
                    {% elif import_data_type == "commerce_inventory_transaction" %}
                        {% if LANGUAGE_CODE == 'ja'%}入出庫レコードの作成{% else %}Create Inventory Transaction Record{% endif %}
                    {% elif import_data_type == "commerce_inventory_warehouse" %}
                        {% if LANGUAGE_CODE == 'ja'%}ロケーションレコードの作成{% else %}Create Location Record{% endif %}
                    {% elif import_data_type == "estimates" %}
                        {% if LANGUAGE_CODE == 'ja'%}見積レコードの作成{% else %}Create Estimate Record{% endif %}
                    {% elif import_data_type == "invoices" %}
                        {% if LANGUAGE_CODE == 'ja'%}売上請求レコードの作成{% else %}Create Invoice Record{% endif %}
                    {% elif import_data_type == "delivery_slips" %}
                        {% if LANGUAGE_CODE == 'ja'%}納品レコードの作成{% else %}Create Delivery Note Record{% endif %}
                    {% elif import_data_type == "receipts" %}
                        {% if LANGUAGE_CODE == 'ja'%}入金レコードの作成{% else %}Create Payment Record{% endif %}
                    {% elif import_data_type == "slips" %}
                        {% if LANGUAGE_CODE == 'ja'%}伝票レコードの作成{% else %}Create Slip Record{% endif %}
                    {% elif import_data_type == "commerce_subscription" %}
                        {% if LANGUAGE_CODE == 'ja'%}サブスクリプションレコードの作成{% else %}Create Subscription Record{% endif %}
                    {% elif import_data_type == "purchaseorder" %}
                        {% if LANGUAGE_CODE == 'ja'%}発注レコードを作成{% else %}Create Purchase Order Records{% endif %}
                    {% elif import_data_type == "journal" %}
                        {% if LANGUAGE_CODE == 'ja'%}仕訳レコードを作成する{% else %}Create Journal Entry Records{% endif %}
                    {% else %}
                        {% if LANGUAGE_CODE == 'ja'%}{{page_group_type|object_group_type:request}}のレコードを作成{% else %}Create {{page_group_type|object_group_type:request}} Record{% endif %}
                    {% endif %}
                </option>    
                <option value="update" {% if entry_mode.0 == "update" %}selected{% endif %}>
                    {% if import_data_type == "contact" %}
                        {% if LANGUAGE_CODE == 'ja'%}連絡先レコードの更新{% else %}Update Contact Records{% endif %}
                    {% elif import_data_type == "company" %}
                        {% if LANGUAGE_CODE == 'ja'%}企業レコードの更新{% else %}Update Company Records{% endif %}
                    {% elif import_data_type == "order" %}
                        {% if LANGUAGE_CODE == 'ja'%}受注レコードの更新{% else %}Update Order Records{% endif %}
                    {% elif import_data_type == "item" %}
                        {% if LANGUAGE_CODE == 'ja'%}商品レコードの更新{% else %}Update Item Records{% endif %}
                    {% elif import_data_type == constant.TYPE_OBJECT_INVENTORY %}
                        {% if LANGUAGE_CODE == 'ja'%}在庫レコードの更新{% else %}Update Inventory Records{% endif %}
                    {% elif import_data_type == "billing" %}
                        {% if LANGUAGE_CODE == 'ja'%}支払請求レコードの更新{% else %}Update Bill Records{% endif %}
                    {% elif import_data_type == "customer_case" %}
                        {% if LANGUAGE_CODE == 'ja'%}案件レコードの更新{% else %}Update Case Records{% endif %}
                    {% elif import_data_type == "commerce_inventory_transaction" %}
                        {% if LANGUAGE_CODE == 'ja'%}入出庫レコードの更新{% else %}Update Inventory Transaction Records{% endif %}
                    {% elif import_data_type == "commerce_inventory_warehouse" %}
                        {% if LANGUAGE_CODE == 'ja'%}ロケーションレコードの更新{% else %}Update Location Records{% endif %}  
                    {% elif import_data_type == "estimates" %}
                        {% if LANGUAGE_CODE == 'ja'%}見積レコードの更新{% else %}Update Estimate Records{% endif %}  
                    {% elif import_data_type == "invoices" %}
                        {% if LANGUAGE_CODE == 'ja'%}請求レコードの更新{% else %}Update Invoice Records{% endif %}
                    {% elif import_data_type == "delivery_slips" %}
                        {% if LANGUAGE_CODE == 'ja'%}納品レコードの更新{% else %}Update Delivery Note Records{% endif %}
                    {% elif import_data_type == "receipts" %}
                        {% if LANGUAGE_CODE == 'ja'%}入金レコードの更新{% else %}Update Payment Records{% endif %}
                    {% elif import_data_type == "slips" %}
                        {% if LANGUAGE_CODE == 'ja'%}伝票記録の更新{% else %}Update Slip Records{% endif %}
                    {% elif import_data_type == "commerce_subscription" %}
                        {% if LANGUAGE_CODE == 'ja'%}サブスクリプションレコードの更新{% else %}Update Subscription Records{% endif %}
                    {% elif import_data_type == "purchaseorder" %}
                        {% if LANGUAGE_CODE == 'ja'%}発注レコードの更新{% else %}Update Purchase Order Records{% endif %}
                    {% elif import_data_type == "journal" %}
                        {% if LANGUAGE_CODE == 'ja'%}仕訳記録の更新{% else %}Update Journal Entry Records{% endif %}
                    {% else %}
                        {% if LANGUAGE_CODE == 'ja'%}{{page_group_type|object_group_type:request}}レコードの更新{% else %}Update {{page_group_type|object_group_type:request}} Records{% endif %}
                    {% endif %}
                </option>    
                <option value="create_and_update" {% if entry_mode.0 == "create_and_update" %}selected{% endif %}>
                    {% if import_data_type == "contact" %}
                        {% if LANGUAGE_CODE == 'ja'%}連絡先レコードの作成と更新{% else %}Create And Update Contact Records{% endif %}
                    {% elif import_data_type == "company" %}
                        {% if LANGUAGE_CODE == 'ja'%}企業レコードの作成と更新{% else %}Create And Update Company Records{% endif %}
                    {% elif import_data_type == "order" %}
                        {% if LANGUAGE_CODE == 'ja'%}受注レコードの作成と更新{% else %}Create And Update Order Records{% endif %}
                    {% elif import_data_type == "item" %}
                        {% if LANGUAGE_CODE == 'ja'%}商品レコードの作成と更新{% else %}Create And Update Item Records{% endif %}
                    {% elif import_data_type == constant.TYPE_OBJECT_INVENTORY %}
                        {% if LANGUAGE_CODE == 'ja'%}在庫レコードの作成と更新{% else %}Create And Update Inventory Records{% endif %}
                    {% elif import_data_type == "billing" %}
                        {% if LANGUAGE_CODE == 'ja'%}支払請求レコードの作成と更新{% else %}Create And Update Bill Records{% endif %}
                    {% elif import_data_type == "customer_case" %}
                        {% if LANGUAGE_CODE == 'ja'%}案件レコードの作成と更新{% else %}Create And Update Case Records{% endif %}
                    {% elif import_data_type == "commerce_inventory_transaction" %}
                        {% if LANGUAGE_CODE == 'ja'%}入出庫レコードの作成と更新{% else %}Create And Update Inventory Transaction Records{% endif %}
                    {% elif import_data_type == "commerce_inventory_warehouse" %}
                        {% if LANGUAGE_CODE == 'ja'%}ロケーションレコードの作成と更新{% else %}Create And Update Location Records{% endif %}    
                    {% elif import_data_type == "estimates" %}
                        {% if LANGUAGE_CODE == 'ja'%}見積レコードの作成と更新{% else %}Create And Update Estimate Records{% endif %} 
                    {% elif import_data_type == "invoices" %}
                        {% if LANGUAGE_CODE == 'ja'%}請求レコードの作成と更新{% else %}Create And Update Invoice Records{% endif %} 
                    {% elif import_data_type == "delivery_slips" %}
                        {% if LANGUAGE_CODE == 'ja'%}納品レコードの作成と更新{% else %}Create And Update Delivery Note Records{% endif %}
                    {% elif import_data_type == "receipts" %}
                        {% if LANGUAGE_CODE == 'ja'%}入金レコードの作成と更新{% else %}Create And Update Payment Records{% endif %}  
                    {% elif import_data_type == "slips" %}
                        {% if LANGUAGE_CODE == 'ja'%}伝票レコードの作成と更新{% else %}Create And Update Slip Records{% endif %}  
                    {% elif import_data_type == "commerce_subscription" %}
                        {% if LANGUAGE_CODE == 'ja'%}サブスクリプションレコードの作成と更新{% else %}Create And Update Subscription Records{% endif %}  
                    {% elif import_data_type == "purchaseorder" %}
                        {% if LANGUAGE_CODE == 'ja'%}発注レコードの作成と更新{% else %}Create And Update Purchase Order Records{% endif %}  
                    {% elif import_data_type == "journal" %}
                        {% if LANGUAGE_CODE == 'ja'%}仕訳レコードの作成と更新{% else %}Create And Update Journal Entry Records{% endif %}  
                    {% else %}
                        {% if LANGUAGE_CODE == 'ja'%}{{page_group_type|object_group_type:request}}の作成と更新{% else %}Create And Update {{page_group_type|object_group_type:request}}{% endif %}
                    {% endif %}
                </option>                       
            
            </select>
        </div>
        
    </div>	
    {% comment %} "sanka-properties": getArrayElementsByName("sanka-properties").map(input => input.value) {% endcomment %}
    <div id="choose-key-field-{{import_data_type}}" class="mt-4 mb-2"></div>
    
    <div id="choose-object-relation-field-{{import_data_type}}" class="mt-4 mb-2"></div>


        {% if import_data_type == "item" %}
            <div class="fv-row d-flex flex-column d-none" id="component-key">    
                <label class="{% include 'data/utility/form-label.html' %}">
                    <span class="required">
                        {% if LANGUAGE_CODE == 'ja'%}構成プロパティキーを選択{% else %}Choose Component Key Property{% endif %}
                    </span>
                </label>

                <div>
                    <select name="component-key" disabled="true" class="bg-white form-select form-select-solid border h-40px select2-this">
                        <option value="item_id">
                            {% with column_display="item_id|commerce_items"|get_column_display:request %}
                                {{column_display.name}}
                            {% endwith %}
                        </option>    
                        {% for text_custom_prop in text_custom_props %}
                            <option value="{{text_custom_prop}}">
                                {% with args=text_custom_prop|stringify|add:'|commerce_items' %} 
                                    {% with column_display=args|get_column_display:request %}
                                        {{column_display.name}}
                                    {% endwith %}
                                {% endwith %}
                            </option>    
                        {% endfor %}
                    </select>
                </div>
                
            </div>		
        {% endif %}
    {% endfor %}
    {% endwith %}

    <div class="d-none tw-text-red-500" id="mapping-error-message"></div>

</div>

<script>
    $(document).ready(function() {
        $('.select2-this').select2();
        $('.select2-this').on('select2:select', function (e) {
            var selectElement = $(this).closest('select').get(0);
            selectElement && selectElement.dispatchEvent(new Event('htmx-change'));
        });
        
    });

    function showSankaProperties(elm) {
        options = {{import_data_types|safe}}
        const header = elm.id.split("##").pop();
        options.forEach(type => {
            const elementId = `sanka-properties-${type}-${header}`;
            const element = document.getElementById(elementId);
            
            if (element) {
                element.classList.toggle('d-none', type !== elm.value);
            }
        });
    }

    function handleCheckbox(checkbox) {
        // Get the parent element of the checkbox
        var parent = checkbox.parentNode;
        // Find the next input element within the parent
        var nextInput = parent.querySelector('input[type="hidden"]');
        if (!checkbox.checked) {
            nextInput.value = "False";
        } else {
            nextInput.value = "True";
        }
    }

    var lastChecked = null;
    $(document).ready(function() {
        // Get all checkboxes
        var checkboxes = document.querySelectorAll('input[type="checkbox"].checkbox-ignore');

        // Add click event listener to each checkbox
        checkboxes.forEach(checkbox => {
            // Store original onchange attribute value
            var originalOnchange = checkbox.getAttribute('onchange');
            
            // We're not removing onchange, just adding a click handler for shift functionality
            checkbox.addEventListener('click', function(event) {
                // Only process shift-clicks here
                if (event.shiftKey && lastChecked !== null) {

                    // Convert NodeList to Array for easier manipulation
                    var checkboxesArray = Array.from(checkboxes);
                    
                    // Find indices of current and last checked checkboxes
                    var startIndex = checkboxesArray.indexOf(lastChecked);
                    var endIndex = checkboxesArray.indexOf(this);
                    
                    // Determine the range (handle both directions - top to bottom or bottom to top)
                    var start = Math.min(startIndex, endIndex);
                    var end = Math.max(startIndex, endIndex);
                    
                    // Set all checkboxes in range to the same state as the current checkbox
                    for (let i = start; i <= end; i++) {
                        var currentCheckbox = checkboxesArray[i];
                        currentCheckbox.checked = this.checked;
                        
                        // Manually trigger the checkbox's onchange handler
                        // This will run handleCheckbox, customSubmit, and objectRelation if needed
                        if (currentCheckbox.onchange) {
                            currentCheckbox.onchange();
                        }
                    }
                    
                    // Call customSubmit just once at the end to avoid multiple form submissions
                    if (typeof customSubmit === 'function') {
                        customSubmit();
                    }
                }
                
                // Update lastChecked to current checkbox
                lastChecked = this;
            });
        });


        {% comment %} Validate properties based on how to import {% endcomment %}
        {% if import_data_types.0 == constant.TYPE_OBJECT_INVENTORY %}
        function validateMappingProperties() {
            console.log('validateMappingProperties true')
            howToImport = document.querySelector("#csvMappingContainer select[name='how-to-import']").value
            
            mappingErrorMessage = document.getElementById('mapping-error-message')
            const nearestForm = findNearestForm(mappingErrorMessage);
            const submitButtons = nearestForm ? nearestForm.querySelectorAll('button[type="submit"]') : null;
            if (howToImport == 'create') {
                // Check if item_id is selected in one of sanka-properties, but not more than two sanka-properties
                sankaProperties = document.getElementsByName('sanka-properties')
                itemPropCount = 0
                for (var i = 0; i < sankaProperties.length; i++) {
                    if (sankaProperties[i].value == 'item_id' || sankaProperties[i].value == 'associate#{{constant.TYPE_OBJECT_ITEM}}') {
                        itemPropCount++
                    }
                }
                if (itemPropCount > 1) {
                    {% if LANGUAGE_CODE == 'ja' %}  
                        mappingErrorMessage.innerHTML = '商品プロパティを複数選択することはできません。'
                    {% else %}
                        mappingErrorMessage.innerHTML = 'Can not select more than one item property.'
                    {% endif %}
                    mappingErrorMessage.classList.remove('d-none')
                    if (submitButtons) {
                        submitButtons.forEach(button => {
                            button.disabled = true
                        })
                    }
                    return false
                } else if (itemPropCount < 1) {
                    {% if LANGUAGE_CODE == 'ja' %}
                    mappingErrorMessage.innerHTML = '在庫レコードをインポートするには、Sanka「商品」プロパティが必要です。「商品」プロパティを一つ選択してください。'
                    {% else %}
                    mappingErrorMessage.innerHTML = 'To import inventory records, Sanka "item" property is required. Please select one "Item" property.'
                    {% endif %}
                    mappingErrorMessage.classList.remove('d-none')
                    if (submitButtons) {
                        submitButtons.forEach(button => {
                            button.disabled = true
                        })
                    }
                    return false
                } else {
                    mappingErrorMessage.classList.add('d-none')
                    if (submitButtons) {
                        submitButtons.forEach(button => {
                            button.disabled = false
                        })
                    }
                    return true
                }
            } else {
                keyField = document.getElementsByName('key-field')
                console.log('key-field', keyField)
                if (!keyField) {
                    mappingErrorMessage.classList.remove('d-none')
                    if (submitButtons) {
                        submitButtons.forEach(button => {
                            button.disabled = true
                        })
                    }
                    return false
                }

                keyField = keyField[0]
                sankaProperties = document.getElementsByName('sanka-properties')
                for (var i = 0; i < sankaProperties.length; i++) {
                    if (sankaProperties[i].value == keyField.value) {
                        mappingErrorMessage.classList.add('d-none')
                        if (submitButtons) {
                            submitButtons.forEach(button => {
                                button.disabled = false
                            })
                        }
                        return true
                    }
                }
                {% if LANGUAGE_CODE == 'ja' %}
                mappingErrorMessage.innerHTML = '選択した在庫キープロパティを選択する必要があります'
                {% else %}
                mappingErrorMessage.innerHTML = 'Require to select choosen inventory key property'
                {% endif %}
                mappingErrorMessage.classList.remove('d-none')
                if (submitButtons) {
                    submitButtons.forEach(button => {
                        button.disabled = true
                    })
                }
                return false
            }
        }
        {% else %}
        function validateMappingProperties() {
            console.log('validateMappingProperties')
            return true
        }
        {% endif %}

        function findNearestForm(element) {
            while (element && element !== document.body) {
                if (element.tagName === 'FORM') {
                    return element;
                }
                element = element.parentElement;
            }
            return null; // Return null if no form is found
        }

        validateMappingProperties()

        document.addEventListener('validateMappingProperties', function() {
            validateMappingProperties();
        });
    });

</script>
